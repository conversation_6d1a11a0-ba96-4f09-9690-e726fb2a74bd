
hpa:
  enabled: false
#  minReplicas: 1
#  maxReplicas: 3
#  metrics:
#  - type: Resource
#    resource:
#      name: cpu
#      target:
#        type: Utilization
#        averageUtilization: 800

ingress:
  className: nginx
  path: "/"
  tls:
    enabled: false
#  canary:
#    weight: 0

livenessProbe:
  enabled: true
  path: "/health/liveness"
readinessProbe:
  enabled: false
  path: "/health/readiness"

#resources:
#  requests:
#   cpu: 100m
#   memory: 128Mi

## Configure extra Volumes
## ref: https://kubernetes.io/docs/concepts/storage/volumes/
#
extraVolumes: &extraVolumes
- name: dotenv-secret-volume
  secret:
    # secretName: {{ .Values.application.secretName }}
- name: storage
  nfs:
    server: 192.168.1.62
    path: /srv/data/resources
    readOnly: false
# - name: config-volume
#   configMap:
#     name: test-config

## Configure extra Volumes mounts
## ref: https://kubernetes.io/docs/concepts/storage/volumes/
#
extraVolumeMounts: &extraVolumeMounts
- name: dotenv-secret-volume
  mountPath: /app/.env
  subPath: DOTENV_FILE
- name: storage
  mountPath: /srv/assets

# - name: config-volume
#   mountPath: /app/config.yaml
#   subPath: config.yaml

extraEnvFrom: []
#- secretRef:
#    name: extra-env.{{ .Values.gitlab.env }}

.worker: &worker
  replicaCount: 1
  command: [ "/cnb/process/schedule" ]
  livenessProbe: &workerLivenessProbe
    command: [ "/bin/sh", "-c", "ps -efww | grep -v grep | grep 'schedule:work'" ]
    initialDelaySeconds: 15
    timeoutSeconds: 15
    probeType: "exec"
  readinessProbe: *workerLivenessProbe
  extraVolumes: *extraVolumes
  extraVolumeMounts: *extraVolumeMounts

workers:
  schedule:
    <<: *worker
    command: [ "/cnb/process/schedule" ]
    livenessProbe: &scheduleLivenessProbe
      <<: *workerLivenessProbe
      command: [ "/bin/sh", "-c", "ps -efww | grep -v grep | grep 'schedule:work'" ]
    readinessProbe: *scheduleLivenessProbe

#  queue:
#    <<: *worker
#    command: [ "/cnb/process/queue" ]
#    livenessProbe: &queueLivenessProbe
#      <<: *workerLivenessProbe
#      command: [ "/bin/sh", "-c", "ps -efww | grep -v grep | grep 'queue:work'" ]
#    readinessProbe: *queueLivenessProbe

#  horizon:
#    <<: *worker
#    command: [ "/cnb/process/horizon" ]
#    livenessProbe: &horizonLivenessProbe
#      <<: *workerLivenessProbe
#      command: [ "/cnb/process/horizon-status" ]
#    readinessProbe: *horizonLivenessProbe
#    lifecycle:
#      preStop:
#        exec:
#          command: [ "/cnb/process/horizon-terminate" ]


