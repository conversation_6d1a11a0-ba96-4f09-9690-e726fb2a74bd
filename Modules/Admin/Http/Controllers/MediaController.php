<?php

namespace Modules\Admin\Http\Controllers;

// use Amscore\Admin\Facades\Admin;
// use Amscore\Admin\Layout\Content;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\File\Repositories\Contracts\MediaRepositoryInterface;

class MediaController extends Controller
{
    protected $mediaRepository;

    public function __construct(MediaRepositoryInterface $media)
    {
        $this->mediaRepository = $media;
    }

    // public function index(Request $request)
    // {
    //     return Admin::content(function (Content $content) use ($request) {
    //         $content->header('Media manager');
    //         $path = $request->get('path', '/');
    //         $view = $request->get('view', 'table');
    //         $manager = new MediaManager($path);
    //         $content->body(view("laravel-admin-media::$view", [
    //             'list' => $manager->ls(),
    //             'nav' => $manager->navigation(),
    //             'url' => $manager->urls(),
    //         ]));
    //     });
    // }

    public function download(Request $request)
    {
        $file = $request->get('file');
        $manager = new MediaManager($file);

        return $manager->download();
    }

    public function upload(Request $request)
    {
        $files = $request->file('files');
        $dir = $request->get('dir', '/') . DIRECTORY_SEPARATOR . date('Y/m/d');
        $manager = new MediaManager($dir);
        try {
            if ($manager->upload($files)) {
                return response()->json([
                    'status' => true,
                    'message' => trans('admin.upload_succeeded'),
                ]);
            }
            // $this->mediaRepository->create([

            // ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => true,
                'message' => $e->getMessage(),
            ]);
        }

        return back();
    }

    public function delete(Request $request)
    {
        $files = $request->get('files');
        $manager = new MediaManager();
        try {
            if ($manager->delete($files)) {
                return response()->json([
                    'status' => true,
                    'message' => trans('admin.delete_succeeded'),
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => true,
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function move(Request $request)
    {
        $path = $request->get('path');
        $new = $request->get('new');
        $manager = new MediaManager($path);
        try {
            if ($manager->move($new)) {
                return response()->json([
                    'status' => true,
                    'message' => trans('admin.move_succeeded'),
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => true,
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function newFolder(Request $request)
    {
        $dir = $request->get('dir');
        $name = $request->get('name');
        $manager = new MediaManager($dir);
        try {
            if ($manager->newFolder($name)) {
                return response()->json([
                    'status' => true,
                    'message' => trans('admin.move_succeeded'),
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => true,
                'message' => $e->getMessage(),
            ]);
        }
    }
}
