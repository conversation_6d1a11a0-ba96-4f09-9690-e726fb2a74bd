<?php

namespace Modules\Meta\Traits;

use Illuminate\Database\Eloquent\Model;
use Modules\Admin\Form\NestedForm;
use Plank\Metable\Metable as BaseMetable;
use Serializable;

trait Metable
{
    use BaseMetable;

    /**
     * Metable item unAttached.
     *
     * @return array
     */
    protected $unAttachedMetableItems = [];

    /**
     * Initialize the trait.
     *
     * @return void
     */
    public static function bootMetable()
    {
        static::deleted(function (self $model) {
            if (!method_exists($model, 'runSoftDelete') || $model->isForceDeleting()) {
                $model->purgeMeta();
            }
        });
    }

    /**
     * Set all meta in model.
     *
     * @return void
     */
    public function setMetaAttribute($values)
    {
        $credentials = $this->getArrayableMetas();

        collect($values)->only(array_keys($credentials))
            ->map(function ($item, $collection) {
                $this->addMetaCollection($item, $collection);
            });
    }

    /**
     * Set force all meta in model.
     *
     * @return void
     */
    public function setForceMetaAttribute($values)
    {
        collect($values)->map(function ($item, $collection) {
            $this->addMetaCollection($item, $collection);
        });
    }

    /**
     * Set meta collection in model.
     *
     * @return void
     */
    public function addMetaCollection($item, $collection = 'default')
    {
        $item = $this->prepare($item);

        if (in_array($collection, $this->getArrayValuesable())) {
            $item = array_values($item);
        }

        $castable = $this->getDefault($collection);

        try {
            $serializable = new $castable($item);

            if ($serializable instanceof Serializable) {
                return $this->setMeta($collection, $serializable);
            }
        } catch (\Throwable) {
        }

        if (!is_null($item)) {
            $this->setMeta($collection, $item);
        } elseif ($this->hasMeta($collection)) {
            $this->removeMeta($collection);
        }
    }

    /**
     * Get all meta group by key meta.
     *
     * @return \Collection
     */
    public function getMetaCollectAttribute()
    {
        return collect($this->meta)
            ->mapWithKeys(fn($item) => [$item->key => $item->value]);
    }

    /**
     * Convert the model's meta attributes to an array.
     *
     * @return array
     */
    public function metasToArray()
    {
        $attributes = [];

        // Here we will grab all of the appended, calculated attributes to this model
        // as these attributes are not really in the attributes array, but are run
        // when we need to array or JSON the model for convenience to the coder.
        foreach ($this->getArrayableMetas() as $key => $value) {
            $attributes[$key] = $this->metaAttribute($key);
        }

        return $attributes;
    }

    /**
     * Get all of the appendable values that are arrayable.
     *
     * @return array
     */
    protected function getArrayableMetas()
    {
        //ensure public property
        return property_exists($this, 'scopeMeta') ? (array) $this->scopeMeta : [];
    }

    /**
     * Register meta.
     *
     * @param  array|string  $attributes
     * @return $this
     */
    public function registerMeta($attributes)
    {
        $this->scopeMeta = array_merge(
            $this->getArrayableMetas(),
            is_string($attributes) ? func_get_args() : $attributes
        );

        return $this;
    }

    /**
     * Get all of the appendable values that are arrayable.
     *
     * @return array
     */
    protected function getArrayValuesable()
    {
        return (array) ($this->arrayValues ?? null);
    }

    /**
     * Get the value of an meta attribute.
     *
     * @param  string  $attribute
     * @return mixed
     */
    protected function metaAttribute($attribute)
    {
        $value = $this->metaCollect->get($attribute);
        $castable = $this->getDefault($attribute);

        if ($value instanceof Serializable) {
            return $value->toArray();
        }

        try {
            $serializable = (new $castable());

            if ($serializable instanceof Serializable) {
                return $serializable->toArray();
            }
        } catch (\Throwable) {
        }

        return $value ?? $castable;
    }

    /**
     * Get the value ddefault if attribute null.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return mixed
     */
    protected function getDefault($attribute)
    {
        return $this->scopeMeta[$attribute] ?? null;
    }

    /**
     * @param string $attribute
     * @return bool
     */
    protected function isPropertyMeta($attribute)
    {
        return (bool) array_key_exists($attribute, $this->getArrayableMetas());
    }

    public function prepareToAttachMeta(Model $meta)
    {
        $this->unAttachedMetableItems[] = compact('meta');
    }

    public function processUnattachedMeta(callable $callable)
    {
        foreach ($this->unAttachedMetableItems as $item) {
            $callable($item['meta']);
        }

        $this->unAttachedMetableItems = [];
    }

    /**
     * Add or update the value of the `Meta` at a given key.
     */
    public function setMeta(string $key, mixed $value): void
    {
        if ($this->hasMeta($key)) {
            $meta = $this->getMetaRecord($key);
            $meta->setAttribute('value', $value);
            $meta->save();
        } else {
            $meta = $this->makeMeta($key, $value);

            if (!$this->exists) {
                $this->prepareToAttachMeta($meta);

                $class = $this::class;

                $class::created(function ($model) {
                    $model->processUnattachedMeta(function (Model $meta) use ($model) {
                        $this->processMetaItem($model, $meta);
                    });
                });

                return;
            }

            $this->processMetaItem($this, $meta);
        }

        // Update cached relationship, if necessary.
        if ($this->relationLoaded('meta')) {
            $this->meta[$key] = $meta;
        }
    }

    public function processMetaItem(Model $model, Model $meta)
    {
        $model->meta()->save($meta);
    }

    /**
     * Clean value meta before create.
     *
     * @return \mixed
     */
    private function prepare($values)
    {
        if (is_string($values)) {
            return trim($values);
        }

        if (is_array($values)) {
            return collect($values)->reject(function ($value, $key) {
                if (is_array($value) && array_key_exists(NestedForm::REMOVE_FLAG_NAME, $value)) {
                    return $value[NestedForm::REMOVE_FLAG_NAME] == 1;
                }

                return false;
            })->map(function ($value) {
                if (is_array($value) && array_key_exists(NestedForm::REMOVE_FLAG_NAME, $value)) {
                    unset($value[NestedForm::REMOVE_FLAG_NAME]);
                }

                return $value;
            })->toArray();
        }

        return $values;
    }
}
