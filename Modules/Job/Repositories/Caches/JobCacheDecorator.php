<?php

namespace Modules\Job\Repositories\Caches;

use Modules\Job\Repositories\Contracts\JobRepositoryInterface;
use Redmix0901\Core\Repositories\Caches\BaseCacheDecorator;
use Redmix0901\Core\Repositories\Interfaces\BaseRepositoryInterface;

class JobCacheDecorator extends BaseCacheDecorator implements JobRepositoryInterface
{
    protected static $cacheable = true;

    public function __construct(BaseRepositoryInterface $base)
    {
        $this->cache_time = 60;

        // Invoke parent
        parent::__construct($base);
    }

    /**
     * @inheritdoc
     */
    public function searchOnEloquent($keyword = null, $params = [], $fields = '*')
    {
        return $this->getWithCache(__FUNCTION__, func_get_args());
    }

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = '*', $params = [], $fields = '*')
    {
        if (static::$cacheable) {
            return $this->getWithCache(__FUNCTION__, func_get_args());
        }

        return $this->getWithoutCache(__FUNCTION__, func_get_args());
    }

    /**
     * Temporarily disable search syncing for the given callback.
     *
     * @param  callable  $callback
     * @return mixed
     */
    public function withoutCache($callback)
    {
        static::disableSearchCaching();

        try {
            return $callback();
        } finally {
            static::enableSearchCaching();
        }
    }

    /**
     * @inheritdoc
     */
    public function encryptRecommendJobs($email, array $job_ids, $event)
    {
        return $this->getWithCache(__FUNCTION__, func_get_args());
    }

    /**
     * @inheritdoc
     */
    public function decryptRecommendJobs($token)
    {
        return $this->getWithCache(__FUNCTION__, func_get_args());
    }

    public static function enableSearchCaching()
    {
        static::$cacheable = true;
    }

    public static function disableSearchCaching()
    {
        static::$cacheable = false;
    }

    public function availableLocations()
    {
        return $this->getWithCache(__FUNCTION__, func_get_args());
    }
}
