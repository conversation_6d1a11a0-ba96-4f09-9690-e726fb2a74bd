<?php

namespace Modules\Job\Repositories\Eloquents;

use Carbon\Carbon;
use Exception;
use Modules\Job\Repositories\Contracts\CandidateRepositoryInterface;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\DateHistogramAggregation;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\FilterAggregation;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\TermsAggregation;
use ONGR\ElasticsearchDSL\Aggregation\Metric\CardinalityAggregation;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MatchQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MultiMatchQuery;
use ONGR\ElasticsearchDSL\Query\MatchAllQuery;
use ONGR\ElasticsearchDSL\Query\Specialized\ScriptQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\ExistsQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\IdsQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\RangeQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermsQuery;
use ONGR\ElasticsearchDSL\Sort\FieldSort;
use Redmix0901\Core\Repositories\Eloquent\BaseRepository;

class CandidateEloquentRepository extends BaseRepository implements CandidateRepositoryInterface
{
    private $arrPeriod = [
        'daily' => 'day',
        'monthly' => 'month',
        'yearly' => 'year',
        'weekly' => 'week',
    ];

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = null, $params = [], $fields = '*')
    {
        \Log::info('Search candidate form Elasticsearch');

        return $this->querySearch($keyword, $params, $fields)->raw();
    }

    public function searchOnEloquent($keyword = null, $params = [], $fields = '*')
    {
        return $this->querySearch($keyword, $params, $fields)->get();
    }

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearchStatistics($keyword = null, $params = [], $fields = '*')
    {
        return $this->querySearchStatistics($keyword, $params, $fields)->raw();
    }

    /**
     * @inheritdoc
     */
    private function querySearch($keyword, $params, $fields)
    {
        return $this->model->search('*', function ($client, $body) use ($keyword, $params) {
            // Pagination
            $size = $params['page_size'] ?? 10;
            $from = isset($params['page']) ? $size * ($params['page'] - 1) : 0;

            $baseQuery = new BoolQuery();

            // get by id candidate
            if ($this->hasQuery($params, 'candidate_id')) {
                $baseQuery->add(new TermQuery('_id', $params['candidate_id']), BoolQuery::MUST);
            }

            // get by cvbuilder_id
            if ($this->hasQuery($params, 'cvbuilder_id')) {
                $baseQuery->add(new TermQuery('cvbuilder_id', $params['cvbuilder_id']), BoolQuery::MUST);
            }

            // get by cvbuilder_ids
            if ($this->hasQuery($params, 'cvbuilder_ids')) {
                $baseQuery->add(new TermsQuery('cvbuilder_id', explode(',', (string) $params['cvbuilder_ids'])), BoolQuery::MUST);
            }

            //get by resume_id
            if ($this->hasQuery($params, 'resume_id')) {
                $baseQuery->add(new TermQuery('resume.id', $params['resume_id']), BoolQuery::MUST);
            }

            //get by resume_id
            if ($this->hasQuery($params, 'resume_ids')) {
                $baseQuery->add(new TermsQuery('resume.id', $params['resume_ids']), BoolQuery::MUST);
            }

            //get by media_id
            if ($this->hasQuery($params, 'media_id')) {
                $baseQuery->add(new TermQuery('media_id', $params['media_id']), BoolQuery::MUST);
            }

            //get by media_ids
            if ($this->hasQuery($params, 'media_ids')) {
                $baseQuery->add(new TermsQuery('media_id', explode(',', (string) $params['media_ids'])), BoolQuery::MUST);
            }

            /*
             * Tìm kiếm keyword các trường title và tên của employer
             */
            if (!empty($keyword)) {
                $baseQuery->add(new MultiMatchQuery(
                    ['resume.full_name', 'resume.email', 'resume.phone', 'job.title', 'resume.id'],
                    $keyword,
                    ['operator' => 'or']
                ), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'owner')) {
                $baseQuery->add(new TermsQuery('job.company_id', $params['owner']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'status')) {
                $baseQuery->add(new TermsQuery('status', $params['status']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'job')) {
                $baseQuery->add(new TermsQuery('job.id', explode(',', (string) $params['job'])), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'level')) {
                $baseQuery->add(new TermsQuery('job.level', explode(',', (string) $params['level'])), BoolQuery::MUST);
            }

            /*
             * Tìm kiếm keyword trên cột full_address
             */
            if (isset($params['full_address'])) {
                $baseQuery->add(new MultiMatchQuery(
                    ['resume.addresses.full_address'],
                    $params['full_address'],
                    ['operator' => 'or']
                ), BoolQuery::MUST);
            }

            if (array_key_exists('ids', $params)) {
                $baseQuery->add(new IdsQuery(empty($params['ids']) ? [] : explode(',', (string) $params['ids'])), BoolQuery::MUST);
            }

            //-----------
            $queryRange = [
                'first_applied_to' => true,
                'first_applied_from' => true,
                'applied_to' => true,
                'applied_from' => true,
            ];
            $queryRange = array_keys(array_intersect_key($queryRange, $params));
            if (!empty($queryRange)) {
                $fromRangeValue = array_values(preg_grep('/[a-z]*applied_from/i', $queryRange));
                $toRangeValue = array_values(preg_grep('/[a-z]*applied_to/i', $queryRange));
                $fromRange = (count($fromRangeValue) == 2) ? (Carbon::parse($params[$fromRangeValue[0]])->gt(Carbon::parse($params[$fromRangeValue[0]])) ? $params[$fromRangeValue[0]] : $params[$fromRangeValue[1]]) : (isset($fromRangeValue[0]) ? $params[$fromRangeValue[0]] : null);
                $toRange = (count($toRangeValue) == 2) ? (Carbon::parse($params[$toRangeValue[0]])->gt(Carbon::parse($params[$toRangeValue[0]])) ? $params[$toRangeValue[0]] : $params[$toRangeValue[1]]) : (isset($toRangeValue[0]) ? $params[$toRangeValue[0]] : null);
                $createdRange = new RangeQuery(
                    'created_at',
                    array_filter(
                        [
                            'gte' => $fromRange ?? null,
                            'lte' => $toRange ?? null,
                            'format' => 'yyyy-MM-dd',
                        ]
                    )
                );

                if (isset($params['not_in_applied_range'])) {
                    $baseQuery->add($createdRange, BoolQuery::MUST_NOT);
                } else {
                    $baseQuery->add($createdRange, BoolQuery::MUST);
                }
            }

            //-----------
            if (!isset($params['not_in_applied_range']) && $this->hasQuery($params, 'hackerrank_tags')) {
                $baseQuery->add(new TermsQuery('tags_hackerrank.keyword', explode(',', (string) $params['hackerrank_tags'])), BoolQuery::MUST);
            }

            if (!isset($params['not_in_applied_range']) && $this->hasQuery($params, 'has_offer')) {
                if (array_key_exists('is_group', $params)) {
                    $baseQuery->add(new MatchQuery('has_offer', 1), BoolQuery::MUST);
                }
            }

            $body->addSort(new FieldSort('created_at', null, ['order' => FieldSort::DESC]));
            //------------

            if (empty($keyword) && empty($params)) {
                $baseQuery = new MatchAllQuery();
            }

            $body->setSize($size);
            $body->setFrom($from);
            $body->addQuery($baseQuery);
            $body->addAggregation(new TermsAggregation('status', 'status'));

            return $client->search(['index' => $this->model->searchableAs(), 'body' => $body->toArray()])->asArray();
        })
            ->query(function ($builder) {
                //$builder->with('tags', 'salary', 'company', 'author', 'addresses');
            });
    }

    private function hasQuery($query, $key)
    {
        return (bool) (isset($query[$key]) && !is_null($query[$key]));
    }

    public function candidateBarChartData($period, $fromDate, $toDate, $labelFormat)
    {
        try {
            $candidateList = $this->advancedGet([
                'condition' => [
                    'start_date' => ['created_at', '>=', $fromDate->format('Y-m-d 00:00:00')],
                    'end_date' => ['created_at', '<=', $toDate->format('Y-m-d 23:59:59')],
                ],
                'select' => ['id', 'resume_id', 'created_at'],
            ]);

            $timeList = [];
            $candidateData = [];
            $userApplyData = [];

            while ($fromDate->lessThanOrEqualTo($toDate)) {
                $key = $fromDate->format($labelFormat);

                if ($period != config('constant.CHART.PERIOD.daily')) {
                    if (!$fromDate->equalTo($toDate)) {
                        $endDate = $this->jumpDate($period, $fromDate);

                        if ($endDate->greaterThan($toDate)) {
                            $key .= ' - ' . $toDate->format($labelFormat);
                        } else {
                            $key .= ' - ' . $endDate->format($labelFormat);
                        }
                    } else {
                        $endDate = $toDate;
                    }
                } else {
                    $endDate = $fromDate->clone();
                }

                $newCandidateList = $candidateList->whereBetween('created_at', [$fromDate->format('Y-m-d 00:00:00'), $endDate->format('Y-m-d 23:59:59')]);
                $candidateList = $candidateList->whereNotBetween('created_at', [$fromDate->format('Y-m-d 00:00:00'), $endDate->format('Y-m-d 23:59:59')]);

                $timeList[] = $key;
                $candidateData[] = $newCandidateList->count();
                $userApplyData[] = $newCandidateList->unique('resume_id')->count();

                $fromDate = $endDate->clone()->addDay();
            }

            return [
                'success' => true,
                'labels' => $timeList,
                'candidates' => $candidateData,
                'user_apply' => $userApplyData,
            ];
        } catch (Exception) {
            return [
                'success' => false,
            ];
        }
    }

    private function jumpDate($period, $date)
    {
        return match ($period) {
            config('constant.CHART.PERIOD.daily') => $date->clone()->addDay(),
            config('constant.CHART.PERIOD.monthly') => $date->clone()->lastOfMonth(),
            config('constant.CHART.PERIOD.yearly') => $date->clone()->lastOfYear(),
            default => $date->clone()->nextWeekendDay()->addDay(),
        };
    }

    private function querySearchStatistics($keyword, $params, $fields)
    {
        $statusArray = $this->model->getStatusDescription();

        return $this->model->search('*', function ($client, $body) use ($keyword, $params, $statusArray) {
            // Pagination
            $size = $params['page_size'] ?? 10;
            $from = isset($params['page']) ? $size * ($params['page'] - 1) : 0;

            $baseQuery = new BoolQuery();

            if ($this->hasQuery($params, 'status')) {
                $status = explode(',', (string) $params['status']);
                $status = collect($status)->map(function ($value, $key) use ($statusArray) {
                    if (array_search(ucwords($value), $statusArray)) {
                        return array_search(ucwords($value), $statusArray);
                    }
                })->all();
                $baseQuery->add(new TermsQuery('status', $status), BoolQuery::MUST);
            }

            if (empty($keyword) && empty($params)) {
                $baseQuery = new MatchAllQuery();
            }

            //query by period
            // dd($params['created_at']);
            if ($this->hasQuery($params, 'created_at')) {
                $dateRangeAggregation = new RangeQuery(
                    'created_at',
                    [
                        'gte' => $params['created_at']['from'],
                        'lte' => $params['created_at']['to'],
                        'format' => 'yyyy-MM-dd HH:mm:ss',
                    ]
                );

                $body->addQuery($dateRangeAggregation);
            }

            $body->setSize($size);
            $body->setFrom($from);
            $body->addQuery($baseQuery);

            // $body->addAggregation(new TermsAggregation('status', 'status'));

            $dateHist = new DateHistogramAggregation('statis');
            $dateHist->setField('created_at');
            if ($this->hasQuery($params, 'period')) {
                if (array_key_exists($params['period'], $this->arrPeriod)) {
                    $dateHist->setInterval($this->arrPeriod[$params['period']]);
                }
            } else {
                $dateHist->setInterval('month');
            }

            //Apply apply popup to all
            $aggregationGroup = new FilterAggregation('group');
            $aggregationGroup->setFilter(new ExistsQuery('group'));

            //count user
            $cardinalityAggregation = new CardinalityAggregation('resume_count');
            $cardinalityAggregation->setField('resume.id');

            //count user apply popup to all
            $aggregationGroup->addAggregation($cardinalityAggregation);
            $dateHist->addAggregation($aggregationGroup);

            //Số apply dùng file upload
            $aggregationMedia = new FilterAggregation('media_id');
            $aggregationMedia->setFilter(new ExistsQuery('media_id'));
            $dateHist->addAggregation($aggregationMedia);

            //Số apply dùng file cvbuilder
            $aggregationMediaCvbuilder = new FilterAggregation('cvbuilder_id');
            $aggregationMediaCvbuilder->setFilter(new ExistsQuery('cvbuilder_id'));
            $aggregationMediaCvbuilder->addAggregation($cardinalityAggregation);

            //count apply source type
            $source = new TermsAggregation('source_type', 'source');
            $source->addParameter('size', 10000);
            $source->addAggregation($cardinalityAggregation);
            $aggregationMediaCvbuilder->addAggregation($source);

            $dateHist->addAggregation($aggregationMediaCvbuilder);

            //count candidate has source is MobileApp
            $aggregationSourceMobileApp = new FilterAggregation('source_MobileApp');
            $aggregationSourceMobileApp->setFilter(new TermsQuery('device_apply', ['Android', 'iOS']));
            $dateHist->addAggregation($aggregationSourceMobileApp);

            $aggregationSourceAndroid = new FilterAggregation('source_android');
            $aggregationSourceAndroid->setFilter(new MatchQuery('device_apply', 'Android'));
            $dateHist->addAggregation($aggregationSourceAndroid);

            $aggregationSourceIos = new FilterAggregation('source_ios');
            $aggregationSourceIos->setFilter(new MatchQuery('device_apply', 'iOS'));
            $dateHist->addAggregation($aggregationSourceIos);

            $aggregationSourcePC = new FilterAggregation('source_pc');
            $aggregationSourcePC->setFilter(new MatchQuery('device_apply', 'PC'));
            $dateHist->addAggregation($aggregationSourcePC);

            $aggregationSourceMobileWeb = new FilterAggregation('source_mobile');
            $aggregationSourceMobileWeb->setFilter(new MatchQuery('device_apply', 'MobileWeb'));
            $dateHist->addAggregation($aggregationSourceMobileWeb);

            //count all resume
            $dateHist->addAggregation($cardinalityAggregation);

            //count sent candidate at:
            $aggregationSentEmployer = new FilterAggregation('count_sent_employer');
            $srciptCountSentEmployer = new ScriptQuery(
                "
                    def check = doc['sent_employer_at.keyword'];

                    if(check.size() > 0){
                        check = check[0];
                        def input = doc['created_at'].value;
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern('yyyy-MM-dd');
                        def inputFormat = input.format(formatter);
                        return check.contains(inputFormat);
                    }

                    return false;
                "
            );
            $aggregationSentEmployer->setFilter($srciptCountSentEmployer);
            $dateHist->addAggregation($aggregationSentEmployer);

            $body->addAggregation($dateHist);

            $body->addSort(new FieldSort('created_at', null, ['order' => FieldSort::DESC]));

            return $client->search(['index' => $this->model->searchableAs(), 'body' => $body->toArray()])->asArray();
        })
            ->query(function ($builder) {
            });
    }

    private function hasRangeQuery($query, $from, $to)
    {
        return array_filter(
            [
                'gte' => $query[$from] ?? null,
                'lte' => $query[$to] ?? null,
            ]
        );
    }

    private function hasOrderBy($params, $key)
    {
        return isset($params['ordering'])
            && in_array($key, explode(',', (string) $params['ordering']));
    }
}
