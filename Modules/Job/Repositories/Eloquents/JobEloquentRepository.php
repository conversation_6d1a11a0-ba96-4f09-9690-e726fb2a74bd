<?php

namespace Modules\Job\Repositories\Eloquents;

use Elastic\Elasticsearch\Client;
use Illuminate\Contracts\Encryption\DecryptException;
use Modules\Company\Entities\Company;
use Modules\Company\Repositories\Contracts\CompanyRepositoryInterface;
use Modules\Job\Entities\Job;
use Modules\Job\Repositories\Contracts\CandidateRepositoryInterface;
use Modules\Job\Repositories\Contracts\JobRepositoryInterface;
use Modules\Taxonomy\Entities\Taxonomy;
use Modules\User\Repositories\Contracts\UserRepositoryInterface;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\TermsAggregation;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\Compound\FunctionScoreQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MultiMatchQuery;
use ONGR\ElasticsearchDSL\Query\MatchAllQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\IdsQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\RangeQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermsQuery;
use ONGR\ElasticsearchDSL\Search;
use ONGR\ElasticsearchDSL\Sort\FieldSort;
use Redmix0901\Core\Repositories\Eloquent\BaseRepository;

class JobEloquentRepository extends BaseRepository implements JobRepositoryInterface
{
    private array $regionOther = [
        '01', '79', '48',
    ];

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = null, $params = [], $fields = '*')
    {
        return $this->querySearch($keyword, $params, $fields)->raw();
    }

    /**
     * @inheritdoc
     */
    public function searchOnEloquent($keyword = null, $params = [], $fields = '*')
    {
        if (isset($params['page_size'])) {
            return $this->querySearch($keyword, $params, $fields)->paginate($params['page_size']);
        }

        return $this->querySearch($keyword, $params, $fields)->get();
    }

    /**
     * @param string $keyword
     * @param string $params['full_address'] = 'duong 3/2'
     * @param string $params['company'] = 1,2,3
     * @param string $params['level'] = paid,free,crawl
     * @param string $params['ids'] = 1,2,3
     * @param string $params['except_ids'] = 1,2,3
     * @param string $params['region_ids'] = 1,2,3
     * @param string $params['features']
     * @param string $params['skills_ids'] = 1,2,3
     * @param array $params['required_skills_arr'] = ["PHP", "Java"]
     * @param array $params['optional_skills_arr'] = ["PHP", "Java"]
     */
    private function querySearch($keyword, $params, $fields)
    {
        $client = $this->model->search('*', function (Client $client, Search $body) use ($keyword, $params) {
            /*
            |--------------------------------------------------------------------------
            | Query
            |--------------------------------------------------------------------------
            |
            */
            $baseQuery = new BoolQuery();

            /*
             * Tìm kiếm keyword các trường title và tên của employer
             */
            if (!empty($keyword)) {
                if (in_array($keyword, \arrayHackerRank())) {
                    $baseQuery->add(new TermsQuery('features', ['hackerrank']), BoolQuery::MUST);
                } else {
                    $baseQuery->add(new MultiMatchQuery(
                        [
                            'title',
                            'title_slug',
                            'skills_str',
                            'skills_arr',
                            'company.display_name',
                            'company.display_name_slug',
                            'title.synonym^2',
                            'title_slug.synonym^2',
                            'skills_str.synonym^2',
                            'skills_arr.synonym^2',
                        ],
                        $keyword,
                        ['operator' => 'and']
                    ), BoolQuery::MUST);
                }
            }

            //SEARCH ONLY SKILL
            if ($this->hasQuery($params, 'sk')) {
                $displayname = str_replace('+', ' ', $params['sk']);
                $baseQuery->add(new MultiMatchQuery(
                    [
                        'skills_str',
                        'skills_arr',
                        'skills_str.synonym^2',
                        'skills_arr.synonym^2',
                    ],
                    $displayname,
                    ['operator' => 'or']
                ), BoolQuery::MUST);
            }

            //SEARCH keyword DASH EMPLOYER
            if ($this->hasQuery($params, 'q')) {
                $baseQuery->add(new MultiMatchQuery(
                    [
                        'id',
                        'title',
                        'title_slug',
                        'skills_str',
                        'title.synonym^2',
                        'title_slug.synonym^2',
                        'skills_str.synonym^2',
                    ],
                    $params['q'],
                    ['operator' => 'or']
                ), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'skills_str')) {
                $baseQuery->add(new MultiMatchQuery(
                    [
                        'title',
                        'title_slug',
                        'skills_str',
                        'title.synonym^2',
                        'title_slug.synonym^2',
                        'skills_str.synonym^2',
                    ],
                    $params['skills_str'],
                    ['operator' => 'and']
                ), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'skills_arr')) {
                foreach ($params['skills_arr'] as $skill) {
                    $baseQuery->add(new MultiMatchQuery(
                        [
                            'title',
                            'title_slug',
                            'skills_str',
                            'skills_arr',
                            'title.synonym^2',
                            'title_slug.synonym^2',
                            'skills_str.synonym^2',
                            'skills_arr.synonym^2',
                        ],
                        $skill,
                        ['operator' => 'and']
                    ), BoolQuery::MUST);
                }
            }

            /*
             * Tìm kiếm keyword trên cột full_address
             */
            if ($this->hasQuery($params, 'full_address')) {
                $baseQuery->add(new MultiMatchQuery(
                    ['addresses.full_addresses', 'addresses.sort_addresses'],
                    $params['full_address'],
                    ['operator' => 'or']
                ), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'industries_ids')) {
                $companyByIndustry = app(CompanyRepositoryInterface::class)->searchOnElasticsearch(
                    '',
                    [
                        'industries_ids' => $params['industries_ids'],
                        'page_size' => 1000,
                        'status' => Company::STATUS_ACTIVE,
                    ]
                );

                $companyIds = collect($companyByIndustry['hits']['hits'])->pluck('_id');
                if ($companyIds) {
                    $baseQuery->add(new TermsQuery('owned_id', $companyIds), BoolQuery::MUST);
                }
            }

            if ($this->hasQuery($params, 'company')) {
                // $baseQuery->add(new TermsQuery('company.id', explode(',', $params['company'])), BoolQuery::MUST);
                $baseQuery->add(new TermsQuery('owned_id', explode(',', $params['company'])), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'region_ids')) {
                if ($params['region_ids'] == 'other') {
                    $baseQuery->add(new TermsQuery('addresses.address_region_ids', $this->regionOther), BoolQuery::MUST_NOT);
                } elseif ($params['region_ids'] != 'all') {
                    $baseQuery->add(new TermsQuery('addresses.address_region_ids', $this->termsQuery($params['region_ids'])), BoolQuery::MUST);
                }
            }

            if ($this->hasQuery($params, 'skills_id')) {
                $baseQuery->add(new TermsQuery('skills_ids', $this->termsQuery($params['skills_id'], true)), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'skills_id_or')) {
                $arrSkillId = explode(',', $params['skills_id_or']);
                $arrSkills = $this->sortArraySkill($arrSkillId);
                $skillIds = new BoolQuery();
                $baseQueryScoreWeight = new FunctionScoreQuery($baseQuery);
                $countSkill = count($arrSkills);
                foreach (array_filter($arrSkills) as $skill) {
                    $skillIds->add(new TermQuery('skills_ids', $skill), BoolQuery::SHOULD);
                    $scoreQuery = new TermQuery('skills_ids', $skill);
                    $baseQueryScoreWeight->addWeightFunction($countSkill, $scoreQuery);
                    $countSkill--;
                }
                $baseQuery->add($skillIds, BoolQuery::MUST);
                $body->addQuery($baseQueryScoreWeight);
            }

            if ($this->hasQuery($params, 'required_skills_arr') && $this->hasQuery($params, 'optional_skills_arr')) {
                $requiredSkills = $params['required_skills_arr'] ?? [];
                $optionalSkills = $params['optional_skills_arr'] ?? [];
                $bestMatch = new BoolQuery();
                $bestMatchScoreWeight = new FunctionScoreQuery(new BoolQuery());
                $bestMatchScoreWeight->setParameters(['score_mode' => 'sum']);
                foreach ($requiredSkills as $skill) {
                    $scoreQuery = new TermQuery('required_skills_arr', $skill);
                    $bestMatchScoreWeight->addWeightFunction(round(Job::REQUIRED_SKILL_WEIGHT, 2), $scoreQuery);
                }
                foreach ($optionalSkills as $skill) {
                    $scoreQuery = new TermQuery('optional_skills_arr', $skill);
                    $bestMatchScoreWeight->addWeightFunction(1, $scoreQuery);
                }
                $bestMatch->add(new TermsQuery('required_skills_arr', $requiredSkills), BoolQuery::SHOULD);
                $bestMatch->add(new TermsQuery('optional_skills_arr', $optionalSkills), BoolQuery::SHOULD);
                $baseQuery->add($bestMatch, BoolQuery::MUST);
                $body->addQuery($bestMatchScoreWeight);
            }

            if ($this->hasQuery($params, 'level')) {
                $baseQuery->add(new TermsQuery('level', $this->termsQuery($params['level'])), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'job_levels_ids')) {
                $baseQuery->add(new TermsQuery('job_levels_ids', $this->termsQuery($params['job_levels_ids'])), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'job_levels_ids_or')) {
                $skillOrBoolQuery = new BoolQuery();
                foreach (array_filter(explode(',', $params['job_levels_ids_or'])) as $skill) {
                    $skillOrBoolQuery->add(new TermQuery('job_levels_ids', $skill), BoolQuery::SHOULD);
                }
                $baseQuery->add($skillOrBoolQuery, BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'experiences_id')) {
                $baseQuery->add(new TermsQuery('experiences_ids', $this->termsQuery($params['experiences_id'])), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'experiences_ids_or')) {
                $experienceOrBoolQuery = new BoolQuery();
                foreach (array_filter(explode(',', $params['experiences_ids_or'])) as $exp) {
                    $experienceOrBoolQuery->add(new TermQuery('experiences_ids', $exp), BoolQuery::SHOULD);
                }
                $baseQuery->add($experienceOrBoolQuery, BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'job_type')) {
                $baseQuery->add(new MultiMatchQuery(['job_types_arr', 'job_types_str'], $params['job_type'], ['operator' => 'or']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'job_types_ids')) {
                $baseQuery->add(new TermsQuery('job_types_ids', $this->termsQuery($params['job_types_ids'])), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'status')) {
                $baseQuery->add(new TermsQuery('status', $this->termsQuery($params['status'])), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'contract_types_ids')) {
                $arrContractTypeIds = explode(',', $params['contract_types_ids']);
                $baseQuery->add(new TermsQuery('contract_types_ids', $arrContractTypeIds), BoolQuery::MUST);
            }

            // if ($this->hasQuery($params, 'hot')) {
            //     $baseQuery->add(new TermQuery('is_hot', $params['hot']), BoolQuery::MUST);
            // }
            if ($this->hasQuery($params, 'features')) {
                $baseQuery->add(new TermsQuery('features', array_filter(explode(',', $params['features']))), BoolQuery::MUST);
            }

            // if ($this->hasQuery($params, 'features')) {
            //     $features = new BoolQuery();
            //     foreach (array_filter(explode(',', $params['features'])) as $feature) {
            //         $features->add(new TermQuery('features', $feature), BoolQuery::MUST);
            //     }
            //     $baseQuery->add($features, BoolQuery::MUST);
            // }

            // Search with popular condition
            if ($this->hasQuery($params, 'popular')) {
                $baseQuery->add(new TermQuery('is_distinction', $params['popular']), BoolQuery::MUST);
            }

            if (empty($keyword) && empty($params)) {
                $baseQuery = new MatchAllQuery();
            }

            if (array_key_exists('ids', $params)) {
                $baseQuery->add(new IdsQuery(empty($params['ids']) ? [] : $this->termsQuery($params['ids'])), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'except_ids')) {
                $baseQuery->add(new IdsQuery($this->termsQuery($params['except_ids'])), BoolQuery::MUST_NOT);
            }

            if ($this->hasQuery($params, 'except_skills')) {
                $baseQuery->add(new TermsQuery('skills_ids', $this->termsQuery($params['except_skills'])), BoolQuery::MUST_NOT);
            }

            //remove if user has blacklist companies
            if ($this->hasQuery($params, 'blacklist_companies')) {
                // $baseQuery->add(new TermsQuery('company.id', $params['blacklist_companies']), BoolQuery::MUST_NOT);
                $baseQuery->add(new TermsQuery('owned_id', $params['blacklist_companies']), BoolQuery::MUST_NOT);
            }

            if ($this->hasQuery($params, 'packages')) {
                $baseQuery->add(new TermsQuery('packages.keyword', explode(',', $params['packages'])), BoolQuery::MUST);
            }

            //filter salary_range
            if (isset($params['salary_range']['max']) && isset($params['salary_range']['min'])) {
                if (is_numeric($params['salary_range']['max']) && is_numeric($params['salary_range']['min'])) {
                    if ((int) $params['salary_range']['max'] > (int) $params['salary_range']['min']) {
                        $rangeQueryMin = new RangeQuery(
                            'salary.min',
                            [
                                'lte' => $params['salary_range']['max'],
                            ]
                        );
                        $body->addQuery($rangeQueryMin);

                        $rangeQueryMax = new RangeQuery(
                            'salary.max',
                            [
                                'gte' => $params['salary_range']['min'],
                            ]
                        );
                        $body->addQuery($rangeQueryMax);
                    } else {
                        $rangeQuery = new RangeQuery(
                            'salary.min',
                            [
                                'gte' => $params['salary_range']['min'],
                            ]
                        );
                        $body->addQuery($rangeQuery);
                    }
                    $currency = (isset($params['currency']) && !empty($params['currency'])) ? $params['currency'] : 'USD';
                    $baseQuery->add(new TermQuery('salary.currency.keyword', $currency), BoolQuery::MUST);
                }
            }

            $rangeQueryExpire = $this->hasRangeQuery($params, 'expire_from', 'expire_to');
            if (!empty($rangeQueryExpire)) {
                $dateRangeQuery = new RangeQuery(
                    'expires_at',
                    array_filter(
                        [
                            'gte' => $rangeQueryExpire['gte'] ?? null,
                            'lte' => $rangeQueryExpire['lte'] ?? null,
                            'format' => 'yyyy-MM-dd HH:mm:ss',
                        ]
                    )
                );

                $body->addQuery($dateRangeQuery);
            }

            // Filter by categories ids
            if ($this->hasQuery($params, 'categories_ids')) {
                $baseQuery->add(new TermsQuery('categories_ids', explode(',', $params['categories_ids'])), BoolQuery::MUST);
            }

            /**
             * Search V2 area
             */

            // Job category new
            if ($this->hasQuery($params, 'job_categories_ids')) {
                $baseQuery->add(new TermsQuery('job_categories_ids', explode(',', $params['job_categories_ids'])), BoolQuery::FILTER);
            }

            // Job Role new
            if ($this->hasQuery($params, 'job_roles_ids')) {
                $baseQuery->add(new TermsQuery('job_roles_ids', explode(',', $params['job_roles_ids'])), BoolQuery::MUST);
            }

            // Salary min/max
            if ($this->hasQuery($params, 'salary_min') && $this->hasQuery($params, 'salary_max')) {
                $rangeQueryMin = new RangeQuery(
                    'salary.min_filter',
                    [
                        'lte' => $params['salary_max'],
                    ]
                );

                $rangeQueryMax = new RangeQuery(
                    'salary.max_filter',
                    [
                        'gte' => $params['salary_min'],
                    ]
                );
                $baseQuery->add($rangeQueryMin, BoolQuery::MUST);
                $baseQuery->add($rangeQueryMax, BoolQuery::MUST);
            }

            // Job Benefit
            if ($this->hasQuery($params, 'benefit_ids')) {
                $baseQuery->add(new TermsQuery('benefit_ids', explode(',', $params['benefit_ids'])), BoolQuery::MUST);
            }

            // Search by company industry
            if ($this->hasQuery($params, 'company_industry_ids')) {
                $baseQuery->add(new TermsQuery('company.industries_ids', explode(',', $params['company_industry_ids'])), BoolQuery::MUST);
            }

            // Search by company sizes
            if ($this->hasQuery($params, 'company_size_ids')) {
                $baseQuery->add(new TermsQuery('company.num_employees_id', explode(',', $params['company_size_ids'])), BoolQuery::MUST);
            }

            /**
             * End search V2
             */

            /*
            |--------------------------------------------------------------------------
            | Aggregation
            |--------------------------------------------------------------------------
            |
            */
            $body->addAggregation(new TermsAggregation('status', 'status'));
            $body->addAggregation(new TermsAggregation('categories', 'categories_ids'));

            //add group company.id
            if ($this->hasQuery($params, 'group_company')) {
                // $companyId = new TermsAggregation('company_id', 'company.id');
                $companyId = new TermsAggregation('company_id', 'owned_id');
                $companyId->addParameter('size', 24);
                $body->addAggregation($companyId);
            }

            /**
             * Function score query
             */
            if ($this->hasQuery($params, 'search_v2')) {
                $baseQuery = new FunctionScoreQuery($baseQuery);
                $scoringFields = [
                    'addresses.address_region_ids' => 'region_ids',
                    'job_categories_ids',
                    'job_roles_ids',
                    'salary.min_filter' => 'salary_min',
                    'salary.max_filter' => 'salary_max',
                    'benefit_ids',
                    'skills_id',
                    'experiences_id',
                    'job_types_ids',
                    'contract_types_ids',
                    'company.num_employees_id' => 'company_size_ids',
                    'company.industries_ids' => 'company_industry_ids',
                ];

                $scoringValues = array_values($scoringFields);
                $scoreParams = collect($params)->only($scoringValues);

                // Having any param then use script score
                if ($scoreParams->count()) {
                    // Final script header
                    $scriptLines = ['int score = 0;'];

                    foreach ($scoringFields as $field => $paramKey) {
                        if (is_int($field)) {
                            $field = $paramKey; // for normal ones
                            $paramKey = $field;
                        }

                        $escapedField = str_replace("'", "\\'", $field);
                        $escapedParam = str_replace("'", "\\'", $paramKey);

                        // If not search this then ignore from the score script
                        if (!$scoreParams->has($paramKey)) {
                            continue;
                        }

                        $scriptLines[] = <<<EOL
                            if (doc.containsKey('$escapedField') && doc['$escapedField'].size() > 0
                                && params.containsKey('$escapedParam') && params.$escapedParam != null) {
                                for (v in doc['$escapedField']) {
                                    if (params.$escapedParam.contains(v.toString())) {
                                        score += 1;
                                    }
                                }
                            }
                        EOL;
                    }
                    $scriptLines[] = 'return score;';
                    $scriptSource = implode("\n", $scriptLines);

                    $baseQuery->addScriptScoreFunction($scriptSource, $scoreParams->toArray());
                }
                $baseQuery->addParameter('boost_mode', 'replace');
            }

            /*
            |--------------------------------------------------------------------------
            | SORT JOB
            |--------------------------------------------------------------------------
            |
            | Allways sort job by desc publish date.
            |
            */
            if ($this->hasQuery($params, 'search_v2')) {
                $body->addSort(new FieldSort('level', null, ['order' => FieldSort::DESC]));
                $body->addSort(new FieldSort('package_ids', null, ['order' => FieldSort::DESC, 'mode' => 'max']));
                $body->addSort(new FieldSort('_score', null, ['order' => FieldSort::DESC]));
                if ($this->hasOrderBy($params, 'low_high_salary')) {
                    $body->addSort(new FieldSort('salary.min_filter', null, ['order' => FieldSort::DESC]));
                } else if ($this->hasOrderBy($params, 'high_low_salary')) {
                    $body->addSort(new FieldSort('salary.max_filter', null, ['order' => FieldSort::DESC]));
                } else if ($this->hasOrderBy($params, 'newest_refresh')) {
                    $body->addSort(new FieldSort('refreshed_at', null, ['order' => FieldSort::DESC]));
                } else if ($this->hasOrderBy($params, 'oldest_refresh')) {
                    $body->addSort(new FieldSort('refreshed_at', null, ['order' => FieldSort::ASC]));
                }
            } else if ($this->hasOrderBy($params, 'fresher_jobs_so_hot')) {
                $body->addSort(new FieldSort('num_viewers', null, ['order' => FieldSort::DESC]));
                $body->addSort(new FieldSort('refreshed_at', null, ['order' => FieldSort::DESC]));
            } elseif ($this->hasOrderBy($params, 'custom_score') && $this->hasQuery($params, 'order_region_ids')) {
                $baseQueryScoreWeight = new FunctionScoreQuery($baseQuery);
                $socreQuery = new TermsQuery('addresses.address_region_ids', $this->termsQuery($params['order_region_ids']));
                $baseQueryScoreWeight->addWeightFunction(1000000, $socreQuery);
                $body->addQuery($baseQueryScoreWeight);

                $baseQueryScoreScript = new FunctionScoreQuery($baseQuery);
                $baseQueryScoreScript->addScriptScoreFunction("(_score+Double.parseDouble(doc['id'].value))/1000000", [], [], null);
                $body->addQuery($baseQueryScoreScript);
            } elseif ($this->hasOrderBy($params, 'random')) {
                $baseQuery = new FunctionScoreQuery($baseQuery);
                $baseQuery->addRandomFunction();
            } elseif ($this->hasOrderBy($params, 'newest_job')) {
                $body->addSort(new FieldSort('level', null, ['order' => FieldSort::DESC]));
                $body->addSort(new FieldSort('scores', null, ['order' => FieldSort::DESC]));
                $body->addSort(new FieldSort('refreshed_at', null, ['order' => FieldSort::DESC]));
            } elseif ($this->hasOrderBy($params, 'newest')) {
                $body->addSort(new FieldSort('level', null, ['order' => FieldSort::DESC]));
                $body->addSort(new FieldSort('id', null, ['order' => FieldSort::DESC]));
                $body->addSort(new FieldSort('refreshed_at', null, ['order' => FieldSort::DESC]));
            } elseif ($this->hasOrderBy($params, 'filter_jobs')) {
                $body->addSort(new FieldSort('scores', null, ['order' => FieldSort::DESC]));
                $body->addSort(new FieldSort('id', null, ['order' => FieldSort::DESC]));
            } elseif ($this->hasOrderBy($params, 'expired')) {
                $body->addSort(new FieldSort('expires_at', null, ['order' => FieldSort::ASC]));
            } elseif ($this->hasOrderBy($params, 'jobs_new')) {
                $body->addSort(new FieldSort('level', null, ['order' => FieldSort::DESC]));
                $body->addSort(new FieldSort('refreshed_at', null, ['order' => FieldSort::DESC]));
            } elseif ($this->hasOrderBy($params, 'newest_skill_score')) {
                $body->addSort(new FieldSort('_score', null, ['order' => FieldSort::DESC]));
                $body->addSort(new FieldSort('published_at', null, ['order' => FieldSort::DESC]));
            } elseif ($this->hasOrderBy($params, 'best_match_score')) {
                $body->addSort(new FieldSort('level', null, ['order' => FieldSort::DESC]));
                $body->addSort(new FieldSort('_score', null, ['order' => FieldSort::DESC]));
            } elseif ($this->hasOrderBy($params, 'jobs_level')) {
                $body->addSort(new FieldSort('level', null, ['order' => FieldSort::DESC]));
                $body->addSort(new FieldSort('refreshed_at', null, ['order' => FieldSort::DESC]));
            } elseif (!isset($params['not_sort'])) {
                $body->addSort(new FieldSort('published_at', null, ['order' => FieldSort::DESC]));
            }

            $body->addQuery($baseQuery);

            /*
            |--------------------------------------------------------------------------
            | Pagination
            |--------------------------------------------------------------------------
            |
            */

            if (!isset($params['fake'])) {
                $size = $params['page_size'] ?? 10;
                $from = isset($params['page']) ? $size * ($params['page'] - 1) : 0;
            } else {
                $size = $params['fake']['size'];
                $from = $params['fake']['from'];
            }

            $body->setSize($size);
            $body->setFrom($from);

            return $client->search(['index' => $this->model->searchableAs(), 'body' =>$body->toArray()])->asArray();
        });

        return $client->query(function ($builder) {
                $builder->with(
                    'meta',
                    'taxonomies.term',
                    'media',
                    'addresses',
                    'company.meta',
                    'company.media'
                )->withCount('candidateReady', 'views', 'followers');
            });
    }

    private function hasQuery($query, $key)
    {
        return (bool) (isset($query[$key]) && !is_null($query[$key]) && !empty($query[$key]));
    }

    private function hasRangeQuery($query, $from, $to)
    {
        return array_filter(
            [
                'gte' => $query[$from] ?? null,
                'lte' => $query[$to] ?? null,
            ]
        );
    }

    private function hasOrderBy($params, $key): bool
    {
        return isset($params['ordering'])
            && in_array($key, explode(',', (string) $params['ordering']));
    }

    public function termsQuery($string, $intval = false)
    {
        return collect(explode(',', (string) $string))->reject(function ($value, $intval) {
            if ($intval) {
                return empty($value) && intval($value);
            }

            return empty($value);
        })->values()->all();
    }

    /**
     * Fill model with draft & without syncing to search.
     */
    public function fillAsDraft(array $condition, array $data)
    {
        $item = null;

        if (!empty($condition)) {
            $item = $this->getFirstBy($condition);
        }

        if (!empty($item)) {
            $item->withoutSyncingToSearch(function () use ($item, $data) {
                $item->fill($data);
                $item->markAsReview();
                $item->save();
            });

            return $item;
        }

        $this->resetModel();

        return false;
    }

    /**
     * Save model with draft & without syncing to search.
     */
    public function saveAsDraft(array $condition, array $data)
    {
        $item = null;

        if (!empty($condition)) {
            $item = $this->getFirstBy($condition);

            if (!empty($item)) {
                $item->withoutSyncingToSearch(function () use (&$item, $data) {
                    $item->update(array_merge($data, [
                        $this->model->getStatusColumn() => $this->model->getStatusReview(),
                    ]));
                });

                return $item;
            }
        } else {
            $this->model->withoutSyncingToSearch(function () use (&$item, $data) {
                $item = $this->create(array_merge($data, [
                    $this->model->getStatusColumn() => $this->model->getStatusReview(),
                ]));
            });

            return $item;
        }

        $this->resetModel();

        return false;
    }

    public function encryptRecommendJobs($email, array $job_ids, $event)
    {
        $userResume = app(UserRepositoryInterface::class)->firstOrCreate([
            'email' => $email,
        ]);

        $jobList = $this->searchOnElasticsearch(null, [
            'ids' => implode(',', $job_ids),
        ]);

        if (!$jobList['hits']['total']['value']) {
            return [
                'success' => false,
                'message' => 'Email wrong!',
            ];
        }

        return [
            'success' => true,
            'data' => urlencode(encrypt([
                'uuid' => $userResume->uuid,
                'job_ids' => $job_ids,
                'event' => $event,
            ])),
        ];
    }

    public function decryptRecommendJobs($token)
    {
        try {
            $data = decrypt(urldecode((string) $token));

            $jobIds = $data['job_ids'];
            $jobList = $this->searchOnElasticsearch(null, [
                'ids' => implode(',', $jobIds),
                'status' => Job::STATUS_OPEN,
            ])['hits']['hits'] ?? [];

            if (count($jobList)) {
                $jobList = \collect($jobList)->map(fn($item) => collect($item['_source'])->only(['id', 'title', 'addresses', 'skills', 'salary', 'skills_ids', 'skills_str', 'skills_arr', 'company', 'detail_url'])->toArray());
            }

            if (empty($data['uuid']) || empty($jobList) || empty($data['event'])) {
                return [
                    'success' => false,
                    'message' => 'Token is invalid',
                ];
            }
            if (!auth('api')->check()) {
                $userResume = app(UserRepositoryInterface::class)->getModel()->where('uuid', $data['uuid'])->first();
            } else {
                $userResume = auth('api')->user();
            }

            $skillsIds = $jobList->reduce(fn($result, $jobItem) => array_merge($result, $jobItem['skills_ids']), []);

            $skillList = Taxonomy::with('term')->taxonomy('skills')->whereIn('id', $skillsIds)->get()->map(function ($taxonomyItem) {
                $taxonomyItem->name = $taxonomyItem->term->name;

                return $taxonomyItem->only(['id', 'name']);
            });

            $jobList = $jobList->map(function ($jobItem) use ($skillList) {
                $jobItem['skills_arr'] = array_values($skillList->whereIn('id', $jobItem['skills_ids'])->toArray());

                return $jobItem;
            });

            $applied_ids = app(CandidateRepositoryInterface::class)->getModel()->where('resume_id', $userResume->id)->whereIn('job_id', $jobList->pluck('id')->toArray())->get()->pluck('job_id')->unique()->values()->toArray();

            if ($jobList->count() === count($applied_ids)) {
                $event = null;
            } else {
                $event = $data['event'];
            }

            return [
                'success' => true,
                'data' => [
                    'uuid' => $data['uuid'],
                    'job_list' => $jobList,
                    'event' => $event,
                    'applied_ids' => $applied_ids,
                ],
            ];
        } catch (DecryptException) {
            return [
                'success' => false,
                'message' => 'Token is invalid',
            ];
        }
    }

    public function sortArraySkill($arrSkillId)
    {
        $arrSkillStar = [];
        $arrSkills = [];
        foreach ($arrSkillId as $skill_id_rating) {
            preg_match_all('/\d+/', (string) $skill_id_rating, $numbers);
            $arrSkillStar[] = $numbers[0][1] ?? null;
            $skill_id = substr((string) $skill_id_rating, 0, strpos((string) $skill_id_rating, '^'));
            $arrSkills[] = $skill_id == '' ? $skill_id_rating : $skill_id;
        }
        array_multisort($arrSkillStar, SORT_DESC, $arrSkills);

        return $arrSkills;
    }

    public function availableLocations()
    {
        $result = Job::search("*", function (\Elastic\Elasticsearch\Client $client, \ONGR\ElasticsearchDSL\Search $body) {
            $baseQuery = new BoolQuery();
            $baseQuery->add(new TermQuery('status', Job::STATUS_OPEN), BoolQuery::MUST);

            $body = new \ONGR\ElasticsearchDSL\Search();

            $body->addQuery($baseQuery);
            $body->addAggregation(new TermsAggregation('regions', 'addresses.address_region_ids'));
            $body->setSize(0);

            return $client->search([
                'index' => $this->model->searchableAs(),
                'body' => $body->toArray(),
            ])->asArray();
        })->raw();

        return $result['aggregations']['regions']['buckets'];
    }
}
