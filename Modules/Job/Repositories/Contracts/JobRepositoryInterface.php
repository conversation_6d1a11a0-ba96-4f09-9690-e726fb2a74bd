<?php

namespace Modules\Job\Repositories\Contracts;

use Redmix0901\Core\Repositories\Interfaces\BaseRepositoryInterface;

interface JobRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * @inheritdoc
     */
    public function searchOnEloquent($keyword = null, $params = [], $fields = '*');

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = '*', $params = [], $fields = '*');

    public function encryptRecommendJobs($email, array $job_ids, $event);

    public function decryptRecommendJobs($token);

    public function availableLocations();
}
