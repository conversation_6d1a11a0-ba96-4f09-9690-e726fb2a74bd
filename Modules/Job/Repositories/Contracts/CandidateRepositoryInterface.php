<?php

namespace Modules\Job\Repositories\Contracts;

use Redmix0901\Core\Repositories\Interfaces\BaseRepositoryInterface;

interface CandidateRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * @inheritdoc
     */
    public function searchOnEloquent($keyword = null, $params = [], $fields = '*');

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = '*', $params = [], $fields = '*');

    /**
     * Make Data for Bar Chart.
     */
    public function candidateBarChartData($period, $fromDate, $toDate, $labelFormat);

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearchStatistics($keyword = '*', $params = [], $fields = '*');
}
