<?php

namespace Modules\Job\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class TopDevBasicAuth
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if ($request->getUser() === 'TopDevBeta' && $request->getPassword() === 'TopDev@Beta') {
            return $next($request);
        }

        return response('Unauthorized', 401);
    }
}
