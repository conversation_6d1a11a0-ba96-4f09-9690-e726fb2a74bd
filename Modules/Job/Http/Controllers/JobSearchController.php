<?php

namespace Modules\Job\Http\Controllers;

use App\Helpers\FakeDataElastic;
use App\Helpers\QueueHeplers;
use App\Helpers\ValidateParamsHelper;
use App\Http\Controllers\Controller;
use App\Services\MetaSearchV2;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Modules\Job\Repositories\Contracts\JobRepositoryInterface;
use Modules\Job\Entities\Job;
use Modules\Job\Http\Requests\JobSearchRequest;
use Modules\Job\Transformers\JobCollection;
use Modules\Search\Jobs\ProcessUserSearchKeyword;
use Redmix0901\Core\Http\Responses\BaseHttpResponse;
use Redmix0901\ElasticResource\ElasticCollection;

class JobSearchController extends Controller
{
    protected JobRepositoryInterface $jobRepository;

    public function __construct(JobRepositoryInterface $job) {
        $this->jobRepository = $job;
    }

    public function index(JobSearchRequest $request) {
        if (isset($request->region_ids) && !empty($request->region_ids)) {
            $request['region_ids'] = app(ValidateParamsHelper::class)->validateRegionIds($request->region_ids);
        }

        $pageSize = $request->page_size ?? 15;
        $request['search_v2'] = true;
        $jobs = $this->jobRepository->searchOnElasticsearch(
            strtolower(str_replace(['++', '#'], '', $request->keyword)),
            array_merge($request->all(), [
                'page_size' => $pageSize,
                'status' => Job::STATUS_OPEN,
            ])
        );

        // if (!$request->_f) {
        //     $jobsCollection = (new ElasticCollection($jobs));
        //     $countJobs = $jobsCollection->total();

        //     $jobsFake = [];
        //     if ($countJobs > 30 && ((!isset($request->region_ids) && empty($request->region_ids)) || (isset($request->region_ids) && $request->region_ids == 'all'))) {
        //         $jobsFake = (new FakeDataElastic(
        //             $this->jobRepository,
        //             $pageSize,
        //             $countJobs,
        //             $request->page
        //         ))->searchWithElas(
        //             strtolower(str_replace(['++', '#'], '', (string) $request->keyword)),
        //             array_merge($request->all(), [
        //                 'status' => Job::STATUS_OPEN,
        //             ])
        //         );
        //     }

        //     if (isset($jobsFake['data'])) {
        //         $jobs['hits']['hits'] = array_merge($jobsFake['data'], $jobs['hits']['hits']);
        //         $jobs['hits']['total']['value'] = $jobsFake['totalFake'];
        //     }
        // }

        $user = auth('api')->user();
        app(QueueHeplers::class)->trackingUserV2(['jobs::' => $request->ids, 'companies::' => $request->ids], 'search', $user, $request);

        $location = $request->getLocationFromId();
        $metaData = $request->getMetaDataFromRequest();
        $keyword = $request->get('keyword');

        if (App::isLocale('en')) {
            $locale = 'en_US';
            $locationMetaTag = Str::ascii($location->pluck('name')->join(', ') ?: null);
        } else {
            $locale = 'vi_VN';
            $locationMetaTag = $location->pluck('name')->join(', ') ?: null;
        }

        $numOfSearchJobs = $jobs['meta']['total'] ?? 0;
        //Get các meta search được custom và lưu trong DB
        $metaSearch = (new MetaSearchV2(
            $keyword,
            $numOfSearchJobs,
            $metaData,
            $location,
        ))
        ->get();

        $metaTitle = $metaSearch['title'];
        $metaDescription = $metaSearch['description'];
        $metaKeywords = $metaSearch['keywords'];

        if (!$metaTitle) {
            $metaTitle = blank($keyword)
                ? trans('meta_search.keyword.title_without_search')
                : trans('meta_search.keyword.title_with_search', ['keywords' => $keyword]);
        }

        if (!($metaKeywords)) {
            $metaKeywords = blank($keyword)
                ? trans('meta_search.keyword.keywords_without_search')
                : trans('meta_search.keyword.keywords', ['keywords' => $keyword ?? 'it']);
        }

        if (!$metaDescription) {
            $metaDescription = blank($keyword)
                ? trans('meta_search.keyword.description_without_search', ['total' => $numOfSearchJobs])
                : trans('meta_search.keyword.description', ['total' => $numOfSearchJobs, 'keywords' => $keyword ?? 'IT', 'location' => $locationMetaTag]);
        }

        $metaTag = [
            'locale' => $locale,
            'title' => $metaTitle,
            'keywords' => $metaKeywords,
            'description' => $metaDescription,
            'type' => 'website',
            'site_name' => 'Topdev',
            'robots' => 'index, follow',
            'image' => resize_with_salt(topdev_thumbnail(), 500, 261)
        ];

        try {
            $userId = auth('api')->id();

            if (!empty($userId)) {
                $processUserSearchKeyword = new ProcessUserSearchKeyword(
                    $userId,
                    $keyword,
                    $location->pluck('id')->join(','),
                    $request->get('fullUrl')
                );
                dispatch($processUserSearchKeyword);
            }

        } catch (\Throwable $exception) {
            Log::info('Fail to log search');
        }

        return JobCollection::fromElasticsearch($jobs, $pageSize)
            ->additional([
                'meta_tag' => $metaTag
            ])
            ->response();
    }
}
