<?php

namespace Modules\Job\Http\Controllers;

use App\Helpers\GetDataUser;
use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Modules\Activity\Services\ProgressManager;
use Modules\File\Repositories\Contracts\MediaRepositoryInterface;
use Modules\Job\Entities\Job;
use Modules\File\Entities\Media;
use Modules\Job\Http\Requests\ApplicantCaseRequest;
use Modules\Job\Jobs\ApplyProcess;
use Modules\Job\Repositories\Contracts\CandidateRepositoryInterface;
use Modules\Job\Repositories\Contracts\JobRepositoryInterface;
use Modules\Promotion\Services\Facades\Promotion;
use Modules\Tracking\Entities\UserCollect;
use Modules\User\Entities\User;
use Modules\User\Repositories\Contracts\UserRepositoryInterface;
use Modules\User\Transformers\NewestCandidateResource;
use Redmix0901\Core\Http\Responses\BaseHttpResponse;
use Redmix0901\ElasticResource\ElasticCollection;

class CandidateController extends Controller
{
    public function __construct(private readonly JobRepositoryInterface $jobRepository, private readonly CandidateRepositoryInterface $candidateRepository)
    {
    }

    public function applyJobs(ApplicantCaseRequest $request, BaseHttpResponse $response)
    {
        // Check if media_id is a digit and convert it to int
        // We use ctype_digit to check if the string is a digit, and then convert it to int
        // This is because the media_id can be a string or an int, and we want to make sure it's an int
        // in the database.
        if (!empty($request->media_id) && ctype_digit((string) $request->media_id) == true) {
            $request->media_id = (int) $request->media_id;
        }

        if (
            !empty($request->media_id)
            && (
                $request->media_id == 'null'
                || $request->media_id == 'NULL'
                || is_int($request->media_id) == false
                || (
                    is_int($request->media_id)
                    && $request->media_id < 1
                )
            )
        ) {
            unset($request['media_id']);
        } else {
            $isOwner = Media::where([
                'id' => $request->media_id,
                'model_id' => auth('api')->user()->id,
                'model_type' => User::class,
            ])->exists();

            if (!$isOwner) {
                unset($request['media_id']);
            }
        }

        if (!empty($request->cvbuilder_id) && ($request->cvbuilder_id == 'null' || $request->cvbuilder_id == 'NULL')) {
            unset($request['cvbuilder_id']);
        }

        // Check if files_cv is a file and validate the mime type
        // If files_cv is null or empty, unset it
        // If files_cv is a file, validate the mime type and max size
        // If the mime type is not correct or the size is too large, return an error response
        if (!empty($request->files_cv)) {
            if (($request->files_cv == 'null' || $request->files_cv == 'NULL')) {
                unset($request['files_cv']);
            } else {
                $validator = Validator::make(
                    [
                        'files_cv' => $request->files_cv
                    ],
                    [
                        'files_cv' => [
                            'mimes:doc,docx,pdf', 
                            'max:5119',
                            function ($attribute, $value, $fail) {
                                $fileMd5 = md5_file($value->getPathname());
                                $exists = Media::query()
                                    ->where('md5_hash', $fileMd5)
                                    ->whereNot(function ($query) {
                                        $query
                                            ->where('model_id', auth('api')->id())
                                            ->where('model_type', auth('api')->user()->getMorphClass());
                                    })
                                    ->exists();

                                if ($exists) {
                                    $fail('The file has already been uploaded.');
                                }
                            },
                        ],
                    ],
                    [
                        'files_cv.mimes' => "The uploaded file must be a valid document format (doc, docx, pdf).",
                        'files_cv.max' => "The file size must not exceed 5MB."
                    ]
                );

                if ($validator->fails()) {
                    return response()->json([
                        'messages' => $validator->errors()->all(),
                        'success' => false,
                    ]);
                }
            }
        }

        if (empty($request->user_profile_id)) {
            if ($request['case'] != config('validate.params.apply_cv.case')[2]) {
                $arr = $request->only('files_cv', 'cvbuilder_id', 'media_id');
                if (count(array_filter($arr)) < 1) {
                    return response()->json([
                        'message' => 'Case Apply require media',
                        'success' => false,
                    ]);
                }
            }
        }

        if (is_array($request->job_id)) {
            $jobStr = implode(',', $request->job_id);
            $jobIds = $request->job_id;
        } else {
            $jobIds = explode(',', (string) $request->job_id);
            $jobStr = $request->job_id;
        }

        $jobList = $this->jobRepository->searchOnElasticsearch(null, [
            'ids' => $jobStr,
        ]);

        $jobListCollection = new ElasticCollection($jobList);

        $countJoblist = $jobListCollection->total();
        $countJobIds = count($jobIds);
        if ($countJoblist !== count($jobIds)) {
            return response()->json(['message' => 'Job List is wrong', 'success' => false]);
        }

        // Check job status
        foreach ($jobListCollection->aggregations() as $key => $value) {
            if ($key == 'status') {
                foreach ($value as $subKey => $subValue) {
                    if ($subValue['key'] != Job::STATUS_OPEN) {
                        return response()->json(['message' => 'Job is not open', 'success' => false]);
                    }
                }
            }
        }

        $dataPayload = $request->all();
        $files_cv = (isset($dataPayload['files_cv']) && !empty($dataPayload['files_cv'])) ? $dataPayload['files_cv'] : null;
        unset($dataPayload['files_cv']);
        if ($request->case !== (string)config('validate.params.apply_cv.case')[0]) {
            unset($dataPayload['tnc']);
        } else {
            $validator = Validator::make(['tnc' => $request->tnc, 'tnc_is_true' => $request->tnc], [
                'tnc' => ['required'],
                'tnc_is_true' => function ($attribute, $value, $fail) {
                    if (filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) == false) {
                        return $fail('Must agree to the terms to continue.');
                    }
                },
            ], [
                'tnc' => 'The Tnc field is required.',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'messages' => $validator->errors()->all(),
                    'success' => false,
                ]);
            }
            //Get vaule tnc
            array_filter(array_merge(
                $dataPayload,
                array_filter([
                    'tnc' => $request->tnc,
                ])
            ));
        }

        $hasLogin = true;
        $exits = false;
        $user = $request->user('api');
        if ($user) {
            $exits = true;
            if ($request['case'] == config('validate.params.apply_cv.case')[0]) {
                $hasLogin = false;
            }
        } else {
            Log::info('user not login');
            $hasLogin = false;
            $dataUser = $this->resolveUser($dataPayload);
            $user = $dataUser['user'];
            $exits = $dataUser['exits'];
        }

        $jobsApplied = [];
        if ($exits == true) {
            $listDataUser = app(GetDataUser::class)->getDataUser($user, 'applied_jobs');
            if ($listDataUser) {
                foreach ($jobIds as $key => $e) {
                    if (in_array($e, $listDataUser['applied_jobs'])) {
                        array_push($jobsApplied, $e);
                        unset($jobIds[$key]);
                    }
                }
            }

            if (count($jobsApplied) == $countJobIds && $countJobIds == 1) {
                return response()->json([
                    'message' => 'applied',
                    'success' => false,
                ]);
            }
        }

        // Create media for user if has media is file
        if ($files_cv) {
            $collectionMedia = $request->upload_from ?? 'files_cv';
            $user->addFile([$files_cv], $collectionMedia);
            $mediaUpload = $user->getMedia($collectionMedia)->sortByDesc('created_at')->first();
            $dataPayload['media_id'] = $mediaUpload->id ?? null;
        }

        $dataPayload = array_filter(array_merge(
            $dataPayload,
            array_filter([
                'source' => $request->header('X-Topdev-Source'),
                'device_apply' => $request->header('X-Topdev-Device'),
                'fcm_device_token' => $request->header('X-Topdev-Device-Token'),
            ])
        ));

        Log::info('-------------apply source-----');
        Log::info(print_r([
            'source' => $request->header('X-Topdev-Source'),
            'device_apply' => $request->header('X-Topdev-Device'),
            'fcm_device_token' => $request->header('X-Topdev-Device-Token'),
        ], true));

        // Get SimilarJobs for user if apply single
        $progress = null;
        if (count(array_filter($jobIds)) == 1) {
            $progress = ProgressManager::create(
                ['force_event' => 'EventSuggestJobs', 'recently_apply' => implode(',', $jobIds), 'suggest_size' => 5],
                ['id' => $user->id],
            );
        }

        // Utm
        $utm = UserCollect::getLatestUtm();
        if ($utm) {
            $dataPayload = array_merge($dataPayload, [
                'utm_source' => $utm->utm_source,
                'utm_medium' => $utm->utm_medium,
                'utm_campaign' => $utm->utm_campaign,
                'utm_content' => $utm->utm_content,
                'utm_term' => $utm->utm_term,
            ]);
        }

        // Apply promotion
        $promotionReward = Promotion::driver('apply')->run();

        // Apply process to AMS
        $jobStr = implode(',', array_filter($jobIds));
        ApplyProcess::dispatch(
            $dataPayload,
            $user,
            $jobStr,
            $dataPayload['case'],
            $hasLogin,
            $jobsApplied,
            $promotionReward?->id
        );

        // Response to user
        $dataReponse = (isset($dataPayload['cvbuilder_id']) && !empty($dataPayload['cvbuilder_id'])) ? ['type' => 'cvbuilder', 'media_id' => (string)$dataPayload['cvbuilder_id']] : ['type' => 'media', 'media_id' => isset($dataPayload['media_id']) ? (string)$dataPayload['media_id'] : null];
        $dataReponse['comeback_url'] = $user->searchHistory->last()->url_origin ?? frontend_url('viec-lam-it');
        $dataReponse['similar_jobs'] = !empty($progress) ? $progress->getSuggestJobs(false) : [];
        $dataReponse['recently_apply'] = !empty($progress) ? $progress->getRecentlyApply() : [];
        $dataReponse['events'] = !empty($progress) ? $progress->expectEvents() : [];
        $dataReponse['recently_used_file'] = $dataPayload['cvbuilder_id'] ?? $dataPayload['media_id'] ?? null;
        $dataReponse['user_profile_id'] = $dataPayload['user_profile_id'] ?? null;
        $dataReponse['file_name'] = $dataPayload['file_name'] ?? null;
        $dataReponse['promotion_reward'] = $promotionReward?->toArray();

        return response()->json([
            'message' => 'apply_success',
            'success' => true,
            'data' => $dataReponse,
        ]);
    }

    public function resolveUser($request)
    {
        $exits = true;
        $user = app(UserRepositoryInterface::class)->getModel()
            ->where('email', $request['email'])->first();

        if (!$user) {
            $exits = false;
            $user = app(UserRepositoryInterface::class)->firstOrCreate(
                ['email' => $request['email']],
                array_merge(
                    (array)$request,
                    [
                        'type' => User::RESUME_TYPE,
                    ]
                )
            );

            Log::info("CandidateController@resolveUser: User not exits, create new user", [
                'email' => $request['email'],
            ]);
        }

        return [
            'exits' => $exits,
            'user' => $user,
        ];
    }

    public function reports(Request $request, BaseHttpResponse $response)
    {
        $fromDate = $request->from_date ? Carbon::createFromFormat('d-m-Y', $request->from_date) : Carbon::today()->subWeeks(5);
        $toDate = $request->to_date ? Carbon::createFromFormat('d-m-Y', $request->to_date) : Carbon::today();

        $media = app(MediaRepositoryInterface::class)->searchOnElasticsearchStatistics(
            null,
            [
                'created_at' => [
                    'from' => $fromDate->startOfDay()->format('Y-m-d H:i:s'),
                    'to' => $toDate->endOfDay()->format('Y-m-d H:i:s'),
                ],
                'model_type' => \Modules\User\Entities\User::class,
                'period' => $request->period,
                'page_size' => 1,
            ]
        );

        $media = collect($media['aggregations']['statis']['buckets'])->keyBy('key_as_string')->toArray();

        $candidates = $this->candidateRepository->searchOnElasticsearchStatistics(
            null,
            [
                'created_at' => [
                    'from' => $fromDate->startOfDay()->format('Y-m-d H:i:s'),
                    'to' => $toDate->endOfDay()->format('Y-m-d H:i:s'),
                ],
                'period' => $request->period,
                'page_size' => 1,
            ]
        );

        $result = [];
        $countCvUploadFromPopup = [];
        $countCvUploadFromDash = [];
        $countCvUploadFromAms = [];
        $countCvUploadFromApp = [];

        foreach ($media as $key => $e) {
            $allCollection = collect($e['collection_name']['buckets'])->keyBy(fn($item) => strtolower((string) $item['key']))
                ->toArray();

            $keyReSult = (explode(' ', $e['key_as_string']))[1];
            $countCvUploadFromPopup[$keyReSult] = 0;
            $countCvUploadFromDash[$keyReSult] = 0;
            $countCvUploadFromAms[$keyReSult] = 0;
            $countCvUploadFromApp[$keyReSult] = 0;

            if (isset($allCollection['uploadfromtopdev']['doc_count'])) {
                $countCvUploadFromPopup[$keyReSult] = $allCollection['uploadfromtopdev']['doc_count'];
            }

            if (isset($allCollection['uploadfromapplytoall']['doc_count'])) {
                $countCvUploadFromPopup[$keyReSult] = $countCvUploadFromPopup[$keyReSult] + (int) $allCollection['uploadfromapplytoall']['doc_count'];
            }

            if (isset($allCollection['uploadfromcvbuilder']['doc_count'])) {
                $countCvUploadFromDash[$keyReSult] = $allCollection['uploadfromcvbuilder']['doc_count'];
            }

            if (isset($allCollection['files_cv']['doc_count'])) {
                $countCvUploadFromAms[$keyReSult] = $allCollection['files_cv']['doc_count'];
            }

            if (isset($allCollection['uploadfrommobile']['doc_count'])) {
                $countCvUploadFromApp[$keyReSult] = $allCollection['uploadfrommobile']['doc_count'];
            }
        }

        foreach ($candidates['aggregations']['statis']['buckets'] as $key => $e) {
            // Log::info('-------aggregations-----');
            // Log::info(print_r($e,true));

            $countGroup = $e['group']['doc_count'] ?? 0;
            $countUserGroup = $e['group']['resume_count']['value'] ?? 0;

            $countCvbuilder = $e['cvbuilder_id']['doc_count'] ?? 0;
            $countUserCvbuilder = $e['cvbuilder_id']['resume_count']['value'] ?? 0;

            $bucketCvBuilderMobile = $e['cvbuilder_id']['source_type']['buckets'] ?? [];
            //TOPDEV MobileApp
            $bucketCvBuilderMobile = collect($bucketCvBuilderMobile)->where('key', 'MobileApp')->first();
            $countCvbuilderMobile = $bucketCvBuilderMobile['doc_count'] ?? 0;
            $countUserCvbuilderMobile = $bucketCvBuilderMobile['resume_count']['value'] ?? 0;

            $countMedia = $e['media_id']['doc_count'] ?? 0;

            $totalApplicationByMobileApp = $e['source_MobileApp']['doc_count'];
            $totalApplicationByAndroid = $e['source_android']['doc_count'];
            $totalApplicationByIos = $e['source_ios']['doc_count'];
            $totalApplicationByPC = $e['source_pc']['doc_count'];
            $totalApplicationByMobile = $e['source_mobile']['doc_count'];
            $totalApplicant = $e['resume_count']['value'];
            $totalSent = $e['count_sent_employer']['doc_count'] ?? 0;

            //add media
            $countUserUploadFromDash = 0;
            $countUserUploadFromPopPup = 0;
            if (isset($media[$e['key_as_string']]['collection_name']['buckets']) && !empty($media[$e['key_as_string']]['collection_name']['buckets'])) {
                $allCollection = collect($media[$e['key_as_string']]['collection_name']['buckets'])->keyBy(fn($item) => strtolower((string) $item['key']))
                    ->toArray();

                // Log::info('-------allCollection-----');
                // Log::info($e['key_as_string']);
                // Log::info(print_r($allCollection,true));
                if (isset($allCollection['uploadfromcvbuilder']['model_id_count']['value'])) {
                    $countUserUploadFromDash = $allCollection['uploadfromcvbuilder']['model_id_count']['value'];
                    // unset($allCollection['UploadFromCvBuilder']);
                }

                if (isset($allCollection['uploadfromtopdev']['model_id_count']['value'])) {
                    $countUserUploadFromPopPup = $allCollection['uploadfromtopdev']['model_id_count']['value'];
                }

                if (isset($allCollection['uploadfromapplytoall']['model_id_count']['value'])) {
                    $countUserUploadFromPopPup = $countUserUploadFromPopPup + (int)$allCollection['uploadfromapplytoall']['model_id_count']['value'];
                }
            }

            $keyReSult = (explode(' ', (string) $e['key_as_string']))[1];
            $result[$keyReSult] = [
                'total_statis' => $e['doc_count'] ?? 0,

                'total_application' => $e['doc_count'] ?? 0,
                'total_application_by_pc' => $totalApplicationByPC,
                'total_application_by_mobile' => $totalApplicationByMobile,
                'total_application_by_mobile_app' => $totalApplicationByMobileApp,
                'total_application_by_android' => $totalApplicationByAndroid,
                'total_application_by_ios' => $totalApplicationByIos,
                'total_applicant' => $totalApplicant,
                'total_sent' => $totalSent,

                'apply_from_apply_to_all' => $countGroup,
                'apply_by_complete_cv_created_on_topdev' => $countCvbuilder,
                'apply_by_file_upload' => $countMedia,

                'user_apply_from_apply_to_all' => $countUserGroup,
                'user_apply_complete_cv_created_on_topdev' => $countUserCvbuilder,

                'count_user_upload_dash' => $countUserUploadFromDash,
                'count_user_upload_popup' => $countUserUploadFromPopPup,

                'count_cv_upload_from_popup' => $countCvUploadFromPopup[$keyReSult],
                'count_cv_upload_from_dash' => $countCvUploadFromDash[$keyReSult],
                'count_cv_upload_from_app' => $countCvUploadFromApp[$keyReSult],
                'count_cv_upload_from_ams' => $countCvUploadFromAms[$keyReSult],

                'apply_by_complete_cv_created_on_topdev_mobile' => $countCvbuilderMobile,
                'user_apply_complete_cv_created_on_topdev_mobile' => $countUserCvbuilderMobile,

                'created_at' => Carbon::parse($keyReSult)->timestamp,
            ];
        }

        return response()->json([
            'error' => false,
            'message' => 'Okay!',
            'data' => $result,
        ]);
    }
}
