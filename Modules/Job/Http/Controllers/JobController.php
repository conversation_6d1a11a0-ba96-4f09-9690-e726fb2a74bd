<?php

namespace Modules\Job\Http\Controllers;

use App\Constants\DeviceConstant;
use App\Helpers\FakeDataElastic;
use App\Helpers\GetDataUser;
use App\Helpers\Helpers;
use App\Helpers\TaxonomyHelper;
use App\Helpers\ValidateParamsHelper;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use League\Csv\Writer;
use Modules\Announcement\Repositories\Contracts\AnnouncementInterface;
use Modules\Announcement\Transformers\StatusJobAnnouncementCollection;
use Modules\Company\Entities\Company;
use Modules\Company\Repositories\Contracts\CompanyRepositoryInterface;
use Modules\Job\Entities\Job;
use Modules\Job\Enums\JobLevelEnum;
use Modules\Job\Http\Requests\ApiRequest;
use Modules\Job\Http\Requests\StoreJobRequest;
use Modules\Job\Http\Requests\UpdateJobRequest;
use Modules\Job\Http\Requests\ValidateParams;
use Modules\Job\Jobs\CreateJobProcess;
use Modules\Job\Jobs\PublishJobProcess;
use Modules\Job\Jobs\UpdateJobProcess;
use Modules\Job\Repositories\Contracts\JobRepositoryInterface;
use Modules\Job\Transformers\JobCollection;
use Modules\Job\Transformers\JobRecommendResource;
use Modules\Job\Transformers\JobResource;
use Modules\Job\Transformers\PopularHomePageCollection;
use Modules\Taxonomy\Repositories\Contracts\TaxonomyRepositoryInterface;
use Modules\User\Entities\User;
use Modules\VietnamArea\Entities\VietnamArea;
use Modules\VietnamArea\Transformers\AreaResource;
use Redmix0901\Core\Http\Responses\BaseHttpResponse;
use Redmix0901\ElasticResource\ElasticCollection;

class JobController extends Controller
{
    /**
     * @var JobRepositoryInterface
     */
    protected $jobRepository;

    /**
     * Create instance ApiJobController.
     */
    public function __construct(JobRepositoryInterface $job)
    {
        $this->jobRepository = $job;
    }

    /**
     * Tìm kiếm các job tất cả cty trên elasticsearch.
     *
     * @param  ValidateParams  $request
     * @return JsonResponse
     */
    public function all(ValidateParams $request)
    {
        $size = $request->page_size ?? 10;
        $similarJob = $request->similar_job ? explode(',', (string) $request->similar_job) : [];
        $idsSimilar = [];
        $page = $request->page;
        $keyword = strtolower(str_replace(['++', '#'], '', (string) $request->keyword));
        $params = $request->all();

        if ($request->has('region_ids') && !empty($request->region_ids)) {
            $params['region_ids'] = app(ValidateParamsHelper::class)->validateRegionIds($request->region_ids);
        }

        if (isset($request['salary_range']) && !empty($request['salary_range'])) {
            $params['salary_range'] = app(TaxonomyHelper::class)->salaryRange($request['salary_range']);
        }

        if ($request->has('group_company')) {
            //Hotfix, sẽ process refactor, nếu bạn đọc được dòng này mà history đã hơn 2 tháng thì hãy ibx cho tôi
            $params['company'] = implode(',', optional(Company::find($request->group_company))->members ?? []);

            if ($request->has('member_group_company_ids')) {
                $params['company'] = implode(',', $request->member_group_company_ids ?? []);
            }
        }

        if ($request->popular_homepage && $request->popular_homepage == true) {
            if ($request->homepage && $request->homepage == true) {
                $params['page'] = 1;
                $params['page_size'] = 23;
            }
            $jobs = $this->popularForCompanies($page ?? 1, $size ?? 10, $params);
            if ($request->homepage && $request->homepage == true) {
                return PopularHomePageCollection::fromElasticsearch($jobs, 23)->response();
            }
        }
        // elseif($request->highlight_companies && $request->highlight_companies == true){
        //     $jobs = $this->getBasicJobs(1, 30, $params);
        //     $size = 30;
        // }
        else {
            $skills_str = null;
            if (!empty($request->skills_id)) {
                $taxonomies = app(TaxonomyRepositoryInterface::class)
                    ->searchOnElasticsearch(null, [
                        'taxonomy' => 'skills',
                        'ids' => $request->skills_id,
                        'page_size' => $size ?? 100000,
                    ]);

                $taxonomiesCollection = (new ElasticCollection($taxonomies));
                if ($taxonomiesCollection->total() > 0) {
                    $arrText = collect($taxonomiesCollection->hits()['hits'])->map(fn($taxonomy) => $taxonomy['_source']['text_vi'])->all();
                    $skills_str = implode(',', $arrText);
                }
            }

            if (!empty($skills_str)) {
                $params['skills_arr'] = array_map(fn($skill) => strtolower(str_replace(['++', '#'], '', (string) $skill)), $arrText);
                unset($params['skills_id']);
            }

            if ($similarJob) {
                foreach (Job::find($similarJob) as $job) {
                    $idsSimilar = array_merge(
                        $idsSimilar,
                        $job->similar_jobs
                            ->whereNull('deleted_at')
                            ->where('status', Job::STATUS_OPEN)
                            ->sortByDesc('pivot.confidence')
                            ->pluck('id')->toArray()
                    );
                }

                $params['page_size'] = 9999;
                unset($params['page']);
                $jobs = $this->jobRepository->searchOnElasticsearch(
                    $keyword,
                    array_merge($params, [
                        'status' => Job::STATUS_OPEN,
                        'ids' => implode(',', $idsSimilar),
                    ])
                );

                if (!blank($idsSimilar) && !blank($jobs['hits']['hits'])) {
                    $jobs['hits']['hits'] = collect(array_replace(array_flip(array_unique($idsSimilar)), collect($jobs['hits']['hits'])->keyBy('_id')->all()))->map(function ($value) {
                        if (is_array($value)) {
                            return $value;
                        }
                    })->reject(fn($item) => is_null($item))->all();
                }

                $jobs['hits']['hits'] = collect($jobs['hits']['hits'])->forPage($page, $size);
            } else {
                $jobs = $this->jobRepository->searchOnElasticsearch(
                    $keyword,
                    array_merge($params, [
                        'status' => Job::STATUS_OPEN,
                    ])
                );
            }
        }

        $jobsCollection = (new ElasticCollection($jobs));
        $countJobs = ($request->popular_homepage && $request->popular_homepage == true) ? 1 : $jobsCollection->total();
        if (isset($request->force_size) && $countJobs < $request->force_size) {
            $jobsForceSize = $this->jobRepository->searchOnElasticsearch(
                $keyword,
                [
                    'status' => Job::STATUS_OPEN,
                    'page_size' => $request->force_size - $countJobs,
                ]
            );
            $jobs = array_replace_recursive($jobsForceSize, $jobs);
        }
        $jobsFake = [];

        // Check to include fake job
        if ($countJobs > 50 && !$request->_f) {
            $jobsFake = (new FakeDataElastic(
                $this->jobRepository,
                $size,
                $countJobs,
                $page
            ))->searchWithElas(
                strtolower(str_replace(['++', '#'], '', (string) $request->keyword)),
                array_merge($params, [
                    'status' => Job::STATUS_OPEN,
                ])
            );
        }

        if (isset($jobsFake['data'])) {
            $jobs['hits']['hits'] = array_merge($jobsFake['data'], $jobs['hits']['hits']);
            unset($jobsFake['data']);
        }

        $user = auth('api')->user();
        $listDataUser = app(GetDataUser::class)->getDataUser($user, 'applied_jobs,followed_jobs,blacklist_companies,followed_companies');

        // if (empty($request->popular_homepage)) {
        //     app(QueueHeplers::class)->trackingUserV2(['jobs::' => $request->ids], 'search', $user, $request);
        // }

        return JobCollection::fromElasticsearch($jobs, $size)
            ->dataFake(isset($jobsFake['totalFake']) ? $jobsFake : [])
            ->listDataUser($listDataUser)
            ->response()
            ->setStatusCode(200);
    }

    public function popularForCompanies($page = 1, $size = 3, $params = [])
    {
        $jobs = $this->jobRepository->searchOnElasticsearch(
            null,
            [
                'page' => 1,
                'status' => Job::STATUS_OPEN,
                'page_size' => 10000,
                'popular' => true,
            ]
        );

        $count_jobs_distinction = (new ElasticCollection($jobs))->total();
        $jobs['hits']['hits'] = collect($jobs['hits']['hits'])->groupBy('_source.owned_id')->toArray();

        $jobs['hits']['hits'] = collect($jobs['hits']['hits'])->map(fn($e) => end($e))->shuffle()->forPage($page, $size)->all();

        if ($count_jobs_distinction < 23) {
            $jobs_basic = $this->getBasicJobs(1, 23 - $count_jobs_distinction, $params);
            $jobs['hits']['hits'] = array_merge($jobs['hits']['hits'], $jobs_basic['hits']['hits']);
        }

        // $jobs['hits']['hits'] = collect($jobs['hits']['hits'])->map(function($e){
        //     return end($e);
        // })->forPage($page,$size)->all();

        return $jobs;
    }

    /**
     * @param $page
     * @param $size
     * @param $params
     *
     * @return array
     */
    public function getBasicJobs($page = 1, $size = 3, $params = [])
    {
        $jobs_basic = $this->jobRepository->searchOnElasticsearch(
            null,
            [
                'page' => 1,
                'status' => Job::STATUS_OPEN,
                'page_size' => 10000,
                'packages' => 'basic-plus',
            ]
        );
        $jobs_basic['hits']['hits'] = collect($jobs_basic['hits']['hits'])->groupBy('_source.owned_id')->toArray();

        $jobs_basic['hits']['hits'] = collect($jobs_basic['hits']['hits'])->map(fn($e) => end($e))->shuffle()->forPage($page, $size)->all();

        return $jobs_basic;
    }

    /**
     * Tìm kiếm các job tất cả cty trên elasticsearch nếu ko có thì lấy jobs mới nhất.
     *
     * @param  ValidateParams  $request
     *
     * @return JsonResponse
     */
    public function recommend(ValidateParams $request)
    {
        $size = $request->page_size ?? 10;

        if (isset($request->region_ids) && !empty($request->region_ids)) {
            $request['region_ids'] = app(ValidateParamsHelper::class)->validateRegionIds($request->region_ids);
        }

        if (isset($request['salary_range']) && !empty($request['salary_range'])) {
            $request['salary_range'] = app(TaxonomyHelper::class)->salaryRange($request['salary_range']);
        }

        $listDataUser = app(GetDataUser::class)->getDataUser(auth('api')->user(), 'applied_jobs,followed_jobs,blacklist_companies,followed_companies');

        $jobs = $this->jobRepository->searchOnElasticsearch(
            $request->keyword,
            array_merge($request->all(), [
                'status' => Job::STATUS_OPEN,
                'blacklist_companies' => $listDataUser['blacklist_companies'] ?? [],
                'except_ids' => implode(',', $listDataUser['applied_jobs'] ?? []),
            ])
        );

        $countJobs = (new ElasticCollection($jobs))->total();

        if ($countJobs < $size) {
            $extraJobs = $this->jobRepository->searchOnElasticsearch(
                null,
                [
                    'status' => Job::STATUS_OPEN,
                    'ordering' => 'newest_job',
                    'page_size' => $size - $countJobs,
                    'blacklist_companies' => $listDataUser['blacklist_companies'] ?? [],
                    'except_ids' => implode(',', $listDataUser['applied_jobs'] ?? []),
                ]
            );

            $extraJobs['hits']['hits'] = array_merge(
                $jobs['hits']['hits'] ?? [],
                $extraJobs['hits']['hits'] ?? []
            );
        }

        return JobCollection::fromElasticsearch($extraJobs ?? $jobs, $size)
            ->listDataUser($listDataUser)
            ->response()
            ->setStatusCode(200);
    }

    public function recommendV2(ValidateParams $request, $id)
    {
        $size = $request->page_size ?? 10;
        if (!empty($id)) {
            $job = $this->jobRepository->searchOnElasticsearch(
                null,
                [
                    'page_size' => 1,
                    'ids' => $id,
                ]
            );
            if (isset($job['hits']['hits'][0]['_source'])) {
                $request['skills_id'] = implode(',', $job['hits']['hits'][0]['_source']['skills_ids']);
                $request['region_ids'] = implode(',', $job['hits']['hits'][0]['_source']['addresses']['address_region_ids']);
            }
        }

        if (isset($request->region_ids) && !empty($request->region_ids)) {
            $request['region_ids'] = app(ValidateParamsHelper::class)->validateRegionIds($request->region_ids);
        }

        if (isset($request['salary_range']) && !empty($request['salary_range'])) {
            $request['salary_range'] = app(TaxonomyHelper::class)->salaryRange($request['salary_range']);
        }

        $listDataUser = app(GetDataUser::class)->getDataUser(auth('api')->user(), 'applied_jobs,followed_jobs,blacklist_companies,followed_companies');

        $except_ids = $listDataUser['applied_jobs'] ?? [];
        if (!empty($id) && is_array($except_ids)) {
            array_push($except_ids, $id);
        }

        $jobs = $this->jobRepository->searchOnElasticsearch(
            $request->keyword,
            array_merge($request->all(), [
                'status' => Job::STATUS_OPEN,
                'blacklist_companies' => $listDataUser['blacklist_companies'] ?? [],
                'except_ids' => implode(',', $except_ids),
            ])
        );

        $countJobs = (new ElasticCollection($jobs))->total();

        if ($countJobs < $size) {
            $extraJobs = $this->jobRepository->searchOnElasticsearch(
                null,
                [
                    'status' => Job::STATUS_OPEN,
                    'ordering' => 'newest_job',
                    'page_size' => $size - $countJobs,
                    'blacklist_companies' => $listDataUser['blacklist_companies'] ?? [],
                    'except_ids' => implode(',', $except_ids),
                ]
            );

            $extraJobs['hits']['hits'] = array_merge(
                $jobs['hits']['hits'] ?? [],
                $extraJobs['hits']['hits'] ?? []
            );
        }

        return JobCollection::fromElasticsearch($extraJobs ?? $jobs, $size)
            ->listDataUser($listDataUser)
            ->response()
            ->setStatusCode(200);
    }

    /**
     * Get job detail.
     *
     * @param  ApiRequest  $request
     * @param  int  $id
     * @param  BaseHttpResponse  $response
     *
     * @return JobResource|BaseHttpResponse
     */
    public function job(ApiRequest $request, int $id, BaseHttpResponse $response)
    {
        $job = $this->jobRepository->searchOnElasticsearch(null, [
            'ids' => $id,
        ]);
        $statusJob = $job['hits']['hits'][0]['_source']['status'] ?? -1;

        if ($request->isMobileApp() && ($statusJob == Job::STATUS_REVIEW) || $statusJob == Job::STATUS_DRAFT) {
            $company = app(CompanyRepositoryInterface::class)->searchOnElasticsearch(
                null,
                [
                    'ids' => $job['hits']['hits'][0]['_source']['owned_id'],
                    'page_size' => 1,
                ]
            );

            $companyDetailUrl = ($company['hits']['hits'][0]['_source']['detail_url']) ?? null;
            if (!empty($companyDetailUrl)) {
                $companyDetailUrl = \locale_url($companyDetailUrl, $request->locale ?? 'en_US', 'Company');
            }

            return $response->setCode(404)
                ->setError(true)
                ->setMessage('Entity for ElasticModel not found')
                ->setData([
                    'redirect_to' => $companyDetailUrl,
                ]);
        }

        $listDataUser = app(GetDataUser::class)->getDataUser(auth('api')->user(), 'applied_jobs,followed_jobs,blacklist_companies,followed_companies');

        return JobResource::listDataUser($listDataUser)->fromElasticsearch($job);
    }

    /**
     * @param  StoreJobRequest  $request
     * @param  BaseHttpResponse  $response
     *
     * @return BaseHttpResponse
     */
    public function store(StoreJobRequest $request, BaseHttpResponse $response)
    {
        $data = json_decode($request->getContent(), true);

        $this->dispatch(new CreateJobProcess($data, $request->user()));

        return $response->setCode(201)
            ->setMessage('Creating');
    }

    /**
     * @param  Job  $job
     * @param  UpdateJobRequest  $request
     * @param  BaseHttpResponse  $response
     *
     * @return BaseHttpResponse
     */
    public function update(Job $job, UpdateJobRequest $request, BaseHttpResponse $response)
    {
        $data = json_decode($request->getContent(), true);

        $this->dispatch(new UpdateJobProcess($job, $data, $request->user(), $request->method()));

        return $response->setMessage('Updating');
    }

    /**
     * @param  Request  $request
     * @param  BaseHttpResponse  $response
     *
     * @return BaseHttpResponse
     */
    public function decryptRecommendJobs(Request $request, BaseHttpResponse $response)
    {
        $result = $this->jobRepository->decryptRecommendJobs($request->token);

        if ($result['success']) {
            return $response->setData($result['data']);
        }

        return $response->setError(true)->setMessage($result['message']);
    }

    //get list is_* of user
    public function getBlacklistAndFllowedUser($user)
    {
        if (isset($user->id)) {
            $user = $this->userRepository->searchOnElasticsearch(null, [
                'ids' => $user->id,
            ]);

            return [
                'applied_jobs' => $user['hits']['hits'][0]['_source']['applied_jobs'] ?? [],
                'blacklist_companies' => $user['hits']['hits'][0]['_source']['blacklist_companies'] ?? [],
            ];
        }

        return [
            'applied_jobs' => [],
            'blacklist_companies' => [],
        ];
    }

    /**
     * Tìm kiếm các job tất cả cty trên elasticsearch.
     *
     * @return JsonResponse
     */
    public function allJobV2(ValidateParams $request)
    {
        $size = $request->page_size ?? 10;

        if (isset($request->region_ids) && !empty($request->region_ids)) {
            $request['region_ids'] = app(ValidateParamsHelper::class)->validateRegionIds($request->region_ids);
        }

        if (isset($request['salary_range']) && !empty($request['salary_range'])) {
            $request['salary_range'] = app(TaxonomyHelper::class)->salaryRange($request['salary_range']);
        }

        if ($request->popular_homepage && $request->popular_homepage == true) {
            $jobs = $this->popularForCompanies($request->page ?? 1, $request->page_size ?? 10, $request->all());
        } else {
            $skills_str = null;
            if (!empty($request->skills_id)) {
                $taxonomies = app(TaxonomyRepositoryInterface::class)->searchOnElasticsearch(null, [
                    'taxonomy' => 'skills',
                    'ids' => $request->skills_id,
                    'page_size' => $request->page_size ?? 100000,
                ]);

                $taxonomiesCollection = (new ElasticCollection($taxonomies));
                if ($taxonomiesCollection->total() > 0) {
                    $arrText = collect($taxonomiesCollection->hits()['hits'])->map(fn($taxonomy) => $taxonomy['_source']['text_vi'])->all();
                    $skills_str = implode(',', $arrText);
                }
            }

            if (!empty($skills_str)) {
                $request['skills_str'] = strtolower(str_replace(['++', '#'], '', $skills_str));
                unset($request['skills_id']);
            }

            $jobs = $this->jobRepository->searchOnElasticsearch(
                strtolower(str_replace(['++', '#'], '', (string) $request->keyword)),
                array_merge($request->all(), [
                    'status' => Job::STATUS_OPEN,
                ])
            );
        }

        $jobsCollection = (new ElasticCollection($jobs));
        $countJobs = ($request->popular_homepage && $request->popular_homepage == true) ? 1 : $jobsCollection->total();
        $jobsFake = [];
        if ($countJobs > 50 && ((!isset($request->region_ids) && empty($request->region_ids)) || (isset($request->region_ids) && $request->region_ids == 'all'))) {
            $jobsFake = (new FakeDataElastic(
                $this->jobRepository,
                $size,
                $countJobs,
                $request->page
            ))->searchWithElas(
                strtolower(str_replace(['++', '#'], '', (string) $request->keyword)),
                array_merge($request->all(), [
                    'status' => Job::STATUS_OPEN,
                ])
            );
        }

        if (isset($jobsFake['data'])) {
            $jobs['hits']['hits'] = array_merge($jobsFake['data'], $jobs['hits']['hits']);
            unset($jobsFake['data']);
        }

        if (isset($request->email)) {
            $userResume = app(\Modules\User\Repositories\Contracts\UserRepositoryInterface::class)->firstOrCreate([
                'email' => $request->email,
            ]);

            $recommendJobs = urlencode(encrypt([
                'uuid' => $userResume->uuid,
                'job_ids' => collect($jobs['hits']['hits'])->pluck('_source.id')->toArray(),
                'event' => 'EventSuggestJobs',
            ]));
        }

        /**
         * @var string $recommendJobs
         * @var array $jobsFake
         *
         * @return JobCollection
         */
        return JobCollection::fromElasticsearch($jobs, $size)
            ->nonAuth(true)
            ->dataFake(isset($jobsFake['totalFake']) ? $jobsFake : [])
            ->recommendJobs($recommendJobs ?? '')
            ->response()
            ->setStatusCode(200);
    }

    /**
     * @param  Request  $request
     * @param $id
     *
     * @return JsonResponse
     */
    public function announcements(Request $request, $id)
    {
        $size = $request->page_size ?? 10;

        $announcements = app(AnnouncementInterface::class)->searchOnElasticsearch(
            null,
            [
                'model_id' => $id,
                'model_type' => \Modules\Job\Entities\Job::class,
                'type' => 'status_job_announcement',
            ]
        );

        return StatusJobAnnouncementCollection::fromElasticsearch($announcements, $size)
            ->response()
            ->setStatusCode(200);
    }

    public function getJobsForFB()
    {
        $csvStr = [[
            'id',
            'title',
            'description',
            'availability',
            'condition',
            'price',
            'link',
            'image_link',
            'brand',
        ]];
        $jobs = $this->jobRepository->searchOnElasticsearch(
            '',
            [
                'page_size' => 10000,
                'status' => Job::STATUS_OPEN,
            ]
        );

        $csvStr = array_merge(
            $csvStr,
            collect($jobs['hits']['hits'])
                ->map(function ($item) {
                    $item = $item['_source'];

                    // Handle salary
                    $salary = '';
                    if ($item['salary']['is_negotiable']) {
                        $salary = '1 USD';
                    } else {
                        $salary = number_format($item['salary']['max'] > 0 ? $item['salary']['max'] : $item['salary']['min']) . ' ' . $item['salary']['currency'];
                    }

                    // Handle image
                    $company = Job::query()->find($item['id'])->company;
                    $image = $company->image_cover->first();
                    if (is_null($image)) {
                        $image = $company->image_galleries->first();
                    }

                    return [
                        $item['id'],
                        $item['title'],
                        'Ứng tuyển ngay',
                        'in stock',
                        'new',
                        $salary,
                        $item['detail_url'],
                        optional($image)->getUrl() ?? 'https://cdn.topdev.vn/v4/assets/images/company/cover_5.webp',
                        $item['company']['display_name'],
                    ];
                })->toArray()
        );

        $csv = Writer::createFromString('');
        $csv->insertAll($csvStr);

        return $csv->toString();
    }

    public function getJobsForGG()
    {
        $csvStr = [[
            'Job ID',
            'Location ID',
            'Title',
            'Final URL',
            'Image URL',
            'Subtitle',
            'Description',
            'Category',
            'Contextual keywords',
            'Salary',
        ]];
        $jobs = $this->jobRepository->searchOnElasticsearch(
            '',
            [
                'page_size' => 10000,
                'status' => Job::STATUS_OPEN,
            ]
        );

        $csvStr = array_merge(
            $csvStr,
            collect($jobs['hits']['hits'])
                ->map(function ($item) {
                    $item = $item['_source'];

                    return [
                        $item['id'],
                        $item['_source']['addresses'][0] ?? 'Vietnam',
                        $item['title'],
                        'https://topdev.vn/viec-lam/' . $item['slug'] . '-' . $item['id'],
                        Helpers::imagesProxyResize($item['company']['image_logo'], 300, 300),
                        'Ứng tuyển ngay',
                        'Lương cao rất hấp dẫn',
                        $item['job_levels_str'],
                        $item['skills_str'],
                        $item['salary']['is_negotiable'] ? 'Lương cao rất hấp dẫn' : $item['salary']['value'],
                    ];
                })->toArray()
        );

        $csv = Writer::createFromString('');
        $csv->insertAll($csvStr);

        return $csv->toString();
    }

    /**
     * @param Request $request
     * @param int $id
     * @return AnonymousResourceCollection
     */
    public function recommendJobs(Request $request, int $id)
    {
        $device = $request->get('device') ?? DeviceConstant::DEVICE_DESKTOP;
        $limit = Job::LIMIT_RECOMMEND_JOB;

        if (
            $request->has('user_agent')
            && (
                str_contains($request->user_agent, 'CFNetwork')
                || str_contains($request->user_agent, 'okhttp')
            )
            || $device == DeviceConstant::DEVICE_MOBILE
        ) {
            $limit = Job::LIMIT_RECOMMEND_JOB_MOBILE;
        }

        $topSimilarJobs = Job::query()->find($id)
            ->recommendSimilarJobs()
            ->take($limit)
            ->with([
                'job_information',
                'company',
                'addresses',
                'taxonomies.term'
            ])->get();
        return JobRecommendResource::collection($topSimilarJobs);
    }

    /**
     * Perform search in elastic for section "Jobs fit for you" in the search page
     * If logged resume having technical skills that match with jobs's skills then get it
     * Otherwise, get open newest jobs
     *
     * @return JsonResponse
     */
    public function fitJobs()
    {
        $user = auth('web')->user();
        $totalItems = 5;
        $fitJobParams = [
            'status' => Job::STATUS_OPEN,
            'ordering' => 'random',
            'page_size' => $totalItems
        ];

        // If user request with resume type then have other logics
        if ($user && $user->type == User::RESUME_TYPE && $user->userProfile) {
            $technicalSkills = collect($user->userProfile->skills['technical_skills'] ?? [])->pluck('skill_name')->toArray();
            if (count($technicalSkills)) {
                $jobs = $this->jobRepository->searchOnElasticsearch(
                    null,
                    array_merge($fitJobParams, [
                        'ordering' => 'best_match_score',
                        'required_skills_arr' => $technicalSkills,
                        'optional_skills_arr' => $technicalSkills,
                    ])
                );
                if (isset($jobs['hits']['total']['value']) && ($total = $jobs['hits']['total']['value']) > 0) {
                    // Need fill up to 5 items if item based on skill is less than 5 records
                    if ($total < $totalItems) {
                        $fitJobParams['page_size'] = $totalItems - $total;
                        $newJobs = $this->jobRepository->searchOnElasticsearch(null, $fitJobParams);
                        $jobs['hits']['total']['value'] = $jobs['hits']['total']['value'] + $totalItems - $total;
                        $jobs['hits']['hits'] = array_merge($jobs['hits']['hits'], $newJobs['hits']['hits'] ?? []);
                    }
                    return JobCollection::fromElasticsearch($jobs, $totalItems)
                        ->nonAuth(true)
                        ->response()
                        ->setStatusCode(200);
                }
            }
        }

        // If user logged in but nothing data that match with user profile skills
        // Need search open jobs
        return JobCollection::fromElasticsearch($this->jobRepository->searchOnElasticsearch(null, $fitJobParams), $totalItems)
            ->nonAuth(true)
            ->response()
            ->setStatusCode(200);
    }

    public function publish(Request $request)
    {
        $jobId = $request->job_id;
        $job = Job::find($jobId);

        if (!$job instanceof Job) {
            return response()->json([
                'message' => 'Job not found',
                'success' => false,
            ], 404);
        }

        if ($job->status != Job::STATUS_REVIEW) {
            return response()->json([
                'message' => 'Job is not in review status',
                'success' => false,
            ], 400);
        }

        $this->dispatch(new PublishJobProcess($job, [
            'status' => Job::STATUS_OPEN,
        ], $request->user(), $request->method()));

        return response()->json([
            'message' => 'Job published successfully',
            'success' => true,
        ], 200);
    }

    public function availableLocations()
    {
        $locations = collect($this->jobRepository->availableLocations());

        $locations = VietnamArea::whereIn('code', $locations->pluck('key'))->get();

        return AreaResource::collection($locations)
            ->response()
            ->setStatusCode(200);
    }
}
