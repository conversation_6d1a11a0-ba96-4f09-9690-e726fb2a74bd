<?php

namespace Modules\Job\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Job\Services\JobCategoryService;
use Modules\Job\Transformers\JobCategoryResource;

class JobCategoryController extends Controller
{
    protected JobCategoryService $jobCategoryService;

    public function __construct(JobCategoryService $jobCategoryService)
    {
        $this->jobCategoryService = $jobCategoryService;
    }

    public function getByAllCategoryType(): JsonResponse
    {
        $jobCategories = $this->jobCategoryService->getAllWithCategoryType();

        return response()->json([
            'status' => true,
            'message' => 'Get all job categories successfully',
            'data' => JobCategoryResource::collection($jobCategories),
        ]);
    }

    public function getAllJobCategory(): JsonResponse
    {
        $jobCategories = $this->jobCategoryService->getAllJobCategory();

        return response()->json([
            'status' => true,
            'message' => 'Get all job categories successfully',
            'data' => JobCategoryResource::collection($jobCategories),
        ]);
    }

    public function getRolesByJobCategoryId(int $jobCategoryId): JsonResponse
    {
        $roles = $this->jobCategoryService->getRolesByJobCategoryId($jobCategoryId);

        return response()->json([
            'status' => true,
            'message' => 'Get all roles by job category id successfully',
            'data' => JobCategoryResource::collection($roles),
        ]);
    }
}
