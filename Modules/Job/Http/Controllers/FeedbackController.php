<?php

namespace Modules\Job\Http\Controllers;

use App\Helpers\CrmApi;
use App\Http\Controllers\Controller;
use Modules\Job\Entities\Job;
use Modules\Job\Http\Requests\FeedbackSubmitRequest;

class FeedbackController extends Controller
{
    public function submit(FeedbackSubmitRequest $request)
    {
        $jobId = $request->validated('job_id');
        $job = Job::query()->find($jobId);

        if (!$job instanceof Job) {
            return response()->json([
                'message' => 'Job not found',
                'success' => false,
            ], 404);
        }

        if ($job->status !== Job::STATUS_REVIEW) {
            return response()->json([
                'message' => 'Job is not in review status',
                'success' => false,
            ], 400);
        }

        $data = [
            'subject' => 'Feedback template JD (Job #' . $job->id . ')',
            'department' => 1,
            'job_id' => $job->id,
            'service' => 7, // value: "Feedback"
            'type_id' => 1, // ticket
            'ams_company_id' => $job->owned_id,
            'message' => "Mã template: $job->template_slug \n" . $request->validated('content'),
        ];

        return app(CrmApi::class)->createCrmTicket($data);
    }
}
