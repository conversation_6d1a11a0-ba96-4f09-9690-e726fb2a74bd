<?php

namespace Modules\Job\Http\Requests;

use App\Helpers\Helpers;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Modules\Company\Repositories\Contracts\CompanyRepositoryInterface;

class JobSearchRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            // Search & Filters
            'keyword' => 'nullable',
            'job_categories_ids' => 'nullable|regex:' . config('validate.params.ids'),
            'job_roles_ids' => 'nullable|regex:' . config('validate.params.ids'),
            'salary_min' => 'nullable|integer|min:0',
            'salary_max' => 'nullable|integer|gte:salary_min',
            'benefit_ids' => 'nullable|regex:' . config('validate.params.ids'),
            'skills_id' => 'nullable|regex:' . config('validate.params.ids'),
            'job_levels_ids' => 'nullable|regex:' . config('validate.params.ids'),
            'job_types_ids' => 'nullable|regex:' . config('validate.params.ids'),
            'contract_types_ids' => 'nullable|regex:' . config('validate.params.ids'),
            'company_size_ids' => 'nullable|regex:' . config('validate.params.ids'),
            'company_industry_ids' => 'nullable|regex:' . config('validate.params.ids'),

            // Fields
            'fields' => 'nullable|array',

            // Locale
            'locale' => 'nullable|in:en_US,vi_VN',

            // Sort & Pagination
            'ordering' => 'nullable',
            'page' => 'nullable|integer|min_digits:1',
            'page_size' => 'nullable|integer|min_digits:1'
        ];
    }

    public function messages()
    {
        return [
            // Search & Filters
            'job_categories_ids.regex' => 'The format of job_categories_ids isn\'t correct',
            'job_roles_ids.regex' => 'The format of job_roles_ids isn\'t correct',
            'salary_min.min' => 'The value of salary_min must be greter than or equal 1',
            'salary_max.gte' => 'The value of salary_max must be greter than or equal salary_min',
            'benefit_ids.regex' => 'The format of benefit_ids isn\'t correct',
            'skills_id.regex' => 'The format of skills_id isn\'t correct',
            'experiences_id.regex' => 'The format of experiences_id isn\'t correct',
            'job_types_ids.regex' => 'The format of job_types_ids isn\'t correct',
            'contract_types_ids.regex' => 'The format of contract_types_ids isn\'t correct',
            'company_size_ids.regex' => 'The format of company_size_ids isn\'t correct',
            'company_industry_ids.regex' => 'The format of company_industry_ids isn\'t correct',

            // Fields
            'fields.regex' => 'The format of fields isn\'t correct',
            // Locale
            'locale.in' => 'Local must be one of en_US or vi_VN',
        ];
    }

    public function getLocationFromId() {
        $locationIds = explode(',', $this->get('region_ids') ?? '');
        if ($locationIds) {
            $locations = location_list();
            return collect($locations)
                ->whereIn('id', $locationIds)
                ->map(fn($region) => [
                    'id' => $region['id'],
                    'name' => $region[app()->getLocale() == 'en' ? 'text_en' : 'text'],
                ])
                ->values();
        }

        return collect([]);
    }

    public function getMetaDataFromRequest()
    {
        $taxonomyKeys = collect($this->only([
                'benefit_ids',
                'skills_id',
                'experiences_id',
                'job_types_ids',
                'contract_types_ids',
                'company_size_ids',
                'company_industry_ids',
            ]))
            ->values()
            ->join(',')
        ;

        $taxonomiesIds = array_unique(explode(',', $taxonomyKeys));
        return collect(taxonomies())->whereIn('id', $taxonomiesIds)->values();
    }
}
