<?php

namespace Modules\Job\Http\Requests;

use App\Helpers\Helpers;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Modules\Company\Entities\Company;
use Modules\Company\Repositories\Contracts\CompanyRepositoryInterface;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;

class ItJobRequest extends FormRequest
{
    protected $keyName = 'keyword';

    protected $taxonomies;

    protected $keyword;

    protected $companies;

    public const FRESHER_SEARCH_PAGE_NAME = [
        'vi-fresher-search-page', 'en-fresher-search-page',
    ];

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $this->keyword = str_replace(' ', '-', (string) $this->input($this->keyName));

        return [
            'company' => 'nullable|regex:' . config('validate.params.ids'),
            'except_ids' => 'nullable|regex:' . config('validate.params.ids'),
            'skills_id' => 'nullable|regex:' . config('validate.params.ids'),
            'ids' => 'nullable|regex:' . config('validate.params.ids'),
            'page' => 'nullable|integer|min:1',
            'expire_from' => 'nullable|date_format:Y-m-d H:i:s',
            'expire_to' => 'nullable|date_format:Y-m-d H:i:s',
            'email' => 'nullable|email',
        ];
    }

    public function messages()
    {
        return [
            'company.regex' => "The format of company isn't correct?",
            'except_ids.regex' => "The format of except_ids isn't correct?",
            'skills_id.regex' => "The format of skills_id isn't correct?",
            'ids.regex' => "The format of ids isn't correct?",
            'page.integer' => "The format of page isn't correct?",
            'page.min' => 'Page require min is 1',
            'expire_from.date_format' => "The format of expire_from isn't correct?",
            'expire_to.date_format' => "The format of expire_to isn't correct?",
            'email.email' => "The format of email isn't correct?",
        ];
    }

    public function isNotFresherPage()
    {
        return !$this->isFresherPage();
    }

    public function isFresherPage()
    {
        if ($this->isFresherSearchPage()) {
            return true;
        }

        if ($this->hasFresherInfix()) {
            return true;
        }

        return false;
    }

    public function isFresherSearchPage()
    {
        return $this->keyword === 'fresher';
    }

    public function hasFresherInfix()
    {
        return Str::contains($this->keyword, ['-fresher-k', 'fresher']);
    }

    public function getItJobFilter()
    {
        return array_filter([
            'region_ids' => $this->getLocationIdFromSlug(),
            'keyword' => $this->keywordSearch(),
            // 'level_str' => $job_levels->pluck('text_vi')->implode(' '),
            // 'skills_id' => $this->isFresherPage() ? null : $skill_str, //Nếu fresher page thì ko cần search skill string
            // 'job_type_str' => $job_types->pluck('text_vi')->implode(' '),
            // 'company_ids' => $company_ids->all(),
            // 'level_ids' => $job_levels->pluck('id')->all(),

            // //For fresher page
            // 'features' => $this->isFresherPage() ? 'fresherpage' : null,
            // 'job_features' => $this->isFresherPage() ? 'fresherpage' : null,
            // 'skills_id_or' => $this->isFresherPage() ? $skill_ids : null,
        ]);
    }

    public function isSearching()
    {
        return !empty($this->input($this->keyName));
    }

    /**
     * @return Collection
     */
    public function guessUserSearchTaxonomies(): Collection
    {
        //Để lần sau ko phải detect taxonomies nữa
        if ($this->taxonomies) {
            return $this->taxonomies;
        }

        return $this->taxonomies = collect(taxonomies())->filter(
            fn ($taxonomy) => in_array($taxonomy['id'], $this->getTaxonomiesIdFromSlug())
        )->values();
    }

    public function getJobLevelsFromSlug(): Collection
    {
        $keyword = $this->keyword;

        /*
         * Loại case /it-jobs/{levels}-k
         * Nhưng case topdev.vn/it-jobs/intern+fresher-k vẫn đúng
         */
        if (Str::endsWith($keyword, '-k') && !Str::contains($keyword, '+')) {
            return collect();
        }

        /**
         * it-jobs/intern+php-fresher-kt1
         * Sẽ loại những char nằm trước dấu +
         * Dấu + được định nghĩa tách phần keyword và slug của taxonmies, location.
         */
        // $keyword = Str::of($keyword)->explode('+')->last();
        $keyword = collect(explode('+', (string) $keyword))->last();

        return collect(job_levels_list())->filter(
            fn ($level) => Str::contains($keyword, $level['slug'])
        )->values();
    }

    public function guessUserSearchJobLevels(): Collection
    {
        return collect(job_levels_list())->filter(
            fn ($level) => Str::contains($this->getKeywordSearch(), $level['slug'])
        )
            ->merge($this->getJobLevelsFromSlug())
            ->values();
    }

    public function guessUserSearchJobTypes(): Collection
    {
        return collect(job_types_list())->filter(
            fn ($level) => Str::contains($this->getKeywordSearch(), $level['slug'])
        )
            ->merge($this->getJobTypesFromSlug())
            ->values();
    }

    public function guessUserSearchContractTypes(): Collection
    {
        return collect(contract_types_list())->filter(
            fn ($level) => Str::contains($this->getKeywordSearch(), $level['slug'])
        )
            ->merge($this->getContractTypesFromSlug())
            ->values();
    }

    public function guessUserSearchCompanies()
    {
        if ($this->companies) {
            return $this->companies;
        }

        $params = [
            'ids' => implode(',', $this->getCompanyIdsFromSlug()),
            'page_size' => 10000,
            'page' => 1,
        ];

        $companies = app(CompanyRepositoryInterface::class)->searchOnElasticsearch(null, $params, ['id', 'display_name']);
        $this->companies = collect($companies['hits']['hits'])->pluck('_source');

        return $this->companies;
    }

    public function guessUserSearchKeyword()
    {
        $normalizeKeywords = $this->multiExplode([' ', ',', ', '], $this->getKeywordSearch());

        $jobTypes = $this->guessUserSearchJobTypes();
        $jobLevels = $this->guessUserSearchJobLevels();
        $contractTypes = $this->guessUserSearchContractTypes();

        $exceptKeywords = $jobTypes->merge($jobLevels)->merge($contractTypes)
            ->pluck('slug')->all();

        return collect($normalizeKeywords)->reject(fn($keyword) => in_array($keyword, $exceptKeywords))->implode(' ');
    }

    //Regex province_id from slug.
    public function getLocationIdFromSlug()
    {
        $query_values = Helpers::getLastValueStr($this->keyword);
        preg_match_all('/l([^l\n]+)/', (string) $query_values, $cid);

        return $cid[1][0] ?? '';
    }

    //Get province name from slug
    public function getProvinceNameFromSlug()
    {
        $province_list = location_list();
        $location_id = $this->getLocationIdFromSlug();

        return collect($province_list ?? [])->where('id', $location_id)->first()[app()->getLocale() == 'en' ? 'text_en' : 'text'] ?? '';
    }

    //Regex taxonomies ids from slug.
    public function getTaxonomiesIdFromSlug(): array
    {
        $query_values = Helpers::getLastValueStr($this->keyword);
        preg_match_all('/\bkt([^l\n]+)/', (string) $query_values, $tax_ids);

        return $this->extractIdsFromMatches(...$tax_ids);
    }

    //Regex company ids from slug.
    public function getCompanyIdsFromSlug(): array
    {
        $query_values = Helpers::getLastValueStr($this->keyword);
        preg_match_all('/c([^l\n]+)/', $query_values, $company_ids);

        return $this->extractIdsFromMatches(...$company_ids);
    }

    public function getSkillsFromSlug(): Collection
    {
        return $this->guessUserSearchTaxonomies()
            ->filter(fn ($t) => $t['taxonomy'] === 'skills')
            ->values();
    }

    public function getIndustriesFromSlug(): Collection
    {
        return $this->guessUserSearchTaxonomies()
            ->filter(fn ($t) => $t['taxonomy'] === 'industries')
            ->values();
    }

    public function getJobTypesFromSlug(): Collection
    {
        return $this->guessUserSearchTaxonomies()
            ->filter(fn ($t) => $t['taxonomy'] === 'job_types')
            ->values();
    }

    public function getSalaryFromSLug(): Collection
    {
        return $this->guessUserSearchTaxonomies()
            ->filter(fn ($t) => $t['taxonomy'] === 'salary_range')
            ->values();
    }

    public function getContractTypesFromSlug(): Collection
    {
        return $this->guessUserSearchTaxonomies()
            ->filter(fn ($t) => $t['taxonomy'] === 'contract_types')
            ->values();
    }

    public function getExperienceYearsFromSLug(): Collection
    {
        return $this->guessUserSearchTaxonomies()
            ->filter(fn ($t) => $t['taxonomy'] === 'experiences')
            ->values();
    }

    public function getKeywordSearch(): string
    {
        if (
            empty($this->getTaxonomiesIdFromSlug())
            && empty($this->getLocationIdFromSlug())
            && $this->getJobLevelsFromSlug()->isEmpty()
            && $this->getJobTypesFromSlug()->isEmpty()
            && $this->getExperienceYearsFromSLug()->isEmpty()
            && empty($this->getCompanyIdsFromSlug())
        ) {
            return collect(Helpers::getListKeyWord($this->keyword, null))->implode(',');
        } elseif (str_contains((string) $this->keyword, '+')) {
            $keyword_str = explode('+', (string) $this->keyword);

            return str_replace('-', ' ', $keyword_str[0]);
        }

        return '';
    }

    //Get skill name search
    public function getSkillStrFromSlug()
    {
        return $this->getSkillsFromSlug()
            ->pluck('text_en')
            ->implode(', ') ?? 'IT';
    }

    public function keywordSearch()
    {
        Helpers::getListKeyWord($this->getSearchSlug(), null, $this->isFresherPage());
    }

    public function getSearchSlug()
    {
        return $this->input($this->keyName);
    }

    public function multiExplode($delimiters, $string)
    {
        $ready = str_replace($delimiters, $delimiters[0], (string) $string);
        $launch = explode($delimiters[0], $ready);

        return $launch;
    }

    //Get skill name search
    public function getCompanyStrFromSlug()
    {
        return $this->guessUserSearchCompanies()
            ->pluck('display_name')
            ->implode(', ');
    }

    /**
     * Extract all ids from matches if match ids have data
     *
     * @param mixed $matchString 1st match result of the regex
     * @param mixed $matchIds ids match result of the regex
     * @return array
     */
    private function extractIdsFromMatches($matchString, $matchIds): array
    {
        return isset($matchIds[0]) ? explode(',', $matchIds[0]) : [];
    }
}
