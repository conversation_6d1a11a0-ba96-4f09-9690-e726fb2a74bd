<?php

namespace Modules\Job\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ApplicantCaseRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $case = config('validate.params.apply_cv.case');
        if (!auth('api')->check()) {
            // unset($case[1]);
            return [
                'display_name' => 'nullable|max:50',
                'email' => 'required|email|max:255',
                'phone' => [
                    'nullable',
                    'regex:/^(0|84)(2(0[3-9]|1[0-6|8|9]|2[0-2|5-9]|3[2-9]|4[0-9]|5[1|2|4-9]|6[0-3|9]|7[0-7]|8[0-9]|9[0-4|6|7|9])|3[2-9]|5[2|5|6|8|9]|7[0|6-9]|8[0-9]|9[0-4|6-9])([0-9]{7})$/',
                ],
                'job_id' => 'required|regex:' . config('validate.params.ids'),
                // 'files_cv' => 'nullable|mimes:doc,docx,xlsx,pdf|max:5119',
                'case' => 'required|in:' . implode(',', $case),
                // 'media_id' => 'nullable|integer|min:1',
                'cvbuilder_id' => 'nullable|regex:' . config('validate.params.user_resume.id'),
            ];
        }

        // unset($case[0]);
        return [
            'job_id' => 'required|regex:' . config('validate.params.ids'),
            // 'files_cv' => 'nullable|mimes:doc,docx,xlsx,pdf|max:5119',
            'case' => 'required|in:' . implode(',', $case),
            // 'media_id' => 'nullable|integer|min:1',
            'cvbuilder_id' => 'nullable|regex:' . config('validate.params.user_resume.id'),
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {
        return [
            'email.email' => "The format of field email isn't correct?",
            'email.max' => 'The email is too long.',
            'email.required' => 'The field email is required?',
            'display_name.max' => 'The name is too long.',
            'display_name.required' => 'Please enter your name.',
            'job_id.regex' => "The format of field job_id isn't correct?",
            'job_id.required' => 'The field job_id is required?',
            'cv_file.mimes' => "Type of file isn't correct?",
            'case.in' => "The format of the case isn't correct?",
            'case.required' => 'The field case is required?',
            // 'media_id.integer' => "The field media id isn't correct?",
            // 'media_id.min' => "The field media_id value isn't correct?",
            'cvbuilder_id.regex' => "The format of field cvbuilder_id isn't correct?",
        ];
    }
}
