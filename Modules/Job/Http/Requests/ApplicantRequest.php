<?php

namespace Modules\Job\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ApplicantRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if (!auth('api')->check()) {
            return [
                'display_name' => 'nullable|max:50',
                'email' => 'nullable|email|max:255',
                'phone' => 'nullable|phone',
                'job_id' => 'nullable',
                'cv_file' => 'nullable|mimes:doc,docx,xlsx,pdf|max:5120',
            ];
        }

        return [];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {
        return [
            'email.email' => "The format of the email address isn't correct?",
            'email.max' => 'The email is too long.',
            'display_name.max' => 'The name is too long.',
            'display_name.required' => 'Please enter your name.',
        ];
    }
}
