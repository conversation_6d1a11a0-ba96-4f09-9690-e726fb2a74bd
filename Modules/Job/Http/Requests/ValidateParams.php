<?php

namespace Modules\Job\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ValidateParams extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            // 'region_ids' => 'nullable|regex:'.config('validate.params.company.region_ids'),
            'company' => 'nullable|regex:' . config('validate.params.ids'),
            'except_ids' => 'nullable|regex:' . config('validate.params.ids'),
            'skills_id' => 'nullable|regex:' . config('validate.params.ids'),
            'ids' => 'nullable|regex:' . config('validate.params.ids'),
            'page' => 'nullable|integer|min:1',
            'expire_from' => 'nullable|date_format:Y-m-d H:i:s',
            'expire_to' => 'nullable|date_format:Y-m-d H:i:s',
            'email'=> 'nullable|email',
        ];
    }

    public function messages()
    {
        return [
            // 'region_ids.regex' => "The format of region_ids isn't correct?",
            'company.regex' => "The format of company isn't correct?",
            'except_ids.regex' => "The format of except_ids isn't correct?",
            'skills_id.regex' => "The format of skills_id isn't correct?",
            'ids.regex' => "The format of ids isn't correct?",
            'page.integer' => "The format of page isn't correct?",
            'page.min' => 'Page require min is 1',
            'expire_from.date_format' => "The format of expire_from isn't correct?",
            'expire_to.date_format' => "The format of expire_to isn't correct?",
            'email.email' => "The format of email isn't correct?",
        ];
    }
}
