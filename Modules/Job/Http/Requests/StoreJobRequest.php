<?php

namespace Modules\Job\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreJobRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => 'required|max:255',
            //'content' => 'required'
        ];
    }

    public function author()
    {
        return $this->user();
    }

    public function metas()
    {
        return $this->only(
            app(Job::class)->scopeMeta
        );
    }

    public function terms()
    {
        return $this->only(
            app(Job::class)->scopeTerm
        );
    }

    public function messages()
    {
        return [

        ];
    }

    /**
     * The data to be validated should be processed as JSON.
     * @return mixed
     */
    public function validationData()
    {
        return $this->json()->all();
    }
}
