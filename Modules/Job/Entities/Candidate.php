<?php

namespace Modules\Job\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use Lara<PERSON>\Scout\Searchable;
use Modules\File\Entities\Media;
use Modules\User\Entities\User;

class Candidate extends Model
{
    use Searchable;

    public const CANDIDATE_WAITING = 0; // -> Chờ xử lý
    public const CANDIDATE_READY = 1; // -> 1 tiếng sau gửi cv cho khách hàng
    public const CANDIDATE_SPAM = 2; // -> chờ update thêm thông tin
    public const CANDIDATE_NOT_ENOUGH_INFORMATION = 3; //-> reject không đủ điều kiện apply job
    public const CANDIDATE_DRAFT = 4;
    public const CANDIDATE_UNQUALIFIED = 5;

    /**
     * @inheritdoc
     */
    protected $table = 'candidates';

    /**
     * @inheritdoc
     */
    protected $primaryKey = 'id';

    /**
     * @inheritdoc
     */
    protected $fillable = [];

    /**
     * @inheritdoc
     */
    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'candidates_ams_v7';
    }

    /**
     * Get the value used to index the model.
     *
     * @return mixed
     */
    public function getScoutKey()
    {
        return $this->id;
    }

    /**
     * Get status for the model.
     *
     * @return array
     */
    public static function getStatusDescription()
    {
        return [
            static::CANDIDATE_WAITING => 'Waiting',
            static::CANDIDATE_READY => 'Ready',
            static::CANDIDATE_UNQUALIFIED => 'Unqualified',
            static::CANDIDATE_NOT_ENOUGH_INFORMATION => 'Not Enough Infomation',
        ];
    }

    public function cvMedia(): BelongsTo
    {
        return $this->belongsTo(Media::class, 'media_id')->withTrashed();
    }

    public function job(): BelongsTo
    {
        return $this->belongsTo(Job::class, 'job_id');
    }

    /**
     * @return BelongsTo
     */
    public function resume(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resume_id');
    }
}
