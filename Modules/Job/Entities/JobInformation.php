<?php

namespace Modules\Job\Entities;

use App\Traits\Concerns\Salary;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property Salary $salary
 * @property array|null $benefits
 */
class JobInformation extends Model
{
    use SoftDeletes;

    protected $table = 'job_information';

    protected $primaryKey = 'id';

    protected $fillable = [];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $cast = [
        'social_post_content' => 'array',
        'other_supports' => 'array',
    ];

    public function job()
    {
        return $this->belongsTo(Job::class, 'job_id');
    }

    /**
     * Salary attribute of this job.
     *
     * @return Salary
     */
    public function getSalaryAttribute($value)
    {
        return new \App\Traits\Concerns\Salary(
            json_decode($value ?? '[]')
        );
    }

    /**
     * Get benefits attribute.
     *
     * @return array
     */
    public function getBenefitsAttribute($value)
    {
        return array_values(json_decode((string) $value, true) ?: []);
    }

    /**
     * Get languages attribute.
     *
     * @return array
     */
    public function getLanguagesAttribute($value)
    {
        return array_values(json_decode((string) $value, true) ?: []);
    }

    /**
     * Get other supports attribute.
     *
     * @return array
     */
    public function getOtherSupportsAttribute($value)
    {
        return array_values(json_decode((string) $value, true) ?: []);
    }
}
