<?php

namespace Modules\Job\Entities;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Kalnoy\Nestedset\NodeTrait;
use Modules\Job\Enums\JobCategoryType;

/**
 * Modules\Job\Entities\JobCategory
 *
 * @property int $id
 * @property int $sort_order
 * @property string $description
 * @property Carbon $activated_at
 * @property int $parent_id
 * @property JobCategoryType $type
 *
 * @property JobCategoryTranslation[] $jobCategoryTranslations
 * @property JobCategory $category
 * @property JobCategory[] $roles
 *
 * @method static Builder|JobCategory newModelQuery()
 * @method static Builder|JobCategory newQuery()
 * @method static Builder|JobCategory query()
 * @method static Builder|JobCategory whereActivatedAt($value)
 * @method static Builder|JobCategory whereCategoryId($value)
 * @method static Builder|JobCategory whereDescription($value)
 * @method static Builder|JobCategory whereId($value)
 * @method static Builder|JobCategory whereParentId($value)
 * @method static Builder|JobCategory whereSortOrder($value)
 * @method static Builder|JobCategory whereType($value)
 * @method static Builder|JobCategory whereUpdatedAt($value)
 * @method static Builder|JobCategory whereCreatedAt($value)
 * @method static Builder|JobCategory whenLoaded($relation, $callback = null)
 *
 * @method static Builder|JobCategory jobCategoriesEn()
 * @method static Builder|JobCategory jobCategoriesVi()
 * @method static Builder|JobCategory jobRolesEn()
 * @method static Builder|JobCategory jobRolesVi()
 * @method static Builder|JobCategory isCategory()
 * @method static Builder|JobCategory isRole()
 *
 * @property-read string $name_vi
 * @property-read string $meta_title_vi
 * @property-read string $meta_description_vi
 * @property-read string $meta_keywords_vi
 * @property-read string $name_en
 * @property-read string $meta_title_en
 * @property-read string $meta_description_en
 * @property-read string $meta_keywords_en
 */
class JobCategory extends Model implements TranslatableContract
{
    use Translatable;
    use NodeTrait;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'id',
        'sort_order',
        'description',
        'activated_at',
        'parent_id',
        'type',
    ];

    protected $casts = [
        'type' => JobCategoryType::class,
        'activated_at' => 'datetime',
    ];

    protected $orderColumn = 'sort_order';

    /**
     * The attributes that are translatable.
     */
    public $translatedAttributes = [
        'name',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    public function isCategoryType(): bool
    {
        return $this->type->isCategory($this->type->value);
    }

    public function isRoleType(): bool
    {
        return $this->type->isRole($this->type->value);
    }

    /**
     * @return HasMany
     */
    public function roles(): HasMany
    {
        return $this->hasMany(JobCategory::class, 'parent_id')
            ->where('type', JobCategoryType::ROLE->value);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(JobCategory::class, 'parent_id')
            ->where('type', JobCategoryType::CATEGORY->value);
    }

    /**
     * @return HasMany
     */
    public function jobCategoryTranslations(): HasMany
    {
        return $this->hasMany(JobCategoryTranslation::class, 'job_category_id', 'id');
    }

    /**
     * Scope a query to only include categories.
     * E.g.: $query->jobCategoriesEn();
     *
     * @param $query
     *
     * @return void
     */
    public function scopeJobCategoriesEn($query)
    {
        return $query->where('type', JobCategoryType::CATEGORY->value)
            ->with('jobCategoryTranslations')
            ->whereHas('jobCategoryTranslations', function ($query) {
                $query->where('locale', 'en');
            });
    }

    /**
     * Scope a query to only include roles.
     * E.g.: $query->jobCategoriesVi();
     *
     * @param $query
     *
     * @return void
     */
    public function scopeJobCategoriesVi($query)
    {
        return $query->where('type', JobCategoryType::CATEGORY->value)
            ->with('jobCategoryTranslations')
            ->whereHas('jobCategoryTranslations', function ($query) {
                $query->where('locale', 'vi');
            });
    }

    /**
     * Scope a query to only include roles.
     * E.g.: $query->jobRolesEn();
     *
     * @return string
     */
    public function scopeJobRolesEn($query)
    {
        return $query->where('type', JobCategoryType::ROLE->value)
            ->with('jobCategoryTranslations')
            ->whereHas('jobCategoryTranslations', function ($query) {
                $query->where('locale', 'en');
            });
    }

    /**
     * Scope a query to only include roles.
     * E.g.: $query->jobRolesVi();
     *
     * @return string
     */
    public function scopeJobRolesVi($query)
    {
        return $query->where('type', JobCategoryType::ROLE->value)
            ->with('jobCategoryTranslations')
            ->whereHas('jobCategoryTranslations', function ($query) {
                $query->where('locale', 'vi');
            });
    }

    /**
     * Accessor for Get the name in Vietnamese.
     * E.g.: $jobCategory->name_vi
     *
     * @return string
     */
    public function getNameViAttribute(): string
    {
        return $this->jobCategoryTranslations->where('locale', 'vi')->first()?->name ?? '';
    }

    /**
     * Accessor for Get the meta title in Vietnamese.
     * E.g.: $jobCategory->meta_title_vi
     *
     * @return string
     */
    public function getMetaTitleViAttribute(): string
    {
        return $this->jobCategoryTranslations->where('locale', 'vi')->first()?->meta_title ?? '';
    }

    /**
     * Accessor for Get the meta description in Vietnamese.
     * E.g.: $jobCategory->meta_description_vi
     *
     * @return string
     */
    public function getMetaDescriptionViAttribute(): string
    {
        return $this->jobCategoryTranslations->where('locale', 'vi')->first()?->meta_description ?? '';
    }

    /**
     * Accessor for Get the meta keywords in Vietnamese.
     * E.g.: $jobCategory->meta_keywords_vi
     *
     * @return string
     */
    public function getMetaKeywordsViAttribute(): string
    {
        return $this->jobCategoryTranslations->where('locale', 'vi')->first()?->meta_keywords ?? '';
    }

    /**
     * Accessor for Get the meta title in English.
     * E.g.: $jobCategory->meta_title_en
     *
     * @return string
     */
    public function getMetaTitleEnAttribute(): string
    {
        return $this->jobCategoryTranslations->where('locale', 'en')->first()?->meta_title ?? '';
    }

    /**
     * Accessor for Get the meta description in English.
     * E.g.: $jobCategory->meta_description_en
     *
     * @return string
     */
    public function getMetaDescriptionEnAttribute(): string
    {
        return $this->jobCategoryTranslations->where('locale', 'en')->first()?->meta_description ?? '';
    }

    /**
     * Accessor for Get the meta keywords in English.
     * E.g.: $jobCategory->meta_keywords_en
     *
     * @return string
     */
    public function getMetaKeywordsEnAttribute(): string
    {
        return $this->jobCategoryTranslations->where('locale', 'en')->first()?->meta_keywords ?? '';
    }

    /**
     * Accessor for Get the name in English.
     * E.g.: $jobCategory->name_en
     *
     * @return string
     */
    public function getNameEnAttribute(): string
    {
        return $this->jobCategoryTranslations->where('locale', 'en')->first()?->name ?? '';
    }

    public function setNameViAttribute($value)
    {
        $this->setTranslation('name', 'vi', $value);
    }

    public function setNameEnAttribute($value)
    {
        $this->setTranslation('name', 'en', $value);
    }

    public function setMetaTitleViAttribute($value)
    {
        $this->setTranslation('meta_title', 'vi', $value);
    }

    public function setMetaTitleEnAttribute($value)
    {
        $this->setTranslation('meta_title', 'en', $value);
    }

    public function setMetaDescriptionViAttribute($value)
    {
        $this->setTranslation('meta_description', 'vi', $value);
    }

    public function setMetaDescriptionEnAttribute($value)
    {
        $this->setTranslation('meta_description', 'en', $value);
    }

    public function setMetaKeywordsViAttribute($value)
    {
        $this->setTranslation('meta_keywords', 'vi', $value);
    }

    public function setMetaKeywordsEnAttribute($value)
    {
        $this->setTranslation('meta_keywords', 'en', $value);
    }

    public function setTranslation($key, $locale, $value)
    {
        $this->jobCategoryTranslations->where('locale', $locale)->first()?->update([$key => $value]);
    }

    /**
     * Scope a query to only include categories.
     * E.g.: $query->isCategory();
     *
     * @param $query
     *
     * @return void
     */
    public function scopeIsCategory($query)
    {
        $query->where('type', JobCategoryType::CATEGORY->value);
    }

    /**
     * Scope a query to only include roles.
     * E.g.: $query->isRole();
     *
     * @param $query
     *
     * @return void
     */
    public function scopeIsRole($query)
    {
        $query->where('type', JobCategoryType::ROLE->value);
    }
}
