<?php

namespace Modules\Job\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class JobCategoryTranslation extends Model
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'job_category_id',
        'locale',
        'name',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    /**
     * Disable timestamps.
     * @var bool
     */
    public $timestamps = false;

    public function jobCategory(): BelongsTo
    {
        return $this->belongsTo(JobCategory::class);
    }
}
