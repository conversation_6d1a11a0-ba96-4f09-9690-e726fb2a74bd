<?php

namespace Modules\Job\Entities;

use App\Traits\HasStatus;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;
use Laravel\Scout\Searchable;
use Modules\Company\Entities\Company;
use Modules\Job\Enums\JobCategoryType;
use Modules\Job\Traits\HasSimilarJob;
use Modules\Taxonomy\Entities\Taxonomy;
use Modules\Taxonomy\Traits\Taxoable;
use Modules\VietnamArea\Entities\Address;
use Modules\VietnamArea\Traits\AddressHelper;

/**
 * Modules\Job\Entities\Job
 *
 * @method static find(mixed $id)
 * @property int $id
 * @property Collection $skills_name
 * @property Collection $job_types_name
 * @property Company $company
 * @property Carbon $published_at
 * @property string $title
 * @property JobInformation $job_information
 * @property string $detail_url
 * @property mixed $address_region_ids
 * @property mixed $address_region_list
 * @property mixed $address_region_array
 * @property mixed $full_addresses
 * @property string $short_addresses
 * @property mixed $collection_addresses
 * @property string $uuid
 * @property string $slug
 * @property int $status
 * @property string|null $content
 * @property string|null $content_html_desktop
 * @property string|null $content_html_mobile
 * @property string|null $requirements
 * @property string|null $responsibilities
 * @property int|null $owned_id
 * @property int|null $parent_id
 * @property int|null $creator_id
 * @property \datetime|null $expires_at
 * @property string|null $emails_cc
 * @property mixed|null $hackerrank
 * @property string|null $lever_id
 * @property string|null $hot
 * @property string|null $blog_tags
 * @property mixed|null $blog_posts
 * @property string|null $sidebar_image_link
 * @property \datetime|null $refreshed_at
 * @property string|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $crm_invoice_id
 * @property int|null $crm_request_design_id
 * @property string|null $level
 * @property string|null $original_published_at
 * @property-read \Illuminate\Database\Eloquent\Collection|Address[] $addresses
 * @property-read int|null $addresses_count
 * @property-read array $address_locality_array
 * @property-read array $address_locality
 * @property-read string $address_locality_list
 * @property-read array $address_region
 * @property-read \Illuminate\Database\Eloquent\Relations\MorphMany $address_short_region_list
 * @property array $terms
 * @property-read \Illuminate\Database\Eloquent\Collection|Job[] $recommendSimilarJobs
 * @property-read int|null $recommend_similar_jobs_count
 * @property-read \Illuminate\Database\Eloquent\Collection|SimilarJob[] $similar_jobs
 * @property-read int|null $similar_jobs_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Taxonomy[] $taxonomies
 * @property-read int|null $taxonomies_count
 * @property-read Collection $job_template
 * @property-read mixed $template_slug
 * @method static Builder|Job doesntHaveSimilarJobs()
 * @method static Builder|Job hasSimilarJobs()
 * @method static Builder|Job newModelQuery()
 * @method static Builder|Job newQuery()
 * @method static Builder|Job onlyClose()
 * @method static Builder|Job onlyDraft()
 * @method static Builder|Job onlyOpen()
 * @method static Builder|Job onlyReview()
 * @method static Builder|Job query()
 * @method static Builder|Job similarJobIn($jobIds)
 * @method static Builder|Job status($status)
 * @method static Builder|Job whereBlogPosts($value)
 * @method static Builder|Job whereBlogTags($value)
 * @method static Builder|Job whereContent($value)
 * @method static Builder|Job whereCreatedAt($value)
 * @method static Builder|Job whereCreatorId($value)
 * @method static Builder|Job whereCrmInvoiceId($value)
 * @method static Builder|Job whereDeletedAt($value)
 * @method static Builder|Job whereEmailsCc($value)
 * @method static Builder|Job whereExpiresAt($value)
 * @method static Builder|Job whereHackerrank($value)
 * @method static Builder|Job whereHot($value)
 * @method static Builder|Job whereId($value)
 * @method static Builder|Job whereLevel($value)
 * @method static Builder|Job whereLeverId($value)
 * @method static Builder|Job whereOriginalPublishedAt($value)
 * @method static Builder|Job whereOwnedId($value)
 * @method static Builder|Job whereParentId($value)
 * @method static Builder|Job wherePublishedAt($value)
 * @method static Builder|Job whereRefreshedAt($value)
 * @method static Builder|Job whereRequirements($value)
 * @method static Builder|Job whereResponsibilities($value)
 * @method static Builder|Job whereSidebarImageLink($value)
 * @method static Builder|Job whereSlug($value)
 * @method static Builder|Job whereStatus($value)
 * @method static Builder|Job whereTitle($value)
 * @method static Builder|Job whereUpdatedAt($value)
 * @method static Builder|Job whereUuid($value)
 * @method static Builder|Job withAllTerms($terms, ?string $taxonomy = null)
 * @method static Builder|Job withAnyTerms($terms, ?string $taxonomy = null)
 * @method static Builder|Job withTermsName($keyword)
 * @mixin \Eloquent
 */
class Job extends Model
{
    use Searchable;
    use HasSimilarJob;
    use Taxoable;
    use HasStatus;
    use AddressHelper;

    public const FEATURE_ENABLE = 'on';
    public const FEATURE_DISABLE = 'off';

    public const STATUS_DRAFT = 0;
    public const STATUS_CLOSED = 1;
    public const STATUS_REVIEW = 2;
    public const STATUS_OPEN = 3;
    public const LIMIT_RECOMMEND_JOB = 9;
    public const LIMIT_RECOMMEND_JOB_MOBILE = 4;

    // Required skill 70%, Optional skill 30% of total
    public const REQUIRED_SKILL_WEIGHT = ((1/.3) *.7);
    protected $table = 'jobs';

    protected $primaryKey = 'id';

    protected $fillable = [
        'uuid',
        'slug',
        'status',
        'title',
        'content',
        'content_html_desktop',
        'content_html_mobile',
        'requirements',
        'responsibilities',
        'owned_id',
        'parent_id',
        'creator_id',
        'expires_at',
        'emails_cc',
        'hackerrank',
        'lever_id',
        'hot',
        'level',
        'crm_invoice_id',
        'crm_request_design_id',
        'refreshed_at',
        'published_at',
    ];

    protected $casts = [
        'expires_at' => 'datetime:Y-m-d H:i:s',
        'published_at' => 'datetime:Y-m-d H:i:s',
        'refreshed_at' => 'datetime:Y-m-d H:i:s',
    ];

    public $timestamps = true;

    /**
     * The attributes that should be append to native types.
     * Ex: categories_id, categories_name.
     *
     * @var array
     */
    public array $scopeTerm = [
        'skills',
        'job_types',
    ];

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'jobs_ams_v3';
    }

    public function job_information(): HasOne
    {
        return $this->hasOne(JobInformation::class, 'job_id')->withDefault();
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'owned_id')
            ->withoutGlobalScopes()
            ->withDefault();
    }

    /**
     * @return BelongsToMany
     */
    public function addresses(): BelongsToMany
    {
        return $this->belongsToMany(Address::class, 'address_job', 'job_id', 'address_id');
    }

    /**
     * Accessor for the detail_url attribute. (->detail_url)
     *
     * @return string
     */
    public function getDetailUrlAttribute(): string
    {
        if ($this->isOpen() || $this->isClose()) {
            return frontend_url('detail-jobs/' . $this->slug . '-' . $this->getKey());
        }

        return frontend_url('/');
    }

    /**
     * Get skills_name attribute define. (->skills_name)
     *
     * @return mixed|null
     */
    public function getSkillsNameAttribute()
    {
        return $this->termAttribute('skills_name');
    }

    /**
     * Get job_types_name attribute define. (->job_types_name)
     *
     * @return mixed|null
     */
    public function getJobTypesNameAttribute()
    {
        return $this->termAttribute('job_types_name');
    }

    /**
     * Get the name of the "status" column.
     *
     * @return string
     */
    public function getStatusColumn(): string
    {
        return 'status';
    }

    /**
     * Get the value of the "status open".
     *
     * @return int
     */
    public function getStatusOpen(): int
    {
        return static::STATUS_OPEN;
    }

    /**
     * Get the value of the "status close".
     *
     * @return int
     */
    public function getStatusClose(): int
    {
        return static::STATUS_CLOSED;
    }

    /**
     * Get the candidates for the job post.
     */
    public function candidates(): HasMany
    {
       return $this->hasMany(Candidate::class, 'job_id');
    }

    /**
     * Accessor for job_template attribute. (->job_template)
     */
    public function getJobTemplateAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'job_template');
    }

    /**
     * Accessor for template_slug attribute. (->template_slug)
     */
    public function getTemplateSlugAttribute()
    {
        return $this->job_template->pluck('slug')->first();
    }


    public function job_categories(): BelongsToMany
    {
        return $this->belongsToMany(JobCategory::class, 'job_category_job');
    }

    /**
     * Accessor for job_category attribute. (->job_category)
     */
    public function getJobCategoryAttribute()
    {
        return $this->job_categories
            ->where('type', JobCategoryType::CATEGORY)
            ->first();
    }

    /**
     * Accessor for job_category_id attribute. (->job_category_id)
     */
    public function getJobCategoryIdAttribute()
    {
        return $this->job_category?->id;
    }

    /**
     * Accessor for job_category_role_ids attribute. (->job_category_role_ids)
     */
    public function getJobCategoryRoleIdsAttribute()
    {
        return $this->job_categories->where('type', JobCategoryType::ROLE)->pluck('id');
    }
}
