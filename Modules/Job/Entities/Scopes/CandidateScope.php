<?php

namespace Modules\Job\Entities\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class CandidateScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param Builder $builder
     * @param Model $model
     * @return void
     */
    public function apply(Builder $builder, Model $model)
    {
        $builder->with(['media', 'meta', 'resume.media', 'resume.addresses', 'job.taxonomies.term', 'job.meta', 'job.company']);
    }
}
