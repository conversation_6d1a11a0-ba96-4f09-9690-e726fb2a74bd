<?php

namespace Modules\Job\Entities\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class JobScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param Builder $builder
     * @param Model $model
     * @return void
     */
    public function apply(Builder $builder, Model $model)
    {
        $builder->withCount('candidateReady', 'candidateDelivered')->with(['taxonomies.term', 'subscriptions.term', 'features.term', 'addresses', 'meta', 'media', 'company.media', 'followers']);
    }
}
