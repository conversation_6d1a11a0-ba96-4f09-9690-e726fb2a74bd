<?php

namespace Modules\Job\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Job\Entities\Job;

class UpdateJobProcess implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public const HTTP_PUT = 'put';
    public const HTTP_PATCH = 'patch';

    /**
     * Create a new job instance.
     *
     * @return void
     * @param mixed[] $params
     */
    public function __construct(private Job $model, private $params = [], private $author = null, private $method = 'put')
    {
        $this->onConnection(config('queue.ams'));
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
    }
}
