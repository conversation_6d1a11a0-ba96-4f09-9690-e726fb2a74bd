<?php

namespace Modules\Job\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ApplyProcess implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    private $payload = null;
    private $hasLogin = true;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($payload, private $user_resume, private $jobIds, private $case, $hasLogin, private $jobsApplied, private $promotionRewardId = null)
    {
        Log::info('-----ApplyProcess------');
        Log::info($hasLogin);

        $this->payload = (array) $payload;
        $this->hasLogin = $hasLogin;

        $this->onConnection(config('queue.ams'));
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        throw new Exception('Ths job handled in AMS');
    }
}
