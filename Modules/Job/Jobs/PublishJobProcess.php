<?php

namespace Modules\Job\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Job\Entities\Job;

class PublishJobProcess implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @param mixed[] $params
     *
          *@return void
     */
    public function __construct(private Job $model, private readonly array $params = [], private $author = null, private $method = 'put')
    {
        $this->onConnection(config('queue.ams'));
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
    }
}
