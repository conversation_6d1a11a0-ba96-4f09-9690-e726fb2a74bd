<?php

namespace Modules\Job\Enums;

enum JobLevelEnum: string
{
    case PAID = 'paid';
    case FREE = 'free';

    /**
     * Get the values of the enum.
     *
     * @return array<int, JobLevelEnum>
     */
    public static function getValues(): array
    {
        return [
            self::PAID,
            self::FREE,
        ];
    }

    /**
     * Get the values of the enum as strings.
     *
     * @return array<string>
     */
    public static function getValuesAsStrings(): array
    {
        return [
            self::PAID->value,
            self::FREE->value,
        ];
    }
}
