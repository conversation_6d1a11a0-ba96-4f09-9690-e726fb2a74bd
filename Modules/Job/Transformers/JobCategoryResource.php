<?php

namespace Modules\Job\Transformers;

use App\Traits\AddDataResource;
use App\Traits\CustomFieldResource;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Job\Entities\JobCategory;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

/**
 * Modules\Job\Transformers\JobCategoryResource
 *
 * @mixin JobCategory
 */
class JobCategoryResource extends JsonResource
{
    use ElasticCollectionTrait;
    use CustomFieldResource;
    use AddDataResource;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     *
     * @return array
     */
    public function toArray(Request $request): array
    {
        /** @var JobCategory $this */
        return [
            'id' => $this->id,
            'description' => $this->description,
            'type' => $this->type->value,
            'sort_order' => $this->sort_order,
            'roles' => JobCategoryResource::collection($this->whenLoaded('roles')),
            'name' => request('lang') === 'en' ? $this->name_en : $this->name_vi,
            'is_active' => Carbon::now()->lessThan($this->activated_at),
        ];
    }
}
