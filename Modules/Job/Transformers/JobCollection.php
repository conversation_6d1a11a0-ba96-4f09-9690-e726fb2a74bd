<?php

namespace Modules\Job\Transformers;

use App\Traits\AddDataCollection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Modules\Announcement\Transformers\StatusJobAnnouncementCollection;
use Modules\Company\Transformers\CompanyCollection;
use Modules\Job\Entities\Job;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class JobCollection extends ResourceCollection
{
    use ElasticCollectionTrait;
    use AddDataCollection;

    protected $dataFake = [];

    protected $nonAuth = false;

    protected $recommendJobs = '';

    public function dataFake($value): static
    {
        $this->dataFake = $value;

        return $this;
    }

    public function nonAuth($value): static
    {
        $this->nonAuth = $value;

        return $this;
    }

    public function recommendJobs($value): static
    {
        $this->recommendJobs = $value;

        return $this;
    }

    /**
     * Transform the resource collection into an array.
     *
     * @param  Request  $request
     *
     * @return AnonymousResourceCollection|ResourceCollection|array
     */
    public function toArray(Request $request): ResourceCollection|AnonymousResourceCollection|array
    {
        if ($this->shouldGetRelation) {
            $this->dataRelation = $this->dataRelation($request->all());
        }

        if (!$this->nonAuth) {
            return JobResource::listDataUser($this->listDataUser)->shouldGetRelation(false)->dataRelation($this->dataRelation)->collection($this->collection);
        }

        return JobV2Resource::shouldGetRelation(false)->dataRelation($this->dataRelation)->collection($this->collection);
    }

    public function withResponse($request, $response)
    {
        $elasticCollection = static::getElasticCollection();
        $aggregations = $elasticCollection->aggregations();

        $res = json_decode($response->getContent(), true);
        if (!empty($this->dataFake) && count($this->dataFake) > 0) {
            $res['meta']['total'] = $this->dataFake['totalFake'];
            $res['meta']['last_page'] = (int) ceil(($this->dataFake['totalFake']) / $res['meta']['per_page']);

            $aggregations = $aggregations->map(function ($aggregation) {
                if (isset($aggregation[0]['doc_count'])) {
                    $aggregation[0]['doc_count'] = (int) ceil($aggregation[0]['doc_count'] * 2.14);
                }

                return $aggregation;
            });
        }

        $json = array_merge([
            '_type' => 'jobs',
            'aggregations' => empty($elasticCollection) ? [] : $aggregations,
        ], $res);

        if (!empty($this->recommendJobs)) {
            $json['recommend_jobs'] = $this->recommendJobs;
        }

        // Unset total job if request not include fake
        if ($request->_f) {
            unset(
                $json['meta'],
                $json['aggregations']['status']
            );
        }

        $response->setContent(json_encode($json));
    }

    private function dataRelation($rq): array
    {
        $data = [];
        $fieldsJob = isset($rq['fields']['job']) ? explode(',', (string) $rq['fields']['job']) : [];

        if (in_array('company', $fieldsJob)) {
            $companyIds = $this->collection->pluck('owned_id')->unique()->all();
            $data['companies'] = $this->getDataRelationModal(
                'company',
                [
                    'ids' => implode(',', $companyIds),
                    'page_size' => 10000,
                ]
            );

            $data['companies'] = CompanyCollection::fromElasticsearch($data['companies'])->shouldGetRelation(false)->keyBy('id');
        }

        if (in_array('candidate', $fieldsJob) && isset($this->listDataUser['applied_jobs']) && !empty($this->listDataUser['applied_jobs'])) {
            $data['candidate'] = $this->getDataRelationModal(
                'candidate',
                [
                    'resume_id' => $this->getUserId(),
                    'job' => implode(',', $this->listDataUser['applied_jobs']),
                    'page_size' => 10000,
                ]
            );

            $data['candidate'] = CandidateCollection::fromElasticsearch($data['candidate'])->keyBy('job.id');
        }

        if (isset($rq['fields']['status_job_announcement'])) {
            $jobIds = $this->collection->pluck('id')->all();
            $data['status_job_announcement'] = $this->getDataRelationModal(
                'status_job_announcement',
                [
                    'model_ids' => implode(',', $jobIds),
                    'model_type' => Job::class,
                    'type' => 'status_job_announcement',
                    'page' => 1,
                    'page_size' => 10000,
                ]
            );

            $data['status_job_announcement'] = StatusJobAnnouncementCollection::fromElasticsearch($data['status_job_announcement'])->groupBy('model_id');
        }

        return $data;
    }
}
