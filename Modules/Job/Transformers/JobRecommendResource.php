<?php

namespace Modules\Job\Transformers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;
use Modules\Job\Entities\Job;

/**
 * Modules\Job\Transformers\JobRecommendResource
 *
 * @mixin Job
 */
class JobRecommendResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     *
     * @return array
     */
    public function toArray(Request $request): array
    {
        $locale = $request->get('locale', 'en_US');
        $jobInformation = $this->job_information;
        $company = $this->company;
        $refreshedAt = $this->refreshed_at ?? Carbon::now();

        return [
            'id' => $this->id,
            'status' => $this->status,
            'title' => $this->title,
            'level' => $this->level,
            'content' => $this->content,
            'owned_id' => $this->owned_id,
            'benefits' => $jobInformation->benefits,
            'requirements' => $this->requirements,
            'responsibilities' => $this->responsibilities,
            'salary' => $jobInformation?->salary->getFormattedSalaryAttribute($locale),
            'company' => $this->formatCompany($company),
            'addresses' => $this->formatAddresses($this->addresses),
            'detail_url' => $this->detail_url,
            'skills_arr' => $this->pluckTaxonomyByName($this->taxonomies, 'skills', 'name'),
            'skills_ids' => $this->pluckTaxonomyByName($this->taxonomies, 'skills', 'id'),
            'job_types_ids' => $this->pluckTaxonomyByName($this->taxonomies, 'job_types', 'id'),
            'job_types_str' => $this->pluckTaxonomyByName($this->taxonomies, 'job_types', 'name'),
            'experiences_ids' => $this->pluckTaxonomyByName($this->taxonomies, 'experiences', 'id'),
            'experiences_str' => $this->pluckTaxonomyByName($this->taxonomies, 'experiences', 'name'),
            'refreshed' => [
                'date' => Carbon::createFromFormat('Y-m-d H:i:s', $refreshedAt)->format('d-m-Y'),
                'datetime' => Carbon::createFromFormat('Y-m-d H:i:s', $refreshedAt)->format('H:i:s d-m-Y'),
                'since' => Carbon::createFromFormat('Y-m-d H:i:s', $refreshedAt)->locale($locale)->diffForHumans(),
            ],
            'slug' => $this->slug,
        ];
    }

    /**
     * Format the company.
     *
     * @param mixed $company
     * @return array
     */
    private function formatCompany($company): array
    {
        return [
            'display_name' => $company->display_name,
            'image_logo' => $company->url_image_logo,
            'slug' => $company->slug,
            'id' => $company->id,
        ];
    }

    /**
     * Format the addresses.
     *
     * @param mixed $addresses
     * @return array
     */
    private function formatAddresses($addresses): array
    {
        return [
            'sort_addresses' => $addresses->pluck('display_name'),
            'full_addresses' => $addresses->pluck('full_address'),
        ];
    }

    /**
     * Pluck the taxonomy values by name.
     *
     * @param mixed $taxonomies
     * @param string $taxonomyName
     * @param string $attribute
     * @return Collection
     */
    private function pluckTaxonomyByName($taxonomies, string $taxonomyName, string $attribute): Collection
    {
        return $taxonomies->where('taxonomy', $taxonomyName)->pluck($attribute);
    }
}
