<?php

namespace Modules\Job\Transformers;

use Illuminate\Http\Resources\Json\JsonResource as Resource;
use Modules\VietnamArea\Transformers\AddressResource;

class JobFromEloquentResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        $locale = $request->locale ?: 'en_US';
        $fields = isset($request->fields['job'])
                    ? explode(',', (string) $request->fields['job']) : [];

        return [
            /*
            |--------------------------------------------------------------------------
            | Info basic
            |--------------------------------------------------------------------------
            */
            'id' => $this->when(in_array('id', $fields), (int) $this->getKey()),
            'title' => $this->when(in_array('title', $fields), (string) $this->title),

            /*
            |--------------------------------------------------------------------------
            | Content
            |--------------------------------------------------------------------------
            */
            'content' => $this->when(in_array('content', $fields), (string) $this->content),
            'benefits' => $this->when(in_array('benefits', $fields), (array) $this->benefits),
            'requirements' => $this->when(in_array('requirements', $fields), (string) $this->requirements),
            'responsibilities' => $this->when(in_array('responsibilities', $fields), (string) $this->responsibilities),
            'recruiment_process' => $this->when(in_array('recruiment_process', $fields), (array) $this->recruiment_process),

            /*
            |--------------------------------------------------------------------------
            | Company
            |--------------------------------------------------------------------------
            */
            $this->mergeWhen(in_array('company', $fields) && $this->relationLoaded('company'), [
                'company' => [
                    'id' => $this->company->getKey(),
                    'display_name' => $this->company->display_name,
                    'description' => $this->company->description,
                    'image_logo' => $this->company->image_logo->toArray(),
                ],
            ]),

            /*
            |--------------------------------------------------------------------------
            | Skill
            |--------------------------------------------------------------------------
            */
            'extra_skills' => $this->when(in_array('extra_skills', $fields), (array) $this->extra_skills_id->all()),
            'best_skills' => $this->when(in_array('best_skills', $fields), (array) $this->skills_id->all()),

            'skills_str' => $this->when(in_array('skills_str', $fields), (string) $this->skills_name->implode(', ')),
            'skills_arr' => $this->when(in_array('skills_arr', $fields), (array) $this->skills_name->all()),
            'skills_ids' => $this->when(in_array('skills_arr', $fields), (array) $this->skills_id->all()),

            'experiences_str' => $this->when(in_array('experiences_str', $fields), (string) $this->experiences_name->implode(', ')),
            'experiences_arr' => $this->when(in_array('experiences_arr', $fields), (array) $this->experiences_id->all()),
            'experiences_ids' => $this->when(in_array('experiences_ids', $fields), (array) $this->experiences_id->all()),

            'job_types_str' => $this->when(in_array('job_types_str', $fields), (string) $this->job_types_name->implode(', ')),
            'job_types_arr' => $this->when(in_array('job_types_arr', $fields), (array) $this->job_types_id->all()),
            'job_types_ids' => $this->when(in_array('job_types_ids', $fields), (array) $this->job_types_id->all()),

            'job_levels_str' => $this->when(in_array('job_levels_str', $fields), (string) $this->job_levels_name->implode(', ')),
            'job_levels_arr' => $this->when(in_array('job_levels_arr', $fields), (array) $this->job_levels_id->all()),
            'job_levels_ids' => $this->when(in_array('job_levels_ids', $fields), (array) $this->job_levels_id->all()),

            /*
            |--------------------------------------------------------------------------
            | Address
            |--------------------------------------------------------------------------
            */
            'addresses' => $this->when(in_array('addresses', $fields), AddressResource::collection($this->whenLoaded('addresses'))),

            /*
            |--------------------------------------------------------------------------
            | Status
            |--------------------------------------------------------------------------
            */
            'status_display' => $this->when(in_array('status_display', $fields), (string) $this->status_display),

            'detail_url' => $this->when(in_array('detail_url', $fields), (string) $this->detail_url),
            'apply_url' => $this->when(in_array('apply_url', $fields), (string) $this->apply_url),
            'job_url' => $this->when(in_array('job_url', $fields), (string) $this->job_url),

            /*
            |--------------------------------------------------------------------------
            |
            |--------------------------------------------------------------------------
            */
            'num_followers' => $this->when(in_array('num_followers', $fields) && $this->hasLogin(), (int) $this->num_followers),
            'num_viewers' => $this->when(in_array('num_viewers', $fields) && $this->hasLogin(), (int) $this->num_viewers),
            'num_candidates' => $this->when(in_array('num_candidates', $fields) && $this->hasLogin(), (int) $this->num_ready_candidates),

            'salary' => $this->when(in_array('salary', $fields), (array) $this->salary),

            'schema_job_posting' => $this->when(in_array('schema_job_posting', $fields), (string) $this->schema_job_posting),

            /*
            |--------------------------------------------------------------------------
            | Datetime
            |--------------------------------------------------------------------------
            */
            'created'  => $this->when(in_array('created', $fields), (array) (empty($this->created_at) ? [] : [
                'date' => $this->created_at->format('d-m-Y'),
                'since' => $this->created_at->locale($locale)->diffForHumans(),
            ])),
            'modified' => $this->when(in_array('modified', $fields), (array) (empty($this->updated_at) ? [] : [
                'date' => $this->updated_at->format('d-m-Y'),
                'since' => $this->updated_at->locale($locale)->diffForHumans(),
            ])),
            'closed' => $this->when(in_array('closed', $fields), (array) (empty($this->expires_at) ? [] : [
                'date' => $this->expires_at->format('d-m-Y'),
                'since' => $this->expires_at->locale($locale)->diffForHumans(),
            ])),
            'published' => $this->when(in_array('published', $fields), (array) (empty($this->published_at) ? [] : [
                'date' => $this->published_at->format('d-m-Y'),
                'since' => $this->published_at->locale($locale)->diffForHumans(),
            ])),

            'applied' => $this->whenPivotLoaded('candidates', fn() => empty($this->pivot->created_at) ? [] : [
                        'date' => $this->pivot->created_at->format('d-m-Y'),
                        'datetime' => $this->pivot->created_at->format('H:i:s d-m-Y'),
                        'since' => $this->pivot->created_at->locale($locale)->diffForHumans(),
                    ]),
        ];
    }

    private function hasLogin()
    {
        return !empty(request()->user());
    }
}
