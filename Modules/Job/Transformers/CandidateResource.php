<?php

namespace Modules\Job\Transformers;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource as Resource;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class CandidateResource extends Resource
{
    use ElasticCollectionTrait;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        $locale = $request->locale ?: 'en_US';
        $fields = isset($request->fields['candidate'])
            ? explode(',', (string) $request->fields['candidate']) : [];

        return [
            // 'id' => $this->when( in_array('id', $fields), (int)$this->id),
            'id' => (int) $this->id,

            'created'  => $this->when(in_array('created', $fields), (empty($this->created_at) ? null : [
                'date' => Carbon::createFromFormat('Y-m-d H:i:s', $this->created_at)->format('d-m-Y'),
                'datetime' => Carbon::createFromFormat('Y-m-d H:i:s', $this->created_at)->format('H:i:s d-m-Y'),
                'since' => Carbon::createFromFormat('Y-m-d H:i:s', $this->created_at)->locale($locale)->diffForHumans(),
            ])),
            'modified' => $this->when(in_array('modified', $fields), (empty($this->updated_at) ? null : [
                'date' => Carbon::createFromFormat('Y-m-d H:i:s', $this->updated_at)->format('d-m-Y'),
                'datetime' => Carbon::createFromFormat('Y-m-d H:i:s', $this->updated_at)->format('H:i:s d-m-Y'),
                'since' => Carbon::createFromFormat('Y-m-d H:i:s', $this->updated_at)->locale($locale)->diffForHumans(),
            ])),

            'confirmed' => $this->when(in_array('modified', $fields), (empty($this->confirmed_at) ? null : [
                'date' => Carbon::createFromFormat('Y-m-d H:i:s', $this->confirmed_at)->format('d-m-Y'),
                'datetime' => Carbon::createFromFormat('Y-m-d H:i:s', $this->confirmed_at)->format('H:i:s d-m-Y'),
                'since' => Carbon::createFromFormat('Y-m-d H:i:s', $this->confirmed_at)->locale($locale)->diffForHumans(),
            ])),

            'delivered' => $this->when(in_array('modified', $fields), (empty($this->delivered_at) ? null : [
                'date' => Carbon::createFromFormat('Y-m-d H:i:s', $this->delivered_at)->format('d-m-Y'),
                'datetime' => Carbon::createFromFormat('Y-m-d H:i:s', $this->delivered_at)->format('H:i:s d-m-Y'),
                'since' => Carbon::createFromFormat('Y-m-d H:i:s', $this->delivered_at)->locale($locale)->diffForHumans(),
            ])),
            'job' => $this->when(in_array('job', $fields), $this->job),
            'resume' => $this->when(in_array('resume', $fields), $this->resume),
            'files_cv' => $this->when(in_array('files_cv', $fields), $this->files_cv),
            'status' => $this->when(in_array('status', $fields), $this->status),
            'status_display' => $this->when(in_array('status_display', $fields), $this->statusDisplay($this->status)),
            'hackerrank' => $this->when(in_array('hackerrank', $fields), [
                'tags' => (array) $this->tags_hackerrank,
                'files' => $this->files_hackerrank,
            ]),
            'is_solving_hackerrank' => $this->when(in_array('is_solving_hackerrank', $fields), (bool) $this->is_solving_hackerrank),
            'system_progress' => $this->when(in_array('system_progress', $fields), $this->transformSystemProgress($this->status_logs)),
            'employer_progress' => $this->when(in_array('employer_progress', $fields), [
                'employer_viewed' => empty($this->employer_viewed_at) ? false : Carbon::createFromFormat('Y-m-d H:i:s', $this->employer_viewed_at)->format('H:i:s d-m-Y'),
            ]),
            'apply_end_of_week' => $this->when(in_array('apply_end_of_week', $fields), ((bool) ($this->apply_end_of_week))),
            'recalled_at' => $this->when(
                in_array('recalled_at', $fields),
                empty($this->recalled_at) ? false : Carbon::createFromFormat('Y-m-d H:i:s', $this->recalled_at)->format('H:i:s d-m-Y')
            ),
            'is_remove_cv' => $this->when(in_array('is_remove_cv', $fields), ((bool) ($this->is_remove_cv))),
        ];
    }

    private function statusDisplay($status = null)
    {
        return match ($status) {
            0 => 'Waiting',
            1 => 'Ready',
            2 => 'Spam',
            3 => 'Not enough infomation',
            4 => 'Draft',
            default => 'Unqualified',
        };
    }

    private function transformSystemProgress($status_logs)
    {
        $status_logs = collect($status_logs);
        if ($status_logs->isEmpty()) {
            return null;
        }

        $statusLogs = $status_logs->sortBy('id')->toArray();
        $lastStatus = end($statusLogs);
        $data['applied_datetime'] = empty($statusLogs[0]['created_at']) ? null : Carbon::createFromFormat('Y-m-d H:i:s', $statusLogs[0]['created_at'])->timestamp;
        $data['verified_datetime'] = empty($lastStatus['created_at']) ? null : Carbon::createFromFormat('Y-m-d H:i:s', $lastStatus['created_at'])->timestamp;
        $data['delivered_text'] = 'Gửi hồ sơ cho Nhà tuyển dụng';
        $data['verified_status'] = true;
        $data['delivered_status'] = $this->hasDelivered();

        switch ($lastStatus['status']) {
            case 1:

                if ($this->hasDelivered()) {
                    $data['delivered_datetime'] = Carbon::createFromFormat('Y-m-d H:i:s', $this->delivered_at)->timestamp;
                } elseif ($this->delivering()) {
                    $data['delivered_datetime'] = 'Đang xử lý';
                } else {
                    $data['delivered_status'] = empty($lastStatus['created_at']) ? false : true;
                    $data['delivered_datetime'] = empty($lastStatus['created_at']) ? 'Đang xử lý' : Carbon::createFromFormat('Y-m-d H:i:s', $lastStatus['created_at'])->timestamp;
                }

                break;
            case 5:
                $data['delivered_datetime'] = empty($lastStatus['created_at']) ? 'Đang xử lý' : Carbon::createFromFormat('Y-m-d H:i:s', $lastStatus['created_at'])->timestamp;
                $data['delivered_status'] = true;
                break;
            case 4:
            case 2:
                $data['delivered_text'] = 'Không gửi hồ sơ cho Nhà tuyển dụng';
                $data['delivered_status'] = true;
                $data['delivered_datetime'] = null;
                break;
            case 3:
                $data['delivered_text'] = 'Gửi hồ sơ cho Nhà tuyển dụng';
                $data['delivered_datetime'] = null;
                $data['delivered_status'] = false;
                break;
            default:
                $data['verified_datetime'] = 'Đang xử lý';
                $data['delivered_datetime'] = 'Đang xử lý';
                $data['verified_status'] = false;
                $data['delivered_status'] = false;
        }

        return [
            'applied' => [
                'text' => 'Ứng tuyển thành công',
                'datetime' => $data['applied_datetime'],
                'datetime_short' => is_int($data['applied_datetime']) ? Carbon::parse($data['applied_datetime'])->format('d-m-Y') : $data['applied_datetime'],
                'status' => true,
                'index' => 1,
            ],
            'verified' => [
                'text' => 'Xác minh hồ sơ',
                'datetime' => $data['verified_datetime'],
                'datetime_short' => is_int($data['verified_datetime']) ? Carbon::parse($data['verified_datetime'])->format('d-m-Y') : $data['verified_datetime'],
                'status' => $data['verified_status'],
                'index' => 2,
            ],
            'delivered' => [
                'text' => $data['delivered_text'],
                'datetime' => $data['delivered_datetime'],
                'datetime_short' => is_int($data['delivered_datetime']) ? Carbon::parse($data['delivered_datetime'])->format('d-m-Y') : $data['delivered_datetime'],
                'status' => $data['delivered_status'],
                'index' => 3,
            ],
        ];
    }

    /**
     * Check if has send candidate to employer.
     *
     * @return bool
     */
    private function hasDelivered()
    {
        if (empty($this->delivered_at)) {
            return false;
        }

        return Carbon::createFromFormat('Y-m-d H:i:s', $this->delivered_at)->isPast();
    }

    /**
     * Check if has send candidate to employer.
     *
     * @return bool
     */
    private function delivering()
    {
        if (empty($this->delivered_at)) {
            return false;
        }

        return Carbon::createFromFormat('Y-m-d H:i:s', $this->delivered_at)->isFuture();
    }
}
