<?php

namespace Modules\Job\Transformers;

use App\Traits\AddDataResource;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource as Resource;
use Modules\Company\Transformers\CompanyResource;
use Modules\Job\Entities\Job;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

/**
 * @mixin Job
 */
class JobV2Resource extends Resource
{
    use ElasticCollectionTrait;
    use AddDataResource;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request): array
    {
        $locale = $request->locale ?: 'en_US';
        $fields = isset($request->fields['job'])
            ? explode(',', (string) $request->fields['job'])
            : [];
        $company = null;

        if (static::$shouldGetRelation) {
            $company = app(\Modules\Company\Repositories\Contracts\CompanyRepositoryInterface::class)
                ->searchOnElasticsearch(null, [
                    'ids' => $this->owned_id ?? null,
                ]);
            $company = ((isset($company['hits']['hits'][0]['_source']) && !empty($company['hits']['hits'][0]['_source'])) ? (CompanyResource::listDataUser(static::$listDataUser)->shouldGetRelation(false)->fromElasticsearch($company)) : null);
        } else {
            $company = isset((static::$dataRelation)['companies'][$this->owned_id]) ? CompanyResource::listDataUser(static::$listDataUser)->shouldGetRelation(false)->collection([(static::$dataRelation)['companies'][$this->owned_id]])->first() : null;
        }

        $locale = $request->get('locale', 'en_US');
        $salary = $this->formatSalary((array) $this->salary, $locale);

        return [
            'id' => $this->when(in_array('id', $fields), (int) $this->id),
            'title' => $this->when(in_array('title', $fields), (string) $this->title),
            'level' => $this->when(in_array('level', $fields), (string) $this->level),
            'company' => $this->when(in_array('company', $fields), $company),

            // 'company' => empty(preg_grep('/^(latest_jobs_)[0-9]+$/i', isset($request->fields['company']) ? explode(",", $request->fields['company']) : [])) ? ((isset($company['hits']['hits'][0]['_source']) && !empty($company['hits']['hits'][0]['_source'])) ? (CompanyResource::fromElasticsearch($company)) : null)

            // : $this->when(in_array('company', $fields), array_merge((array) $this->company, [
            //     'detail_url' => frontend_url('companies/' . ($this->company['slug'] ?? null) . '-' . ($this->company['id'] ?? null)),
            // ])),

            // 'company' => $this->when(in_array('company', $fields) && static::$shouldGetRelation == true,((isset($company['hits']['hits'][0]['_source']) && !empty($company['hits']['hits'][0]['_source'])) ? (CompanyResource::shouldGetRelation(false)->fromElasticsearch($company)) : null)),

            'skills_arr' => $this->when(in_array('skills_arr', $fields), (array) $this->skills_arr),
            'skills_ids' => $this->when(in_array('skills_ids', $fields), (array) $this->skills_ids),

            'addresses' => $this->when(in_array('addresses', $fields), (array) $this->addresses),

            'salary' => $this->when(in_array('salary', $fields), $salary),

            'detail_url' => $this->when(in_array('detail_url', $fields), (string) $this->detail_url),
            'slug' => $this->when(in_array('slug', $fields), (string) $this->slug),

            'features' => $this->when(in_array('features', $fields), (array) $this->features),

            'experiences_ids' => $this->when(in_array('experiences_ids', $fields), (array) $this->experiences_ids),
            'job_levels_ids' => $this->when(in_array('job_levels_ids', $fields), (array) $this->job_levels_ids),

            'expires'  => $this->when(in_array('expires', $fields), (empty($this->expires_at) ? null : [
                'date' => Carbon::createFromFormat('Y-m-d H:i:s', $this->expires_at)->format('d-m-Y'),
                'datetime' => Carbon::createFromFormat('Y-m-d H:i:s', $this->expires_at)->format('H:i:s d-m-Y'),
                'since' => Carbon::createFromFormat('Y-m-d H:i:s', $this->expires_at)->locale($locale)->diffForHumans(),
            ])),

        ];
    }

    private function formatSalary($salary, $locale)
    {
        if (!$this->skipInvisibleSalary()) {
            if ($this->hasLogin() == false) {
                if ($salary['value'] == 'Negotiable') {
                    $salary['min'] = '*';
                    $salary['max'] = '*';
                    $salary['value'] = null;
                } else {
                    $salary['min'] = preg_replace('/(?<!^)\S/', '*', (string) $salary['min']);
                    $salary['max'] = preg_replace('/(?<!^)\S/', '*', (string) $salary['max']);
                    $value = strtolower(str_replace(' ', '', (string) $salary['value']));
                    if (str_contains($value, 'from')) {
                        $salary['value'] = 'From ' . preg_replace('/(?<!^)\S/', '*', filter_var($salary['value'], FILTER_SANITIZE_NUMBER_INT)) . ' ' . $salary['currency'];
                    } elseif (str_contains($value, 'upto')) {
                        $salary['value'] = 'Up to ' . preg_replace('/(?<!^)\S/', '*', filter_var($salary['value'], FILTER_SANITIZE_NUMBER_INT)) . ' ' . $salary['currency'];
                    } else {
                        $salary['value'] = $salary['min'] . ' - ' . $salary['max'] . ' ' . $salary['currency'];
                    }

                }
            }
        }

        if ($locale == 'vi_VN') {
            if ($salary['value'] == 'Negotiable') {
                $salary['value'] = 'Thương lượng';
            } else {
                $salary['value'] = str_replace(['Up to', 'From'], ['Lên tới', 'Từ'], (string) $salary['value']);
            }
        }

        return $salary;
    }

    private function skipInvisibleSalary()
    {
        return !empty(request()->header('X-Topdev-Source'))
            && in_array(request()->header('X-Topdev-Source'), $this->skipRequireLoginShowSalary ?? []);
    }
}
