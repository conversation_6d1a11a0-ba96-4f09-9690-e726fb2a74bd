<?php

namespace Modules\Job\Transformers;

use App\Helpers\GetDataUser;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Modules\Company\Transformers\CompanyResource;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class PopularHomePageCollection extends ResourceCollection
{
    use ElasticCollectionTrait;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $company = null;
        if (app()->environment('production')) {
            $company = (app(\Modules\Company\Repositories\Contracts\CompanyRepositoryInterface::class))->searchOnElasticsearch(
                null,
                [
                    'ids' => 83771,
                    'page_size' => 1,
                ]
            );
        }

        $listDataUser = app(GetDataUser::class)->getDataUser(auth('api')->user(), 'applied_jobs,followed_jobs,blacklist_companies,followed_companies');
        $jobs = (new JobCollection($this->collection->take(23)))->listDataUser($listDataUser);

        return [
            'jobs' => $jobs,
            'company' => $company ? CompanyResource::fromElasticsearch($company) : [],
        ];
    }

    public function withResponse($request, $response)
    {
        $res = json_decode($response->getContent(), true);
        unset($res['links']);
        unset($res['meta']);
        $response->setContent(json_encode($res));
    }
}
