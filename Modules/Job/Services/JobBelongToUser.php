<?php

namespace Modules\Job\Services;

use Modules\Job\Repositories\Contracts\JobRepositoryInterface;
use Modules\User\Entities\User;
use Modules\User\Repositories\Contracts\UserRepositoryInterface;

class JobBelongToUser
{
    public static function getMyJobsOfUser(User $user, $params)
    {
        $user = app(UserRepositoryInterface::class)->searchOnElasticsearch(
            $user->id,
            [
                'page_size' => 1,
            ]
        );

        $jobs = app(JobRepositoryInterface::class)->withoutCache(fn() => app(JobRepositoryInterface::class)->searchOnElasticsearch(
            $params['keyword'] ?? null,
            array_merge(
                $params,
                [
                    'company' =>  $user['hits']['hits'][0]['_source']['company_id'] ?? '-999999',
                    //-999999 đề phòng user hack xóa dc company_id của profile thì ở đây trả ra all job
                ]
            )
        ));

        return $jobs;
    }
}
