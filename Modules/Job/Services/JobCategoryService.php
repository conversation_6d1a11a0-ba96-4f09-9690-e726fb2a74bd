<?php
namespace Modules\Job\Services;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Modules\Job\Entities\JobCategory;

class JobCategoryService
{
    /**
     * @return Builder[]|Collection|JobCategory[]
     */
    public function getAllJobCategory(): Collection|array
    {
        return JobCategory::query()
            ->isCategory()
            ->with('roles')
            ->get();
    }

    /**
     * @param  int  $id
     *
     * @return mixed|null
     */
    public function getRolesByJobCategoryId(int $id)
    {
        $jobCategory = JobCategory::query()
            ->where('id', $id)
            ->with('roles')
            ->first();

        if ($jobCategory instanceof JobCategory) {
            return $jobCategory->roles;
        }

        return null;
    }

    public function getAllWithCategoryType(): Collection|array
    {
        return JobCategory::query()
            ->isCategory()
            ->get();
    }
}
