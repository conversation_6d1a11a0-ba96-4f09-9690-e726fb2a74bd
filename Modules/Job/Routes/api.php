<?php

use Mo<PERSON><PERSON>\Job\Http\Controllers\JobSearchController;
use Illuminate\Support\Facades\Route;
use Modules\Job\Http\Controllers\JobCategoryController;
use Modules\Job\Http\Controllers\JobController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('/jobs')->group(function () {
    Route::get('/decrypt-recommend-jobs', 'JobController@decryptRecommendJobs');
    //    Route::middleware('auth:api')->group(function () {
    //        Route::post('/', 'JobController@store')->middleware('can:create,Modules\Job\Entities\Job');
    //        Route::put('/{job}', 'JobController@update')->middleware('can:update,job');
    //        Route::patch('/{job}', 'JobController@update')->middleware('can:update,job');
    //    });
    Route::get('/', 'JobController@all')->middleware('ensure_frontend_requests_are_stateful');
    Route::get('{id}', 'JobController@job')->where('id', '[0-9]+');
    Route::get('{id}/recommend', 'JobController@recommendV2')->where('id', '[0-9]+');
    Route::get('/recommend', 'JobController@recommend');
    Route::get('/popular-for-companies', 'JobController@popularForCompanies');
    Route::get('{id}/announcements', 'JobController@announcements')->where('id', '[0-9]+');
    Route::get('{id}/recommend-jobs', 'JobController@recommendJobs')->where('id', '[0-9]+');
    Route::get('/fits-for-you', [JobController::class, 'fitJobs'])->where('id', '[0-9]+');

    Route::post('/feedback', 'FeedbackController@submit')->middleware('auth:api');
    Route::post('/publish', 'JobController@publish')->middleware('auth:api');
    Route::get('/search/v2', [JobSearchController::class, 'index'])->middleware([
        'ensure_frontend_requests_are_stateful',
        'setlocaleapi'
    ])->name('job_search_v2');
    Route::get('/available-locations', [JobController::class, 'availableLocations']);
});

Route::prefix('job-categories')->group(function () {
    Route::get('job-category-with-all-type', [JobCategoryController::class, 'getAllJobCategory']);
    Route::get('all-by-category-type', [JobCategoryController::class, 'getByAllCategoryType']);
    Route::get('all-roles-by-job-category/{jobCategoryId}', [JobCategoryController::class, 'getRolesByJobCategoryId']);
});

Route::prefix('candidates')->group(function () {
    Route::middleware('auth:api')->group(function () {
        Route::get('/reports', 'CandidateController@reports')->middleware('permission:administrator');
    });
    Route::post('/', 'CandidateController@apply');
});

Route::prefix('apply-jobs')->group(function () {
    Route::middleware('formdata')->group(function () {
        Route::post('/', 'CandidateController@applyJobs');
    });
});

Route::get('/recommend', 'JobController@allJobV2');
Route::get('/viec-lam-it/all-jobs-fb-2024.csv', 'JobController@getJobsForFB');
Route::get('/viec-lam-it/all-jobs-gg-2024.csv', 'JobController@getJobsForGG');
