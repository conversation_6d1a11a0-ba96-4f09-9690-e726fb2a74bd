<?php

namespace Modules\Job\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Job\Entities\Job;
use Modules\User\Entities\User;

class JobPolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if replies can be created by the user.
     */
    public function create(User $user): bool
    {
        return true;
    }

    /**
     * Determine if the given reply can be updated by the user.
     */
    public function update(User $user, Job $reply): bool
    {
        return true;
    }

    /**
     * Determine if the given reply can be deleted by the user.
     */
    public function delete(User $user, Job $reply): bool
    {
        return true;
    }
}
