<?php

namespace Modules\Job\Providers;

use Illuminate\Support\ServiceProvider;
use Modules\Job\Entities\Candidate;
use Modules\Job\Entities\Job;
use Modules\Job\Repositories\Caches\JobCacheDecorator;
use Modules\Job\Repositories\Contracts\CandidateRepositoryInterface;
use Modules\Job\Repositories\Contracts\JobRepositoryInterface;
use Modules\Job\Repositories\Eloquents\CandidateEloquentRepository;
use Modules\Job\Repositories\Eloquents\JobEloquentRepository;

class JobServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerConfig();
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);

        $this->app->singleton(JobRepositoryInterface::class, fn() => new JobCacheDecorator(
            new JobEloquentRepository(
                new Job
            )
        ));

        $this->app->singleton(CandidateRepositoryInterface::class, fn() => new CandidateEloquentRepository(
            new Candidate
        ));
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            __DIR__ . '/../Config/config.php' => config_path('job.php'),
        ], 'config');
        $this->mergeConfigFrom(
            __DIR__ . '/../Config/config.php',
            'job'
        );
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }
}
