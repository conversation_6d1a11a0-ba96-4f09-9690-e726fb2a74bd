<?php

namespace Modules\Job\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Modules\Job\Entities\Job;
use Modules\Job\Entities\JobSimilarJob;
use Modules\Job\Entities\SimilarJob;
use Modules\Job\Enums\JobLevelEnum;

/**
 * @property Collection<Job> $similar_jobs
 */
trait HasSimilarJob
{
    /**
     * Similar jobs relations. Get similar jobs from database :).
     *
     * @return BelongsToMany
     */
    public function similar_jobs(): BelongsToMany
    {
        return $this->belongsToMany(SimilarJob::class, JobSimilarJob::class, 'job_id', 'similar_job_id')
            ->where('level', 'paid')
            ->orderByDesc('job_similar_job.confidence')
            ->withPivot('confidence')
            ->withTimestamps();
    }

    /**
     * @return bool
     * @throws \Exception
     */
    public function updateSimilarJobs(array $similarJobs = [])
    {
        // Remove old job
        $importedSimilarJobIds = collect($similarJobs)->pluck('similar_job_id');
        $diff = $this->similar_jobs->whereNotIn('id', $importedSimilarJobIds);

        // logger(__METHOD__. ' origin:', $this->similar_jobs->modelKeys());
        // logger(__METHOD__. ' imported:', $importedSimilarJobIds->toArray());

        if ($diff->isNotEmpty()) {
            logger(__METHOD__.' will be detached: '.$diff->count(), $diff->modelKeys());

            // $this->similar_jobs()->detach($diff);
            $this->similar_jobs()->newPivotStatementForId($diff->modelKeys())->delete();
            $this->refresh();
        }

        // Add or update new similar jobs
        /** @var array $item {similar_job_id,confidence} */
        foreach ($similarJobs as $item) {
            $similar_job_id = $item['similar_job_id'];
            $attributes = ['confidence' => $item['confidence']];
            /** @var SimilarJob $job */
            if ($job = $this->similar_jobs->find($similar_job_id)) {
                if ($job->pivot->confidence != $item['confidence']) {
                    // logger('Update only when changed', [$item['confidence'], $job->pivot->confidence]);
                    $job->pivot->update($attributes);
                }
            } else {
                if (Job::withTrashed()->find($similar_job_id)) {
                    $this->similar_jobs()->attach($similar_job_id, $attributes);
                } else {
                    logs()->warning('similar_job not found', ['job_id' => $this->getKey(), 'related' => $item]);
                }
            }
        }

        return true;
    }

    /**
     * @param  Builder  $query
     *
     * @return Builder
     */
    public function scopeSimilarJobIn($query, $jobIds)
    {
        return $query->whereHas('similar_jobs', function (Builder $builder) use ($jobIds) {
            // $builder->scopes();
            $jobIds = is_array($jobIds) ?: preg_split('/[\s,]+/', (string) $jobIds);

            // logger('scopeSimilarJob', $jobIds);
            return $builder->whereIn('job_similar_job.similar_job_id', $jobIds);
        });
    }

    /**
     * @param  Builder  $query
     *
     * @return Builder
     */
    public function scopeHasSimilarJobs(Builder $query)
    {
        return $query->has('similar_jobs');
    }

    /**
     * @param  Builder  $query
     *
     * @return Builder
     */
    public function scopeDoesntHaveSimilarJobs(Builder $query): Builder
    {
        return $query->doesntHave('similar_jobs');
    }

    /**
     * Get Recommend Job with more information
     *
     * @return BelongsToMany
     */
    public function recommendSimilarJobs(): BelongsToMany
    {
        return $this->belongsToMany(Job::class, JobSimilarJob::class, 'job_id', 'similar_job_id')
            ->where('status', Job::STATUS_OPEN)
            ->orderByRaw('CASE
                WHEN level = "'.JobLevelEnum::PAID->value.'" THEN 1
                WHEN level = "'.JobLevelEnum::FREE->value.'" THEN 2
                ELSE 3
            END')
            ->orderBy('job_similar_job.confidence', 'desc');
    }
}
