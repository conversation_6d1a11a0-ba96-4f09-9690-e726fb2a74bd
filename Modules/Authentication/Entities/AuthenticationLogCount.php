<?php

namespace Modules\Authentication\Entities;

use Illuminate\Database\Eloquent\Model;

class AuthenticationLogCount extends Model
{
    /**
     * @inheritdoc
     */
    protected $connection = 'mysql_accounts';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'authentication_log_count';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'login_count',
    ];
}
