<?php

namespace Modules\Authentication\Entities;

use Illuminate\Database\Eloquent\Model;
use Modules\User\Entities\User;

class Social extends Model
{
    /**
     * @inheritdoc
     */
    protected $connection = 'mysql_accounts';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'socials';

    protected $fillable = [
        'user_id', 'provider_user_id', 'provider', 'email',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
