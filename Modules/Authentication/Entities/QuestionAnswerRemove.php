<?php

namespace Modules\User\Entities;

use Illuminate\Database\Eloquent\Model;

class QuestionAnswerRemove extends Model
{
    /**
     * @inheritdoc
     */
    protected $connection = 'mysql_accounts';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'question_answer_remove';

    protected $fillable = [
        'user_id',
        'question_id',
        'reason',
    ];
}
