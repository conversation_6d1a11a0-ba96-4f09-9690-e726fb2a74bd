<?php

namespace Modules\Authentication\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface;
use Modules\Core\Http\Responses\ApiResponse;

class AuthenticationLogController extends Controller
{
    /**
     * The authentication log repository.
     *
     * @var \Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface
     */
    protected $authLogRepository;

    /**
     * Create a new controller instance.
     *
     * @param  \Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface  $authLogRepository
     * @return void
     */
    public function __construct(AuthenticationLogRepositoryInterface $authLogRepository)
    {
        $this->authLogRepository = $authLogRepository;
        
        $this->middleware('auth:api')->except([]);
    }

    /**
     * Get the authenticated user's login history.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $limit = $request->input('limit', 10);
        
        $logs = $this->authLogRepository->getLoginLogs($user->id, $limit);
        
        return ApiResponse::success([
            'logs' => $logs
        ]);
    }

    /**
     * Get the specified login log.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $user = request()->user();
        $log = $this->authLogRepository->findWhere([
            'id' => $id,
            'authenticatable_type' => get_class($user),
            'authenticatable_id' => $user->id,
        ])->first();

        if (!$log) {
            return ApiResponse::error('Log not found', 404);
        }

        return ApiResponse::success([
            'log' => $log
        ]);
    }
}
