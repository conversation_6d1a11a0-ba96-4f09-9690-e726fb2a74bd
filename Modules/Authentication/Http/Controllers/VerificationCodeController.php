<?php

namespace Modules\Authentication\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Modules\Authentication\Events\VerifySuccessfully;
use Modules\Authentication\Jobs\SendVerificationCodeJob;
use Modules\Authentication\Repositories\Contracts\VerificationCodeRepositoryInterface;
use Modules\Core\Http\Responses\ApiResponse;
use Modules\User\Entities\User;

class VerificationCodeController extends Controller
{
    /**
     * The verification code repository.
     *
     * @var \Modules\Authentication\Repositories\Contracts\VerificationCodeRepositoryInterface
     */
    protected $verificationCodeRepository;

    /**
     * Create a new controller instance.
     *
     * @param  \Modules\Authentication\Repositories\Contracts\VerificationCodeRepositoryInterface  $verificationCodeRepository
     * @return void
     */
    public function __construct(VerificationCodeRepositoryInterface $verificationCodeRepository)
    {
        $this->verificationCodeRepository = $verificationCodeRepository;
    }

    /**
     * Send verification code.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function send(Request $request)
    {
        $request->validate([
            'verifiable' => 'required|string',
            'type' => 'required|in:email,phone',
        ]);

        $verifiable = $request->input('verifiable');
        $type = $request->input('type');

        // Generate and save verification code
        $verificationCode = $this->verificationCodeRepository->createFor($verifiable);
        
        // Dispatch job to send verification code
        SendVerificationCodeJob::dispatch(
            $verifiable,
            $verificationCode->code,
            $type
        )->onQueue('notifications');

        return ApiResponse::success([
            'message' => 'Verification code sent successfully',
            'verifiable' => $verifiable,
            'type' => $type,
            'expires_in' => config('authentication.verification_code.expires'),
            'expires_in_minutes' => ceil(config('authentication.verification_code.expires') / 60),
        ]);
    }

    /**
     * Verify the verification code.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verify(Request $request)
    {
        $request->validate([
            'verifiable' => 'required|string',
            'code' => 'required|string',
            'type' => 'required|in:email,phone',
        ]);

        $verifiable = $request->input('verifiable');
        $code = $request->input('code');
        $type = $request->input('type');

        // Find and validate the verification code
        $verificationCode = $this->verificationCodeRepository->findValidCode($verifiable, $code);

        if (!$verificationCode) {
            return ApiResponse::error('Invalid or expired verification code', 422);
        }

        // Mark the code as used
        $this->verificationCodeRepository->markAsUsed($verifiable, $code);

        // Fire verification successful event
        event(new VerifySuccessfully($verifiable, $type));

        return ApiResponse::success([
            'message' => 'Verification successful',
            'verifiable' => $verifiable,
            'type' => $type,
        ]);
    }
}
