<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Auth\Events\Logout;
use Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface;

class LogSuccessfulLogout
{
    /**
     * The authentication log repository.
     *
     * @var \Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface
     */
    protected $authLogRepository;

    /**
     * Create the event listener.
     *
     * @param  \Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface  $authLogRepository
     * @return void
     */
    public function __construct(AuthenticationLogRepositoryInterface $authLogRepository)
    {
        $this->authLogRepository = $authLogRepository;
    }

    /**
     * Handle the event.
     *
     * @param  \Illuminate\Auth\Events\Logout  $event
     * @return void
     */
    public function handle(Logout $event)
    {
        if ($event->user) {
            $this->authLogRepository->logLogout($event->user);
        }
    }
}
