<?php

namespace Modules\Authentication\Listeners;

use <PERSON><PERSON>\Passport\Events\RefreshTokenCreated;
use Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface;

class LogSuccessfullRefreshToken
{
    /**
     * The authentication log repository.
     *
     * @var \Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface
     */
    protected $authLogRepository;

    /**
     * Create the event listener.
     *
     * @param  \Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface  $authLogRepository
     * @return void
     */
    public function __construct(AuthenticationLogRepositoryInterface $authLogRepository)
    {
        $this->authLogRepository = $authLogRepository;
    }

    /**
     * Handle the event.
     *
     * @param  \Laravel\Passport\Events\RefreshTokenCreated  $event
     * @return void
     */
    public function handle(RefreshTokenCreated $event)
    {
        // Get the user model
        $userModel = config('auth.providers.users.model');
        $user = (new $userModel)->find($event->userId);
        
        if ($user) {
            // Log the token refresh (treated as a login)
            $log = $this->authLogRepository->logLogin(
                $user, 
                request()->ip(), 
                request()->userAgent(), 
                'api_refresh_token'
            );
            
            // Update the user's last login info
            if ($log && method_exists($user, 'updateLastLogin')) {
                $user->updateLastLogin($log->id);
            }
        }
    }
}
