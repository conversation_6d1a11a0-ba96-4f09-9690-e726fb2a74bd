<?php

namespace Modules\Authentication\Listeners;

use Modules\Authentication\Events\UserFirstLogin;
use Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface;

class UpdateUserLoginNth
{
    /**
     * The authentication log repository.
     *
     * @var \Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface
     */
    protected $authLogRepository;

    /**
     * Create the event listener.
     *
     * @param  \Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface  $authLogRepository
     * @return void
     */
    public function __construct(AuthenticationLogRepositoryInterface $authLogRepository)
    {
        $this->authLogRepository = $authLogRepository;
    }

    /**
     * Handle the event.
     *
     * @param  \Modules\Authentication\Events\UserFirstLogin  $event
     * @return void
     */
    public function handle(UserFirstLogin $event)
    {
        // Update the login count for the user
        $this->authLogRepository->updateLoginNth($event->user);
    }
}
