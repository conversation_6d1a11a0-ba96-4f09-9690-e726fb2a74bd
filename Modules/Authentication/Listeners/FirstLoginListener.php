<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Auth\Events\Login;
use Illuminate\Support\Facades\DB;
use Modules\Authentication\Events\UserFirstLogin;
use Modules\User\Entities\User;

class FirstLoginListener
{
    /**
     * Handle the event.
     *
     * @param  \Illuminate\Auth\Events\Login  $event
     * @return void
     */
    public function handle(Login $event)
    {
        $user = $event->user;
        
        // Check if this is the first login
        $loginCount = DB::connection('mysql_accounts')
            ->table('authentication_log')
            ->where('authenticatable_type', get_class($user))
            ->where('authenticatable_id', $user->id)
            ->where('id', '!=', $user->last_login_id)
            ->count();
            
        if ($loginCount === 0) {
            // This is the first login
            event(new UserFirstLogin($user));
        }
    }
}
