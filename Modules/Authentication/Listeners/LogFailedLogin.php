<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Auth\Events\Failed;
use Illuminate\Support\Facades\Request;
use Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface;

class LogFailedLogin
{
    /**
     * The authentication log repository.
     *
     * @var \Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface
     */
    protected $authLogRepository;

    /**
     * Create the event listener.
     *
     * @param  \Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface  $authLogRepository
     * @return void
     */
    public function __construct(AuthenticationLogRepositoryInterface $authLogRepository)
    {
        $this->authLogRepository = $authLogRepository;
    }

    /**
     * Handle the event.
     *
     * @param  \Illuminate\Auth\Events\Failed  $event
     * @return void
     */
    public function handle(Failed $event)
    {
        if ($event->user) {
            $this->authLogRepository->logFailedLogin(
                $event->user,
                Request::ip(),
                Request::userAgent(),
                $event->credentials
            );
        }
    }
}
