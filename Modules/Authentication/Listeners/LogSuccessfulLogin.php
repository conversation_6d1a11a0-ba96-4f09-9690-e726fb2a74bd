<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Auth\Events\Login;
use Illuminate\Http\Request;
use Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface;

class LogSuccessfulLogin
{
    /**
     * The authentication log repository.
     *
     * @var \Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface
     */
    protected $authLogRepository;

    /**
     * The request.
     *
     * @var \Illuminate\Http\Request
     */
    protected $request;

    /**
     * Create the event listener.
     *
     * @param  \Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface  $authLogRepository
     * @param  \Illuminate\Http\Request  $request
     * @return void
     */
    public function __construct(AuthenticationLogRepositoryInterface $authLogRepository, Request $request)
    {
        $this->authLogRepository = $authLogRepository;
        $this->request = $request;
    }

    /**
     * Handle the event.
     *
     * @param  \Illuminate\Auth\Events\Login  $event
     * @return void
     */
    public function handle(Login $event)
    {
        $user = $event->user;
        $ip = $this->request->ip();
        $userAgent = $this->request->userAgent();
        
        // Get the login type (web, api, etc.)
        $type = $this->request->is('api/*') ? 'api' : 'web';
        
        // Log the login
        $log = $this->authLogRepository->logLogin($user, $ip, $userAgent, $type);
        
        // Update the user's last login info
        if ($log && method_exists($user, 'updateLastLogin')) {
            $user->updateLastLogin($log->id);
        }
    }
}
