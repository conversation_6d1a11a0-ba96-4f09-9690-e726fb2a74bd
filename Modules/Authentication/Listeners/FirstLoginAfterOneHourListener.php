<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Support\Facades\Mail;
use Modules\Authentication\Events\UserFirstLoginFromWeb;
use Modules\Notification\Notifications\UserFirstLoginNotification;

class FirstLoginAfterOneHourListener
{
    /**
     * Handle the event.
     *
     * @param  \Modules\Authentication\Events\UserFirstLoginFromWeb  $event
     * @return void
     */
    public function handle(UserFirstLoginFromWeb $event)
    {
        try {
            // Send notification after 1 hour
            $event->user->notify(
                (new UserFirstLoginNotification($event->user, $event->ipAddress, $event->userAgent))
                    ->delay(now()->addHour())
            );
        } catch (\Exception $e) {
            \Log::error('Failed to schedule first login notification: ' . $e->getMessage());
        }
    }
}
