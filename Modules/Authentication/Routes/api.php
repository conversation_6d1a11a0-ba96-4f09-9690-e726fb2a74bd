<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the authentication module.
| These routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group.
|
*/

// Public routes
Route::prefix('auth')->group(function () {
    // Send verification code
    Route::post('/verification/send', 'VerificationCodeController@send')
        ->name('api.auth.verification.send');
        
    // Verify code
    Route::post('/verification/verify', 'VerificationCodeController@verify')
        ->name('api.auth.verification.verify');
});

// Protected routes (require authentication)
Route::middleware('auth:api')->group(function () {
    // Authentication logs
    Route::get('/auth/logs', 'AuthenticationLogController@index')
        ->name('api.auth.logs.index');
        
    Route::get('/auth/logs/{id}', 'AuthenticationLogController@show')
        ->name('api.auth.logs.show');
});