<?php

namespace Modules\Authentication\Repositories\Eloquents;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Modules\Authentication\Entities\AuthenticationLog;
use Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface;
use Modules\Core\Repositories\Eloquents\BaseEloquentRepository;

class AuthenticationLogEloquentRepository extends BaseEloquentRepository implements AuthenticationLogRepositoryInterface
{
    /**
     * @var AuthenticationLog
     */
    protected $model;

    /**
     * AuthenticationLogEloquentRepository constructor.
     *
     * @param AuthenticationLog $model
     */
    public function __construct(AuthenticationLog $model)
    {
        parent::__construct($model);
        $this->model = $model;
    }

    /**
     * @inheritDoc
     */
    public function getLatestLoginLog($userId)
    {
        return $this->model
            ->where('authenticatable_id', $userId)
            ->whereNotNull('login_at')
            ->orderBy('login_at', 'desc')
            ->first();
    }

    /**
     * @inheritDoc
     */
    public function getLoginLogs($userId, $limit = 10)
    {
        return $this->model
            ->where('authenticatable_id', $userId)
            ->whereNotNull('login_at')
            ->orderBy('login_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function logLogin($user, $ipAddress, $userAgent, $typeLogin = null)
    {
        $log = $this->model->create([
            'authenticatable_type' => get_class($user),
            'authenticatable_id' => $user->id,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'login_at' => now(),
            'type_login' => $typeLogin,
        ]);

        return $log;
    }

    /**
     * @inheritDoc
     */
    public function logLogout($user)
    {
        $log = $this->getLatestLoginLog($user->id);
        
        if ($log) {
            $log->update([
                'logout_at' => now()
            ]);
            
            return $log;
        }
        
        return null;
    }

    /**
     * @inheritDoc
     */
    public function updateLoginNth($user)
    {
        $log = $this->getLatestLoginLog($user->id);
        
        if ($log) {
            $loginNth = $this->model
                ->where('authenticatable_id', $user->id)
                ->where('id', '!=', $log->id)
                ->whereNotNull('login_at')
                ->count() + 1;
            
            $log->update([
                'login_nth' => $loginNth
            ]);
            
            return $log;
        }
        
        return null;
    }
    
    /**
     * Log a failed login attempt.
     *
     * @param  mixed  $user
     * @param  string  $ipAddress
     * @param  string  $userAgent
     * @param  array  $credentials
     * @return \Modules\Authentication\Entities\AuthenticationLog
     */
    public function logFailedLogin($user, $ipAddress, $userAgent, $credentials = [])
    {
        return $this->model->create([
            'authenticatable_type' => get_class($user),
            'authenticatable_id' => $user->id,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'login_at' => null,
            'logout_at' => null,
            'login_nth' => 0,
            'client_id' => $credentials['client_id'] ?? null,
            'type_login' => $credentials['type_login'] ?? 'failed',
            'failed_attempt' => true,
            'failed_credentials' => $this->sanitizeCredentials($credentials),
        ]);
    }
    
    /**
     * Sanitize credentials before logging.
     *
     * @param  array  $credentials
     * @return array
     */
    protected function sanitizeCredentials($credentials)
    {
        // Remove sensitive data from credentials
        $sensitiveFields = ['password', 'password_confirmation', 'token', 'api_token'];
        
        foreach ($sensitiveFields as $field) {
            if (isset($credentials[$field])) {
                $credentials[$field] = '********';
            }
        }
        
        return $credentials;
    }
}
