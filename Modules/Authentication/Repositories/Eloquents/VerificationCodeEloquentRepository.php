<?php

namespace Modules\Authentication\Repositories\Eloquents;

use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Modules\Authentication\Entities\VerificationCode;
use Modules\Authentication\Repositories\Contracts\VerificationCodeRepositoryInterface;
use Modules\Authentication\Support\CodeGenerator;
use Modules\Core\Repositories\Eloquents\BaseEloquentRepository;

class VerificationCodeEloquentRepository extends BaseEloquentRepository implements VerificationCodeRepositoryInterface
{
    /**
     * @var VerificationCode
     */
    protected $model;
    
    /**
     * @var CodeGenerator
     */
    protected $codeGenerator;

    /**
     * VerificationCodeEloquentRepository constructor.
     *
     * @param VerificationCode $model
     * @param CodeGenerator $codeGenerator
     */
    public function __construct(VerificationCode $model, CodeGenerator $codeGenerator)
    {
        parent::__construct($model);
        $this->model = $model;
        $this->codeGenerator = $codeGenerator;
    }

    /**
     * @inheritDoc
     */
    public function createFor($verifiable, $code = null)
    {
        if ($code === null) {
            $code = $this->codeGenerator->generate();
        }
        
        $verificationCode = $this->model->create([
            'code' => $code,
            'verifiable' => $verifiable,
            'expires_at' => now()->addMinutes(config('verificationcode.expire_minutes', 10)),
        ]);
        
        return $verificationCode;
    }

    /**
     * @inheritDoc
     */
    public function findValidCode($verifiable, $code)
    {
        $verificationCode = $this->model
            ->where('verifiable', $verifiable)
            ->where('expires_at', '>=', now())
            ->latest()
            ->first();
            
        if (!$verificationCode) {
            return null;
        }
        
        if (Hash::check($code, $verificationCode->code)) {
            return $verificationCode;
        }
        
        return null;
    }

    /**
     * @inheritDoc
     */
    public function markAsUsed($verifiable, $code)
    {
        $verificationCode = $this->findValidCode($verifiable, $code);
        
        if ($verificationCode) {
            $verificationCode->delete();
            return true;
        }
        
        return false;
    }

    /**
     * @inheritDoc
     */
    public function deleteExpired()
    {
        return $this->model
            ->where('expires_at', '<', now())
            ->delete();
    }
}
