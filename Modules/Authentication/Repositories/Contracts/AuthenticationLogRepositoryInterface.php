<?php

namespace Modules\Authentication\Repositories\Contracts;

use Modules\Core\Repositories\Contracts\BaseRepositoryInterface;

interface AuthenticationLogRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Get the latest login log for a user
     *
     * @param int $userId
     * @return mixed
     */
    public function getLatestLoginLog($userId);
    
    /**
     * Get the login logs for a user
     *
     * @param int $userId
     * @param int $limit
     * @return mixed
     */
    public function getLoginLogs($userId, $limit = 10);
    
    /**
     * Log a successful login
     *
     * @param mixed $user
     * @param string $ipAddress
     * @param string $userAgent
     * @param string|null $typeLogin
     * @return mixed
     */
    public function logLogin($user, $ipAddress, $userAgent, $typeLogin = null);
    
    /**
     * Log a successful logout
     *
     * @param mixed $user
     * @return mixed
     */
    public function logLogout($user);
    
    /**
     * Update login nth for user.
     *
     * @param  mixed  $user
     * @return mixed
     */
    public function updateLoginNth($user);
    
    /**
     * Log a failed login attempt.
     *
     * @param  mixed  $user
     * @param  string  $ipAddress
     * @param  string  $userAgent
     * @param  array  $credentials
     * @return \Modules\Authentication\Entities\AuthenticationLog
     */
    public function logFailedLogin($user, $ipAddress, $userAgent, $credentials = []);
}
