<?php

namespace Modules\Authentication\Repositories\Contracts;

use Modules\Core\Repositories\Contracts\BaseRepositoryInterface;

interface VerificationCodeRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * Create a new verification code for the given verifiable
     *
     * @param string $verifiable
     * @param string|null $code
     * @return mixed
     */
    public function createFor($verifiable, $code = null);
    
    /**
     * Find a valid verification code
     *
     * @param string $verifiable
     * @param string $code
     * @return mixed
     */
    public function findValidCode($verifiable, $code);
    
    /**
     * Mark a verification code as used
     *
     * @param string $verifiable
     * @param string $code
     * @return bool
     */
    public function markAsUsed($verifiable, $code);
    
    /**
     * Delete expired verification codes
     *
     * @return int Number of deleted codes
     */
    public function deleteExpired();
}
