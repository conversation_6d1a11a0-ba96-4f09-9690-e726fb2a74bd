<?php

namespace Modules\Authentication\Commands;

use GuzzleHttp\Client;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class PullTaxonomyFromOriginCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pull:taxonomies';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update taxonomy if has new version';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @return void
     */
    public function handle(): void
    {
        $client = new Client();

        $response = Http::get(api_url('td/v2/areas/province'));

        Storage::put('public/provinces.json', json_encode($response->json('data')));

        $response = Http::get(api_url('td/v2/taxonomies'), [
            'fields' => 'skills,industries'
        ]);

        Storage::put('public/taxonomies.json', json_encode($response->json()));
    }
}
