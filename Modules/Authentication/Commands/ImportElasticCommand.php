<?php

namespace Modules\Authentication\Commands;

use Illuminate\Console\Command;
use Modules\Authentication\Entities\AuthenticationLog;

class ImportElasticCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'authentication-log:import-elastic';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import es.';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        AuthenticationLog::chunkById(300, function ($authenlogs) {
            foreach ($authenlogs as $authenlog) {
                \Log::info('-----authenlog: ' . $authenlog->id);
                $authenlog->searchable();
            }
        });
    }
}
