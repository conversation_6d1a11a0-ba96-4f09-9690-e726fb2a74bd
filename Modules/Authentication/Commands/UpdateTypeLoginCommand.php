<?php

namespace Modules\Authentication\Commands;

use Illum<PERSON>\Console\Command;
use <PERSON><PERSON><PERSON>\Agent\Agent;
use Modules\Authentication\Entities\AuthenticationLog;

class UpdateTypeLoginCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'authentication-log:update-type-login';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command update type login.';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $agent = new Agent();
        AuthenticationLog::whereNotNull('login_at')->chunk(1000, function ($logs) use ($agent) {
            foreach ($logs as $log) {
                if ($log->type_login == 'web') {
                    $agent->setUserAgent($log->user_agent);
                    if ($agent->isDesktop() || $agent->isRobot()) {
                        $device = 'web';
                    } elseif ($agent->isPhone()) {
                        $device = 'mobile';
                    } else {
                        $device = 'tablet';
                    }
                    $log->type_login = $device;
                    $log->save();

                    $logsCount = AuthenticationLog::where([
                        'client_id' => $log->client_id,
                        'type_login' => $log->type_login,
                        'authenticatable_id' => $log->authenticatable_id,
                    ])->whereNotNull('login_at')
                        ->where('login_at', '<', $log->login_at)
                        ->count();
                    $count = !$logsCount ? 1 : $logsCount;
                    $log->update(['login_nth' => $count]);

                    $this->info('Update log of id:' . $log->id);
                }
            }
        });
    }
}
