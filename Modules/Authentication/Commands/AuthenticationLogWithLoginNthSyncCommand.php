<?php

namespace Modules\Authentication\Commands;

use Illuminate\Console\Command;
use Modules\Authentication\Entities\AuthenticationLog;

class AuthenticationLogWithLoginNthSyncCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $signature = 'authen-log:update-login-nth';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'No description.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $users = AuthenticationLog::pluck('authenticatable_id')->unique()->values();

        foreach ($users as $key => $user) {
            $logs = AuthenticationLog::where(['authenticatable_id' => $user])->get();
            $this->info('Update User: ' . $user);
            foreach ($logs as $key => $log) {
                $this->info($key);
                $log->forceFill(['login_nth' => $key + 1])->save();
            }
        }
    }
}
