<?php

namespace Modules\Authentication\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Notification;
use Modules\Authentication\Notifications\SendVerificationCode;

class SendVerificationCodeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The verifiable address (email/phone).
     *
     * @var string
     */
    protected $verifiable;

    /**
     * The verification type (email/phone).
     *
     * @var string
     */
    protected $type;

    /**
     * The verification code.
     *
     * @var string
     */
    protected $code;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int[]
     */
    public $backoff = [60, 300, 600];

    /**
     * Create a new job instance.
     *
     * @param  string  $verifiable
     * @param  string  $code
     * @param  string  $type
     * @return void
     */
    public function __construct($verifiable, $code, $type = 'email')
    {
        $this->verifiable = $verifiable;
        $this->code = $code;
        $this->type = $type;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Create a dummy notifiable instance
        $notifiable = new class {
            public $email;
            public $phone;
            
            public function routeNotificationForMail()
            {
                return $this->email;
            }
            
            public function routeNotificationForNexmo()
            {
                return $this->phone;
            }
        };
        
        // Set the appropriate property based on type
        if ($this->type === 'email') {
            $notifiable->email = $this->verifiable;
        } else {
            $notifiable->phone = $this->verifiable;
        }
        
        // Send the notification
        Notification::send(
            $notifiable,
            new SendVerificationCode($this->code, $this->type)
        );
    }
    
    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        // Log the failure
        \Log::error('Failed to send verification code: ' . $exception->getMessage(), [
            'verifiable' => $this->verifiable,
            'type' => $this->type,
            'exception' => $exception
        ]);
    }
}
