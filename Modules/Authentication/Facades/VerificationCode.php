<?php

namespace Modules\Authentication\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static \Modules\Authentication\Entities\VerificationCode createFor(string $verifiable, string $code = null)
 * @method static \Modules\Authentication\Entities\VerificationCode|null findValidCode(string $verifiable, string $code)
 * @method static bool markAsUsed(string $verifiable, string $code)
 * @method static int deleteExpired()
 * 
 * @see \Modules\Authentication\Repositories\Contracts\VerificationCodeRepositoryInterface
 */
class VerificationCode extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor(): string
    {
        return 'verification_code';
    }
}
