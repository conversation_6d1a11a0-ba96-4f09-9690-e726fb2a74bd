<?php

use Illuminate\Support\Facades\Auth;
use Modules\Authentication\Entities\AuthenticationLog;

if (!function_exists('generate_verification_code')) {
    /**
     * Generate a verification code.
     *
     * @param  int  $length
     * @param  string  $characters
     * @return string
     */
    function generate_verification_code($length = 6, $characters = '0123456789')
    {
        $code = '';
        $max = strlen($characters) - 1;
        
        for ($i = 0; $i < $length; $i++) {
            $code .= $characters[mt_rand(0, $max)];
        }
        
        return $code;
    }
}

if (!function_exists('get_client_ip')) {
    /**
     * Get the client IP address.
     *
     * @return string
     */
    function get_client_ip()
    {
        $ipAddress = '';
        
        if (isset($_SERVER['HTTP_CLIENT_IP'])) {
            $ipAddress = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED'])) {
            $ipAddress = $_SERVER['HTTP_X_FORWARDED'];
        } elseif (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
            $ipAddress = $_SERVER['HTTP_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_FORWARDED'])) {
            $ipAddress = $_SERVER['HTTP_FORWARDED'];
        } elseif (isset($_SERVER['REMOTE_ADDR'])) {
            $ipAddress = $_SERVER['REMOTE_ADDR'];
        }
        
        return $ipAddress;
    }
}
