<?php

namespace Modules\Authentication\Events;

use Illuminate\Queue\SerializesModels;
use Illuminate\Foundation\Events\Dispatchable;

class UserFirstLoginFromWeb
{
    use Dispatchable, SerializesModels;
    
    /**
     * The authenticated user.
     *
     * @var \Illuminate\Contracts\Auth\Authenticatable
     */
    public $user;

    /**
     * The request IP address.
     *
     * @var string
     */
    public $ipAddress;

    /**
     * The user agent.
     *
     * @var string
     */
    public $userAgent;

    /**
     * Create a new event instance.
     *
     * @param  \Illuminate\Contracts\Auth\Authenticatable  $user
     * @param  string  $ipAddress
     * @param  string  $userAgent
     * @return void
     */
    public function __construct($user, $ipAddress, $userAgent)
    {
        $this->user = $user;
        $this->ipAddress = $ipAddress;
        $this->userAgent = $userAgent;
    }
}
