<?php

namespace Modules\Authentication\Events;

use Illuminate\Queue\SerializesModels;
use Illuminate\Foundation\Events\Dispatchable;

class VerifySuccessfully
{
    use Dispatchable, SerializesModels;
    
    /**
     * The verifiable address (email/phone).
     *
     * @var string
     */
    public $verifiable;

    /**
     * The verification type (email/phone).
     *
     * @var string
     */
    public $type;

    /**
     * Create a new event instance.
     *
     * @param  string  $verifiable
     * @param  string  $type
     * @return void
     */
    public function __construct($verifiable, $type = 'email')
    {
        $this->verifiable = $verifiable;
        $this->type = $type;
    }
}
