<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTypeLoginClientIdToAuthenticationLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('authentication_log', function (Blueprint $table) {
            $table->integer('client_id')->nullable()->after('ip_address');
            $table->enum('type_login', ['web', 'api'])->default('web')->after('client_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('authentication_log', function (Blueprint $table) {
            $table->dropColumn('client_id');
            $table->dropColumn('type_login');
        });
    }
}
