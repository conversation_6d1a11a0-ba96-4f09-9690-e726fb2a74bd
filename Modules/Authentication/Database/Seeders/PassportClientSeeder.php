<?php

namespace Modules\Authentication\Database\Seeders;

use Illuminate\Database\Seeder;

class PassportClientSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        \Laravel\Passport\Client::create([
            'id' => 1,
            'name' => 'Frontend',
            'secret' => 'LVQdXj8kiEIHBJIaM6xP2c7DkPjURjvS0aMbpMjZ',
            'redirect' => 'http://topdev.com/oauth2/callback',
            'personal_access_client' => 0,
            'password_client' => 0,
            'revoked' => 0,
            'password_social_nullable' => 0,
        ]);

        \Laravel\Passport\Client::create([
            'id' => 2,
            'name' => 'Resume Dashboard',
            'secret' => 'ENvbPbfhRpEQsCv123j5y5Zd4L85cHWYMiQ5mzXF',
            'redirect' => 'http://topdev.com/users/oauth2/callback',
            'personal_access_client' => 0,
            'password_client' => 0,
            'revoked' => 0,
            'password_social_nullable' => 0,
        ]);

        \Laravel\Passport\Client::create([
            'id' => 3,
            'name' => 'Employer Dashboard',
            'secret' => 'LAXbSYzYp9R2msEqV0iFHmL5S8WuGjqv8bT2qXij',
            'redirect' => 'http://dash.topdev.com/auth/callback',
            'personal_access_client' => 0,
            'password_client' => 0,
            'revoked' => 0,
            'password_social_nullable' => 0,
        ]);
    }
}
