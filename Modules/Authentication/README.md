# Authentication Module

Module xử lý xác thực và xác minh người dùng trong hệ thống.

## <PERSON><PERSON><PERSON> tính năng chính

- Quản lý đăng nhập/đăng xuất
- <PERSON><PERSON> nhật ký đăng nhập
- X<PERSON>c thực 2 bước qua email/SMS
- Quản lý mã xác minh
- Thống kê đăng nhập

## Cài đặt

1. Thêm module vào `composer.json` nếu chưa có:

```bash
composer require modules/authentication
```

2. Chạy migrations:

```bash
php artisan module:migrate Authentication
```

3. Đăng ký service provider trong `config/app.php` (nếu cần):

```php
'providers' => [
    // ...
    Modules\Authentication\Providers\AuthenticationServiceProvider::class,
    Modules\Authentication\Providers\EventServiceProvider::class,
    Mo<PERSON>les\Authentication\Providers\ScheduleServiceProvider::class,
],
```

## <PERSON><PERSON><PERSON> hình

<PERSON> chép file cấu hình:

```bash
php artisan vendor:publish --tag=authentication-config
```

## Sử dụng

### Gửi mã xác minh

```php
use Modules\Authentication\Facades\VerificationCode;

// Gửi mã xác minh
$verificationCode = VerificationCode::createFor('<EMAIL>');

// Kiểm tra mã xác minh
if ($code = VerificationCode::findValidCode('<EMAIL>', $inputCode)) {
    // Xác minh thành công
    $code->markAsUsed();
}
```

### Sử dụng middleware xác thực

```php
Route::middleware(['auth:api', 'verified'])->group(function () {
    // Các route yêu cầu xác thực và xác minh email
});
```

## Lệnh Artisan

- `verification:cleanup`: Xóa các mã xác minh đã hết hạn

```bash
php artisan verification:cleanup
```

- `user:login-stats`: Xem thống kê đăng nhập người dùng

```bash
php artisan user:login-stats
php artisan user:login-stats --user-id=1
php artisan user:login-stats --days=60 --format=json
```

## Lịch trình tự động

- Xóa mã xác minh hết hạn: Hàng ngày lúc 00:00

## API Endpoints

### Xác thực

- `POST /api/auth/verification/send`: Gửi mã xác minh
- `POST /api/auth/verification/verify`: Xác minh mã
- `GET /api/auth/logs`: Lịch sử đăng nhập
- `GET /api/auth/logs/{id}`: Chi tiết lịch sử đăng nhập

## Events

- `UserFirstLogin`: Khi người dùng đăng nhập lần đầu
- `VerifySuccessfull`: Khi xác minh thành công

## Middleware

- `verified`: Kiểm tra người dùng đã xác minh email chưa

## License

MIT
