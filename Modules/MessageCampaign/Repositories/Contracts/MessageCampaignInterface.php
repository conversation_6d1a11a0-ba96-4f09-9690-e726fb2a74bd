<?php

namespace Modules\MessageCampaign\Repositories\Contracts;

use Redmix0901\Core\Repositories\Interfaces\BaseRepositoryInterface;

interface MessageCampaignInterface extends BaseRepositoryInterface
{
    /**
     * @inheritdoc
     */
    public function searchOnEloquent($keyword = null, $params = [], $fields = '*');

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = '*', $params = [], $fields = '*');
}
