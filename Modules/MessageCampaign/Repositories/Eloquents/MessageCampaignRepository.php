<?php

namespace Modules\MessageCampaign\Repositories\Eloquents;

use Mo<PERSON>les\MessageCampaign\Repositories\Contracts\MessageCampaignInterface;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\TermsAggregation;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MultiMatchQuery;
use ONGR\ElasticsearchDSL\Query\MatchAllQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\IdsQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;
use ONGR\ElasticsearchDSL\Sort\FieldSort;
use Redmix0901\Core\Repositories\Eloquent\BaseRepository;

class MessageCampaignRepository extends BaseRepository implements MessageCampaignInterface
{
    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = null, $params = [], $fields = '*')
    {
        return $this->querySearch($keyword, $params, $fields)->raw();
    }

    public function searchOnEloquent($keyword = null, $params = [], $fields = '*')
    {
        if (isset($params['page_size'])) {
            return $this->querySearch($keyword, $params, $fields)->paginate($params['page_size']);
        }

        return $this->querySearch($keyword, $params, $fields)->get();
    }

    /**
     * @inheritdoc
     */
    private function querySearch($keyword, $params, $fields)
    {
        return $this->model->search('*', function ($client, $body) use ($keyword, $params) {
            // Pagination
            $size = $params['page_size'] ?? 10;
            $from = isset($params['page']) ? $size * ($params['page'] - 1) : 0;

            $baseQuery = new BoolQuery();

            /*
             * Tìm kiếm keyword các trường post_content, post_title, post_name
             */
            if (!empty($keyword)) {
                $baseQuery->add(new MultiMatchQuery(['title.text.keyword', 'body.text.keyword'], $keyword, ['operator' => 'or']), BoolQuery::MUST);
            }

            /*
             * Lọc các post có id sau
             */
            if (array_key_exists('ids', $params)) {
                $baseQuery->add(new IdsQuery(empty($params['ids']) ? [] : explode(',', (string) $params['ids'])), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'action_button')) {
                $baseQuery->add(new TermQuery('action_button.text', $params['action_button']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'campaign_expect')) {
                $baseQuery->add(new TermQuery('campaign_expect.text', $params['campaign_expect']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'status')) {
                $baseQuery->add(new TermQuery('status', $params['status']), BoolQuery::MUST);
            }

            if (empty($keyword) && empty($params)) {
                $baseQuery = new MatchAllQuery();
            }

            $body->setSize($size);
            $body->setFrom($from);
            $body->addQuery($baseQuery);
            $body->addAggregation(new TermsAggregation('status', 'status'));

            $body->addSort(new FieldSort('created_at', null, ['order' => FieldSort::DESC]));

            return $client->search(['index' => $this->model->searchableAs(), 'body' => $body->toArray()])->asArray();
        })
        ->query(function ($builder) {
        });
    }

    /**
     * Check if has query.
     */
    private function hasQuery($query, $key)
    {
        return (bool) (isset($query[$key]) && !is_null($query[$key]));
    }
}
