<?php

namespace Modules\MessageCampaign\Entities;

use App\Traits\UseUuid;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use <PERSON><PERSON>\Scout\Searchable;
use Modules\Meta\Traits\Metable;
use Modules\Post\Entities\Post;

class MessageCampaign extends Post
{
    use Searchable;
    use UseUuid;
    use Metable;

    public const STATUS_ACTIVE = 1;
    public const STATUS_INACTIVE = 2;

    /**
     * @inheritdoc
     */
    protected $type = 'message_campaign';

    /**
     * @var array
     */
    protected $appends = [
        'title_detail', 'body_detail', 'action_button', 'campaign_expect',
    ];

    /**
     * @inheritdoc
     */
    protected $attributes = [
        'title' => null,
        'slug' => null,
        'status' => 1,
    ];

    /**
     * @inheritdoc
     */
    protected $fillable = [
        'title', 'content',
        'title_detail', 'body_detail', 'action_button', 'campaign_expect',
    ];

    /**
     * @inheritdoc
     */
    protected $casts = [
        'title' => 'string',
        'content' => 'string',
        'published_at' => 'datetime:Y-m-d H:i:s',
        'refreshed_at' => 'datetime:Y-m-d H:i:s',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * The attributes that should be append to native types.
     *
     * @var array
     */
    public $scopeMeta = [
        'title_detail' => null,
        'body_detail' => null,
        'action_button' => null,
        'campaign_expect' => null,
    ];

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'message_campaign_ams';
    }

    /**
     * @inheritdoc
     */
    public function ensureBeforeExpires()
    {
    }

    /**
     * @inheritdoc
     */
    public function ensureBeforeUnpublish()
    {
    }

    /**
     * @inheritdoc
     */
    public function sluggable(): string
    {
        return $this->title;
    }

    /**
     * Handle when someone viewed the blog.
     */
    public function recentlyViewedBy($visitor)
    {
    }

    /**
     * Get status for the model.
     *
     * @return array
     */
    public static function getStatusDescription()
    {
        return [
            static::STATUS_ACTIVE => 'Active',
            static::STATUS_INACTIVE => 'Inactive',
        ];
    }

    // /**
    //  * Relationship to the `Meta` model.
    //  *
    //  * @return MorphMany
    //  */
    public function meta() : MorphMany
    {
        return $this->morphMany($this->getMetaClassName(), 'metable');
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        $this->setRelation('meta', $this->meta()->get());
        ($this->getRelation('meta'));

        return [
            'id' => $this->id,
            'title' => !empty($this->title_detail) ? array_merge(['text'=>$this->title], (array) ($this->title_detail)) : ['text' => $this->title],
            'body' => !empty($this->body_detail) ? array_merge(['text'=>$this->content], (array) ($this->body_detail)) : ['text' => $this->content],
            'action_button' => !empty($this->action_button) ? (array) (($this->action_button)) : [],
            'campaign_expect' => !empty($this->campaign_expect) ? ($this->campaign_expect) : null,
            'type' => $this->type,
            'status' => $this->status,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
