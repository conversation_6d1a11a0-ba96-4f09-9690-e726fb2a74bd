<?php

namespace Modules\MessageCampaign\Providers;

use Illuminate\Support\ServiceProvider;
use Mo<PERSON>les\MessageCampaign\Entities\MessageCampaign;
use Modules\MessageCampaign\Repositories\Contracts\MessageCampaignInterface;
use Mo<PERSON>les\MessageCampaign\Repositories\Eloquents\MessageCampaignRepository;

class MessageCampaignServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerConfig();
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);

        $this->app->singleton(MessageCampaignInterface::class, fn() => new MessageCampaignRepository(new MessageCampaign));
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            module_path('MessageCampaign', 'Config/config.php') => config_path('messagecampaign.php'),
        ], 'config');
        $this->mergeConfigFrom(
            module_path('MessageCampaign', 'Config/config.php'),
            'messagecampaign'
        );
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }
}
