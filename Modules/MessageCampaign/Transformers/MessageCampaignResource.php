<?php

namespace Modules\MessageCampaign\Transformers;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource as Resource;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class MessageCampaignResource extends Resource
{
    use ElasticCollectionTrait;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => (array) $this->title,
            'body' => (array) $this->body,
            'action_button' => (array) $this->action_button,
            'campaign_expect' => $this->campaign_expect,
            'type' => $this->type,
            'status' => $this->status,
            'created_at' => Carbon::createFromFormat('Y-m-d H:i:s', $this->created_at)->format('d-m-Y'),
            'updated_at' => Carbon::createFromFormat('Y-m-d H:i:s', $this->updated_at)->format('d-m-Y'),
        ];
    }
}
