<?php

namespace Modules\MessageCampaign\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\MessageCampaign\Http\Requests\ValidateParams;
use Modules\MessageCampaign\Repositories\Contracts\MessageCampaignInterface;
use Modules\MessageCampaign\Transformers\MessageCampaignCollection;

class MessageCampaignController extends Controller
{
    /**
     * @var MessageCampaignInterface
     */
    protected $messageCampaignRepository;

    /**
     * Create instance TaxonomyController.
     */
    public function __construct(MessageCampaignInterface $messageCampaign)
    {
        $this->messageCampaignRepository = $messageCampaign;
    }

    /**
     * Search all company.
     */
    public function all(ValidateParams $request)
    {
        $size = $request->page_size ?? 10;

        $messageCampaign = $this->messageCampaignRepository->searchOnElasticsearch(
            $request->keyword,
            array_merge($request->all(), [
                'page_size' => $size,
                'status' => 1,
            ])
        );

        return MessageCampaignCollection::fromElasticsearch($messageCampaign, $size)
                                ->response()
                                ->setStatusCode(200);
    }
}
