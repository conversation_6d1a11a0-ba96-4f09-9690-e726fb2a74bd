<?php

namespace Modules\Advert\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Advert\Entities\Advert;
use Modules\Advert\Transformers\AdsResource;

class AdvertController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function all(Request $request)
    {
        $ads = Advert::active()->forMobile()->paginate($request->page_size ?? 10);

        return AdsResource::collection($ads);
    }
}
