<?php

namespace Modules\Advert\Entities;

use Illuminate\Database\Eloquent\Model;

class Group extends Model
{
    protected $table = 'mysa_groups';

    protected $connection = 'ads';

    protected $fillable = [];

    /**
     * @return string
     */
    public function ads()
    {
        return $this->belongsToMany(Advert::class, 'mysa_ads__groups_mm_mysa_groups__ads', 'groups', 'ads');
    }
}
