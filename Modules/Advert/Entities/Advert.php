<?php

namespace Modules\Advert\Entities;

use Illuminate\Database\Eloquent\Model;
use Modules\Blog\Repositories\Contracts\TechBlogRepositoryInterface;

class Advert extends Model
{
    protected $destinationTypeDetail = [
        'it-jobs' => 'SEARCH_JOB',
        'companies' => [
            'SEARCH_COMPANY',
            'COMPANY_DETAIL',
        ],
        'detail-jobs' => 'JOB_DETAIL',
        'blog' => 'WebView',
    ];

    protected $table = 'mysa_ads';

    protected $connection = 'ads';

    protected $fillable = [];

    protected $casts = [
        'type_banner' => 'array',
    ];

    public function getImageNameAttribute()
    {
        return preg_replace('/\W+/', '-', strtolower((string) ($this->type_banner['alt'] ?? null)));
    }

    public function getDestinationParamsAttribute()
    {
        $result = ['destinationType'=> null, 'destinationQueries' => []];
        \Log::info('--------destinationType---------');
        \Log::info($this->type_banner['url'] ?? 'null mm');
        if ($this->type_banner['url'] == 'https://topdev.vn/page/mobile-app-promotion') {
            $result = [
                'destinationType' => 'LOGIN',
                'destinationQueries' => [
                    'expect' => config('constant.ADS_DESTINATION_MOBILE_APP_PROMOTION.NON_LOGIN'),
                ],
            ];
        } elseif ($this->type_banner['url'] == 'https://topdev.page.link/personality-test') {
            $result = [
                'destinationType' => 'Outlink',
                'destinationQueries' => [
                    'expect' => "Personality test",
                    'url' => 'https://topdev.page.link/personality-test'
                ],
            ];
        } else {
            $url = ($this->type_banner != null) ? parse_url((string) $this->type_banner['url']) : null;
            $query = $url['query'] ?? null;
            $path = $url['path'] ?? null;

            $data = array_values(array_filter(explode('/', (string) $path)));
            $destinationType = $this->destinationTypeDetail;

            if (count($data) > 1 && array_key_exists($data[0], $destinationType) && !in_array('group', $data)) {
                $result['destinationQueries']['url'] = null;
                $result['destinationType'] = $destinationType[$data[0]];
                $params = explode('-', $data[1]);
                $destinationQueries = (end($params));
                if (preg_match('/k(t[0-9]+)?(l[0-9]+)?/i', $destinationQueries)) {
                    $result['destinationQueries']['keyword'] = (count(explode('+', $data[1])) > 1) ? explode('+', $data[1])[0] : null;
                    $destinationQueries = explode('l', $destinationQueries);
                    $result['destinationQueries']['regions_id'] = $destinationQueries[1] ?? null;
                    $result['destinationQueries']['skills_id'] = (isset($destinationQueries[0]) && preg_match('/kt[0-9]+/i', $destinationQueries[0])) ? str_replace('kt', '', $destinationQueries[0]) : null;

                    if ($data[0] == 'companies') {
                        $result['destinationType'] = $result['destinationType'][0];
                        $result['destinationQueries']['industries_id'] = null;
                    }

                    return $result;
                } elseif (preg_match("/^\d+$/i", $destinationQueries)) {
                    if ($data[0] == 'companies') {
                        $result['destinationType'] = $result['destinationType'][1];
                    }
                    $result['destinationQueries']['id'] = (string) $destinationQueries;

                    return $result;
                } elseif (preg_match('/[a-z-0-9]+/i', $data[1])) {
                    $blog = app(TechBlogRepositoryInterface::class)->searchOnElasticsearch(null, [
                        'page_size' => 1,
                        'permalink' => $this->type_banner['url'] ?? null,
                    ]);

                    $result['destinationQueries']['id'] = (isset($blog['hits']['hits'][0]['_source']['ID']) && !empty($blog['hits']['hits'][0]['_source']['ID'])) ? ((string) ($blog['hits']['hits'][0]['_source']['ID'])) : null;
                    $result['destinationQueries']['url'] = $this->type_banner['url'] ?? null;

                    return $result;
                }
            } else {
                $result = [
                    'destinationType' => 'WebView',
                    'destinationQueries' => [
                        'url' => $this->type_banner['url'] ?? null,
                    ],
                ];
            }
        }
        \Log::info('-----------------');
        \Log::info(print_r($result, true));

        return $result;
    }

    public function getImageResizeAttribute()
    {
        return resize_with_salt(
            $this->type_banner['src_lg'] ?? null,
            $this->type_banner['width'] ?? 10,
            0,
            $this->getImageNameAttribute(),
            'fit'
        );
    }

    public function getDestinationUrlAttribute()
    {
        return network_url('/out&a&c&' . $this->encrypt($this->getKey()));
    }

    public function getTrackingUrlAttribute()
    {
        return network_url('/in&v&a&i&' . $this->encrypt($this->getKey()));
    }

    public function getCustomImageResize($width, $height)
    {
        return resize_with_salt(
            $this->type_banner['src_lg'] ?? null,
            $width,
            $height,
            $this->getImageNameAttribute(),
            'fit'
        );
    }

    public function scopeActive($builder)
    {
        return $builder->where('status', 1);
    }

    public function scopeForMobile($builder)
    {
        return $builder->whereHas('groups', function ($query) {
            $query->where('mysa_groups.id', 47);
        });
    }

    private function encrypt($data)
    {
        $encrypt_key = 'CQus5siCOrjSgae9dO7tiFCIO85FTz92';

        $key = substr(md5($encrypt_key), 0, 24);

        $encrypted_data = openssl_encrypt($data, 'DES-EDE3', $key, OPENSSL_RAW_DATA, '');

        return base64_encode(base64_encode($encrypted_data));
    }

    /**
     * @return BelongsToMany
     */
    public function groups()
    {
        return $this->belongsToMany(Group::class, 'mysa_ads__groups_mm_mysa_groups__ads', 'ads', 'groups');
    }
}
