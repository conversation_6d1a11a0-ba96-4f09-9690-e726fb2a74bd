<?php

namespace Modules\Advert\Transformers;

use Illuminate\Http\Resources\Json\JsonResource as Resource;

class AdsResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        $destinationPrams = $this->getDestinationParamsAttribute();
        if ($destinationPrams['destinationType'] == 'LOGIN' && !empty(request()->user('api'))) {
            $destinationPrams['destinationType'] = 'SHOW_MESSAGE';
            $destinationPrams['destinationQueries'] = [
                'text' => config('constant.ADS_DESTINATION_MOBILE_APP_PROMOTION.HAS_LOGIN'),
            ];
        }

        return [
            'imageURL' => $this->getCustomImageResize($request->width ?? 10, $request->height ?? 10),
            'destinationURL' => $this->destination_url,
            'comName' => $this->type_banner['alt'] ?? null,
            'textLine1' => $this->type_banner['caption_top'] ?? null,
            'textLine2' => $this->type_banner['caption_bottom'] ?? null,
            'subimageURL' => $this->type_banner['src_sm'] ?? null,
            'linkTracking' => $this->tracking_url,
            'destinationType' => $destinationPrams['destinationType'],
            'destinationQueries' => (array) $destinationPrams['destinationQueries'],
        ];
    }
}
