<?php

namespace Modules\Blog\Repositories\Eloquents;

use Modules\Blog\Repositories\Contracts\TechBlogRepositoryInterface;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\TermsAggregation;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MultiMatchQuery;
use ONGR\ElasticsearchDSL\Query\MatchAllQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\IdsQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermsQuery;
use ONGR\ElasticsearchDSL\Sort\FieldSort;
use Redmix0901\Core\Repositories\Eloquent\BaseRepository;

class TechBlogEloquentRepository extends BaseRepository implements TechBlogRepositoryInterface
{
    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = null, $params = [], $fields = '*')
    {
        return $this->querySearch($keyword, $params, $fields)->raw();
    }

    public function searchOnEloquent($keyword = null, $params = [], $fields = '*')
    {
        if (isset($params['page_size'])) {
            return $this->querySearch($keyword, $params, $fields)->paginate($params['page_size']);
        }

        return $this->querySearch($keyword, $params, $fields)->get();
    }

    /**
     * @inheritdoc
     */
    private function querySearch($keyword, $params, $fields)
    {
        return $this->model->search('*', function ($client, $body) use ($keyword, $params) {
            // Pagination
            $size = $params['page_size'] ?? 10;
            $from = isset($params['page']) ? $size * ($params['page'] - 1) : 0;
            if ($this->hasQuery($params, 'offset') && $params['offset'] > 0) {
                $from = $from + $params['offset'] - 1;
            }

            $baseQuery = new BoolQuery();

            $baseQuery->add(new TermQuery('post_type', 'post'), BoolQuery::MUST);

            /*
             * Tìm kiếm keyword các trường post_content, post_title, post_name
             */
            if (!empty($keyword)) {
                $baseQuery->add(new MultiMatchQuery(['post_title', 'post_name'], $keyword, ['operator' => 'or']), BoolQuery::MUST);
            }

            /*
             *  Lọc theo tags
             */
            if ($this->hasQuery($params, 'tag')) {
                $baseQuery->add(new TermsQuery('terms.post_tag.name', explode(',', (string) $params['tag'])), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'qna_tag')) {
                $baseQuery->add(new MultiMatchQuery(['terms.post_tag.name'], $params['qna_tag'], ['operator' => 'or']), BoolQuery::MUST);
            }

            /*
             *  Lọc theo category
             */
            // if ($this->hasQuery($params, 'category')) {
            //     $baseQuery->add(new TermsQuery('terms.category.name', explode(',', $params['category'])), BoolQuery::MUST);
            // }
            if ($this->hasQuery($params, 'category')) {
                $baseQuery->add(new MultiMatchQuery(['terms.category.slug', 'terms.category.name'], $params['category'], ['operator' => 'or']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'permalink')) {
                $baseQuery->add(new TermQuery('permalink', $params['permalink']), BoolQuery::MUST);
            }

            /*
             *  Lọc author
             */
            if ($this->hasQuery($params, 'author')) {
                $baseQuery->add(new TermQuery('post_author.id', $params['author']), BoolQuery::MUST);
            }

            /*
             * Lọc các post có id sau
             */
            if (array_key_exists('ids', $params)) {
                $baseQuery->add(new IdsQuery(empty($params['ids']) ? [] : explode(',', (string) $params['ids'])), BoolQuery::MUST);
            }

            unset($params['page_size']);
            if (empty($keyword) && empty($params)) {
                $baseQuery = new MatchAllQuery();
            }

            $body->setSize($size);
            $body->setFrom($from);
            $body->addQuery($baseQuery);
            $body->addAggregation(new TermsAggregation('status', 'status'));

            if ($this->hasQuery($params, 'latest_modified')) {
                $body->addSort(new FieldSort('post_modified', null, ['order' => FieldSort::DESC]));
            } else {
                $body->addSort(new FieldSort('post_date', null, ['order' => FieldSort::DESC]));
            }

            return $client->search(['index' => $this->model->searchableAs(), 'body' => $body->toArray()])->asArray();
        })
        ->query(function ($builder) {
        });
    }

    /**
     * Check if has query.
     */
    private function hasQuery($query, $key)
    {
        return (bool) (isset($query[$key]) && !is_null($query[$key]));
    }
}
