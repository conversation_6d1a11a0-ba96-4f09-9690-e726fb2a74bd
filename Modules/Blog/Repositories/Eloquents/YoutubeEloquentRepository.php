<?php

namespace Modules\Blog\Repositories\Eloquents;

use Modules\Blog\Repositories\Contracts\YoutubeRepositoryInterface;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\TermsAggregation;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MultiMatchQuery;
use ONGR\ElasticsearchDSL\Query\MatchAllQuery;
use ONGR\ElasticsearchDSL\Sort\FieldSort;
use Redmix0901\Core\Repositories\Eloquent\BaseRepository;

class YoutubeEloquentRepository extends BaseRepository implements YoutubeRepositoryInterface
{
    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = null, $params = [], $fields = '*')
    {
        return $this->querySearch($keyword, $params, $fields)->raw();
    }

    public function searchOnEloquent($keyword = null, $params = [], $fields = '*')
    {
        if (isset($params['page_size'])) {
            return $this->querySearch($keyword, $params, $fields)->paginate($params['page_size']);
        }

        return $this->querySearch($keyword, $params, $fields)->get();
    }

    /**
     * @inheritdoc
     */
    private function querySearch($keyword, $params, $fields)
    {
        return $this->model->search('*', function ($client, $body) use ($keyword, $params) {
            // Pagination
            $size = $params['page_size'] ?? 10;
            $from = isset($params['page']) ? $size * ($params['page'] - 1) : 0;

            $baseQuery = new BoolQuery();

            /*
             * Tìm kiếm keyword các trường title
             */
            if (!empty($keyword)) {
                $baseQuery->add(new MultiMatchQuery(['title', '_id'], $keyword, ['operator' => 'or']), BoolQuery::MUST);
            }

            if (empty($keyword) && empty($params)) {
                $baseQuery = new MatchAllQuery();
            }

            $body->setSize($size);
            $body->setFrom($from);
            $body->addQuery($baseQuery);
            $body->addAggregation(new TermsAggregation('status', 'status'));

            $body->addSort(new FieldSort('published_at', null, ['order' => FieldSort::DESC]));

            return $client->search(['index' => $this->model->searchableAs(), 'body' => $body->toArray()])->asArray();
        })
        ->query(function ($builder) {
        });
    }

    /**
     * Check if has query.
     */
    private function hasQuery($query, $key)
    {
        return (bool) (isset($query[$key]) && !is_null($query[$key]));
    }
}
