<?php

namespace Modules\Blog\Entities;

use <PERSON><PERSON>\Scout\Searchable;
use Modules\Post\Entities\Post;

class TechBlog extends Post
{
    use Searchable;

    protected $connection = 'wordpress';

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'topdevvnblog-post-1';
    }

    /**
     * @inheritdoc
     */
    public function sluggable(): string
    {
        if (!empty($this->name)) {
            return $this->name;
        }

        return $this->display_name;
    }

    /**
     * @inheritdoc
     */
    public function ensureBeforeExpires()
    {
    }

    /**
     * @inheritdoc
     */
    public function ensureBeforeUnpublish()
    {
    }

    /**
     * Handle when someone viewed the blog.
     */
    public function recentlyViewedBy($visitor)
    {
    }
}
