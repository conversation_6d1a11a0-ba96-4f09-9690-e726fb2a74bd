<?php

namespace Modules\Blog\Entities;

use App\Traits\UseUuid;
use <PERSON><PERSON>\Scout\Searchable;
use Modules\Post\Entities\Post;
use Spatie\MediaLibrary\File;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class ItReportDatabase extends Post
{
    use Searchable;
    use UseUuid;

    /**
     * @inheritdoc
     */
    protected $type = 'it_report';

    /**
     * @inheritdoc
     */
    protected $attributes = [
        'slug' => null,
        'status' => 1,
    ];

    /**
     * @inheritdoc
     */
    protected $appends = [
        'files_it_report',
    ];

    /**
     * @inheritdoc
     */
    protected $fillable = [
        'title', 'content', 'files_itreport',
    ];

    /**
     * @inheritdoc
     */
    protected $casts = [
        'title' => 'string',
        'content' => 'string',
    ];

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'database_it_report_ams';
    }

    /**
     * @inheritdoc
     */
    public function ensureBeforeExpires()
    {
    }

    /**
     * @inheritdoc
     */
    public function ensureBeforeUnpublish()
    {
    }

    /**
     * @inheritdoc
     */
    public function sluggable(): string
    {
        return $this->title;
    }

    /**
     * Handle when someone viewed the blog.
     */
    public function recentlyViewedBy($visitor)
    {
    }

    public function checkStatus()
    {
        return $this->status > 0;
    }

    public function shouldBeSearchable()
    {
        return $this->checkStatus();
    }

    /**
     * Register media for model.
     *
     * @var void
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('files_itreport')
        ->acceptsFile(fn(File $file) => $file->mimeType === 'application/pdf');
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'content' => $this->content,
            'type' => $this->type,
            'status' => $this->status,

            /*
            |--------------------------------------------------------------------------
            | Timestamps
            |--------------------------------------------------------------------------
            */
            'expires_at' => empty($this->expires_at) ? null : $this->expires_at->format('Y-m-d H:i:s'),
            'published_at' => empty($this->published_at) ? null : $this->published_at->format('Y-m-d H:i:s'),
            'refreshed_at' => empty($this->refreshed_at) ? null : $this->refreshed_at->format('Y-m-d H:i:s'),
            'created_at' => empty($this->created_at) ? null : $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => empty($this->updated_at) ? null : $this->updated_at->format('Y-m-d H:i:s'),

            'files_itreport' => $this->getFirstMediaUrl('files_itreport'),
            'image_thumbnail' => $this->getFirstMediaUrl('image_thumbnail'),

            'detail_url' => $this->getDetailUrlAttribute(),
        ];
    }

    /**
     * Set file.
     *
     * @return mixed
     */
    public function setFilesItreportAttribute($values)
    {
        //delete old file
        if (!empty($this->getMedia('files_itreport')) && count($this->getMedia('files_itreport')) > 0) {
            collect($this->getMedia('files_itreport'))->each(function ($file, $index) {
                $this->deleteMedia($file);
            });
        }

        //create file
        collect($values)->each(function ($file, $index) {
            if (filter_var($file, FILTER_VALIDATE_URL)) {
                $this->addMediaFromUrl($file)
                    ->toMediaCollection('files_itreport');
            } elseif ($file instanceof UploadedFile) {
                $this->addMedia($file)
                    ->toMediaCollection('files_itreport');
            } elseif (is_string($file)) {
                $this->addMediaFromDisk($file)
                    ->preservingOriginal()
                    ->toMediaCollection('files_itreport');
            }
        });
    }

    /**
     * get file.
     *
     * @return mixed
     */
    public function getFilesItreportAttribute()
    {
        return collect($this->getMedia('files_itreport'))
            ->mapWithKeys(fn($media, $key) => [$media->getKey() => $media->getFullUrl()])
            ->toArray();
    }

    public function getDetailUrlAttribute()
    {
        return 'https://topdev.vn/page/bao-cao-it-viet-nam';
    }
}
