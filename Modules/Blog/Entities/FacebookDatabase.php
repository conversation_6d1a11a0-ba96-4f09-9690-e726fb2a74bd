<?php

namespace Modules\Blog\Entities;

use App\Traits\UseUuid;
use function GuzzleHttp\json_decode;
use <PERSON>vel\Scout\Searchable;
use Modules\Meta\Traits\Metable;
use Modules\Post\Entities\Post;

class FacebookDatabase extends Post
{
    use Searchable;
    use UseUuid;
    use Metable;

    /**
     * @inheritdoc
     */
    protected $type = 'facebook';

    /**
     * @inheritdoc
     */
    protected $attributes = [
        'slug' => null,
        'status' => 0,
    ];

    /**
     * @inheritdoc
     */
    protected $appends = [
        'images_facebook',
    ];

    /**
     * @inheritdoc
     */
    protected $fillable = [
        'title', 'content', 'published_at', 'refreshed_at', 'images_facebook',
    ];

    /**
     * @inheritdoc
     */
    protected $casts = [
        'title' => 'string',
        'content' => 'string',
        'published_at' => 'datetime:Y-m-d H:i:s',
        'refreshed_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * The attributes that should be append to native types.
     *
     * @var array
     */
    public $scopeMeta = [
        'video_facebook' => null,
    ];

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'database_facebook_ams';
    }

    /**
     * @inheritdoc
     */
    public function ensureBeforeExpires()
    {
    }

    /**
     * @inheritdoc
     */
    public function ensureBeforeUnpublish()
    {
    }

    /**
     * @inheritdoc
     */
    public function sluggable(): string
    {
        return $this->title;
    }

    /**
     * Handle when someone viewed the blog.
     */
    public function recentlyViewedBy($visitor)
    {
    }

    public function checkStatus()
    {
        return $this->status > 0;
    }

    public function shouldBeSearchable()
    {
        return $this->checkStatus();
    }

    /**
     * Register media for model.
     *
     * @var void
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('images_facebook');
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        \Log::info('message sosearchablearrat');

        return [
            'id' => $this->id,
            'title' => $this->title,
            'content' => $this->content,
            'type' => $this->type,
            'status' => $this->status,
            'published_at' => $this->published_at->format('Y-m-d H:i:s'),
            'refreshed_at' => $this->refreshed_at->format('Y-m-d H:i:s'),
            'images_facebook' => $this->images_facebook,
            'video_inline' => [
                'title' => !empty(($this->link_video)['title']) ? ($this->link_video)['title'] : null,
                'link_video' => !empty(($this->link_video)['link_video']) ? ($this->link_video)['link_video'] : null,
            ],
        ];
    }

    /**
     * Set images.
     *
     * @return mixed
     */
    public function setImagesFacebookAttribute($values)
    {
        collect($values)->each(function ($file, $index) {
            //add media if type is image
            if (($file['type'] == 'photo') || $file['type'] == 'album') {
                if (isset($file['subattachments']['data']) && count($file['subattachments']['data']) > 0) {
                    foreach ($file['subattachments']['data'] as $img) {
                        $this->addMediaFromUrl($img['media']['image']['src'])
                        ->toMediaCollection('images_facebook');
                    }
                } else {
                    $this->addMediaFromUrl($file['media']['image']['src'])
                        ->toMediaCollection('images_facebook');
                }
            }

            //add meta if type is video
            if (($file['type'] == 'video_inline')) {
                $video = [
                    'title' => $file['title'] ?? null,
                    'link_video' => $file['media']['source'],
                ];

                $this->setMetaAttribute(
                    [
                        'video_facebook' => json_encode($video),
                    ]
                );
            }
        });
    }

    /**
     * Set album image.
     *
     * @return mixed
     */
    public function getImagesFacebookAttribute()
    {
        return collect($this->getMedia('images_facebook'))
            ->mapWithKeys(fn($media, $key) => [$media->getKey() => $media->getFullUrl()])
            ->toArray();
    }

    public function getLinkVideoAttribute()
    {
        return (!empty($this->getMeta('video_facebook'))) ? (json_decode($this->getMeta('video_facebook'), true)) : null;
    }
}
