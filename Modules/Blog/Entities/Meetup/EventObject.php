<?php

namespace Modules\Blog\Entities\Meetup;

use Illuminate\Database\Eloquent\Model;

class EventObject extends Model
{
    protected $connection = 'meetup';

    /**
     * @inheritdoc
     */
    protected $table = 'event_objects';

    protected $casts = ['date_start' => 'datetime', 'date_end' => 'datetime', 'date_sale' => 'datetime', 'date_close' => 'datetime'];

    // protected $casts = [
    //     'date_start' => 'datetime:Y-m-d H:i:s',
    //     'date_end' => 'datetime:Y-m-d H:i:s',
    //     'date_sale' => 'datetime:Y-m-d H:i:s',
    //     'date_close' => 'datetime:Y-m-d H:i:s',
    // ];

    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Get city.
     */
    public function cities()
    {
        return $this->hasOne(City::class, 'id', 'city_id');
    }
}
