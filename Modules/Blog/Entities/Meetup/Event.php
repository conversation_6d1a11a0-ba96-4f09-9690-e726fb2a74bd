<?php

namespace Modules\Blog\Entities\Meetup;

use Illuminate\Database\Eloquent\Model;
use <PERSON>vel\Scout\Searchable;

class Event extends Model
{
    use Searchable;

    protected $connection = 'meetup';

    /**
     * @inheritdoc
     */
    protected $table = 'events';

    /**
     * @var string
     */
    protected $primaryKey = 'id';

    protected $casts = ['created_at' => 'datetime', 'updated_at' => 'datetime'];

    /**
     * Get the event object.
     */
    public function eventObject()
    {
        return $this->hasMany(EventObject::class, 'event_id');
    }

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'database_meetup_ams';
    }

    public function checkStatus()
    {
        return $this->status == 1;
    }

    public function shouldBeSearchable()
    {
        return $this->checkStatus();
    }

    /**
     * Get all event object.
     *
     * @return array
     */
    public function getObjectAttribute()
    {
        return $this->eventObject->map(fn($item, $key) => [
            'city' => [
                'id' => $item->city_id ?? null,
                'city_name' => $item->cities->city_name ?? null,
            ],
            'date_start' => $item->date_start->format('Y-m-d H:i:s'),
            'date_end' => $item->date_end->format('Y-m-d H:i:s'),
            'address' => $item->address,
            // 'ticket_price' => $item->ticket_price,
            // 'amount' => $item->amount,
            // 'amount_max' => $item->amount_max,
            'date_sale' => $item->date_sale->format('Y-m-d H:i:s'),
            'date_close' => $item->date_close->format('Y-m-d H:i:s'),
            'is_closed' => $item->is_closed,
        ])->all();
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        $this->loadMissing('eventObject');

        return [
            'id' => $this->id,
            'title' => $this->title,
            'alias' => $this->alias,
            'logo' => $this->logo,
            'banner' => $this->banner,
            'description' => $this->description,
            // 'price' => $this->price,
            // 'price_vip' => $this->price_vip,
            // 'discount_cv' => $this->discount_cv,
            'status' => $this->status,
            // 'price_share' => $this->price_share,
            // 'qty_share' => $this->qty_share,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
            'object' => (array) $this->object,
        ];
    }
}
