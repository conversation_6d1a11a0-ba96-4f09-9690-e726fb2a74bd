<?php

namespace Modules\Blog\Entities;

use App\Traits\UseUuid;
use <PERSON><PERSON>\Scout\Searchable;
use Modules\Meta\Traits\Metable;
use Modules\Post\Entities\Post;

class YoutubeDatabase extends Post
{
    use Searchable;
    use UseUuid;
    use Metable;

    /**
     * @inheritdoc
     */
    protected $type = 'youtube';

    /**
     * @inheritdoc
     */
    protected $attributes = [
        'slug' => null,
        'status' => 0,
    ];

    /**
     * @inheritdoc
     */
    protected $appends = [
        'images_youtube', 'meta_youtube',
    ];

    /**
     * @inheritdoc
     */
    protected $fillable = [
        'title', 'content', 'images_youtube', 'published_at', 'meta_youtube',
    ];

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'database_youtube_ams';
    }

    /**
     * @inheritdoc
     */
    protected $casts = [
        'title' => 'string',
        'content' => 'string',
        'published_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * The attributes that should be append to native types.
     *
     * @var array
     */
    public $scopeMeta = [
        'video_youtube' => null,
        'viewCount' => null,
    ];

    /**
     * @inheritdoc
     */
    public function ensureBeforeExpires()
    {
    }

    /**
     * @inheritdoc
     */
    public function ensureBeforeUnpublish()
    {
    }

    /**
     * @inheritdoc
     */
    public function sluggable(): string
    {
        return $this->title;
    }

    /**
     * Handle when someone viewed the blog.
     */
    public function recentlyViewedBy($visitor)
    {
    }

    public function checkStatus()
    {
        return $this->status > 0;
    }

    public function shouldBeSearchable()
    {
        return $this->checkStatus();
    }

    /**
     * Register media for model.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('images_youtube');
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        $meta_youtube = $this->meta_youtube;

        return [
            'id' => $this->id,
            'title' => $this->title,
            'content' => $this->content,
            'type' => $this->type,
            'status' => $this->status,
            'published_at' => $this->published_at->format('Y-m-d H:i:s'),
            'images_youtube' => $this->images_youtube,
            'link_youtube' => $meta_youtube['video_youtube'],
            'viewCount' => $meta_youtube['viewCount'],
        ];
    }

    /**
     * Set images.
     *
     * @return mixed
     */
    public function setImagesYoutubeAttribute($values)
    {
        collect($values)->each(function ($file, $index) {
            $file_name = $index . '-' . $file->width . 'x' . $file->height;
            $this->addMediaFromUrl($file->url)
                    ->usingFileName($file_name)
                    ->toMediaCollection('images_youtube');
        });
    }

    public function setMetaYoutubeAttribute($values)
    {
        $this->setMetaAttribute(
            [
                'video_youtube' => $values['video_youtube'],
                'viewCount' => $values['viewCount'],
            ]
        );
    }

    public function getMetaYoutubeAttribute()
    {
        return [
            'video_youtube' => (!empty($this->getMeta('video_youtube'))) ? $this->getMeta('video_youtube') : null,
            'viewCount' => (!empty($this->getMeta('viewCount'))) ? $this->getMeta('viewCount') : null,
        ];
    }

    /**
     * Set album image.
     *
     * @return mixed
     */
    public function getImagesYoutubeAttribute()
    {
        return collect($this->getMedia('images_youtube'))
            ->mapWithKeys(fn($media, $key) => [$media->getKey() => $media->getFullUrl()])
            ->toArray();
    }
}
