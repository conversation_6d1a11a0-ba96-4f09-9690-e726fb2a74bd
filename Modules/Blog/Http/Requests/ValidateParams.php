<?php

namespace Modules\Blog\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ValidateParams extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'page' => 'nullable|integer|min:1',
        ];
    }

    public function messages()
    {
        return [
            'page.integer' => "The format of page isn't correct?",
            'page.min' => 'Page require min is 1',
        ];
    }
}
