<?php

namespace Modules\Blog\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Modules\Blog\Http\Requests\ValidateParams;
use Modules\Blog\Repositories\Contracts\TechBlogRepositoryInterface;
use Mo<PERSON>les\Blog\Transformers\PostTechblogCollection;
use Modules\Blog\Transformers\PostTechblogResource;

class APITechblogController extends Controller
{
    /**
     * @var TechBlogRepositoryInterface
     */
    protected $techblogRepository;

    /**
     * Create instance TaxonomyController.
     */
    public function __construct(TechBlogRepositoryInterface $techblog)
    {
        $this->techblogRepository = $techblog;
    }

    /**
     * Search all post techblog.
     */
    public function all(ValidateParams $request)
    {
        $size = $request->page_size ?? 10;
        $category = $request->get('category');

        $posts = $this->techblogRepository->searchOnElasticsearch(
            strtolower(str_replace(['++', '#'], '', (string) $request->keyword)),
            array_filter(
                array_merge($request->all(), [
                    'page_size' => $size,
                    'category' => $category,
                ])
            )
        );

        return PostTechblogCollection::fromElasticsearch($posts, $size)
                                ->response()
                                ->setStatusCode(200);
    }

    /**
     * Show detail techblog.
     * @param int $id
     * @return PostTechblogResource
     */
    public function showTechblog($id)
    {
        $post = $this->techblogRepository->searchOnElasticsearch(null, [
            'ids' => $id,
        ]);

        return PostTechblogResource::fromElasticsearch($post);
    }

    /**
     * Search all post has category is HR.
     */
    public function categoryHr(ValidateParams $request)
    {
        $size = $request->page_size ?? 10;

        $posts = $this->techblogRepository->searchOnElasticsearch(
            $request->keyword,
            array_merge($request->all(), [
                //
                'category' => 'HR',
            ])
        );

        return PostTechblogCollection::fromElasticsearch($posts, $size)
                                ->response()
                                ->setStatusCode(200);
    }

    /**
     * Search all post has category slug is lap-trinh.
     */
    public function categoryDevelopment(ValidateParams $request)
    {
        $size = $request->page_size ?? 10;

        $posts = $this->techblogRepository->searchOnElasticsearch(
            $request->keyword,
            array_merge($request->all(), [
                //
                'category' => 'lap-trinh',
            ])
        );

        return PostTechblogCollection::fromElasticsearch($posts, $size)
                                ->response()
                                ->setStatusCode(200);
    }
}
