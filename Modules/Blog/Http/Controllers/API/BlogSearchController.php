<?php

namespace Modules\Blog\Http\Controllers\API;

use Illuminate\Routing\Controller;
use Modules\Blog\Repositories\Contracts\TechBlogRepositoryInterface;
use Modules\Blog\Transformers\PostTechblogCollection;
use Modules\Job\Http\Requests\JobSearchRequest;
use Modules\Taxonomy\Repositories\Contracts\TaxonomyRepositoryInterface;
use Redmix0901\ElasticResource\ElasticCollection;

class BlogSearchController extends Controller
{
    
    protected TechBlogRepositoryInterface $blogRepository;

    public function __construct(TechBlogRepositoryInterface $blogRepository) {
        $this->blogRepository = $blogRepository;
    }

    public function index(JobSearchRequest $request) {
        $skills_str = null;
        $pageSize = $request->page_size ?? 15;
        $keyword = $request->get('keyword');

        if (!empty($request->skills_id)) {
            $taxonomies = app(TaxonomyRepositoryInterface::class)->searchOnElasticsearch(null, [
                'taxonomy' => 'skills',
                'ids' => $request->skills_id,
                'page_size' => $request->page_size ?? 100000,
            ]);

            $taxonomiesCollection = (new ElasticCollection($taxonomies));
            if ($taxonomiesCollection->total() > 0) {
                $arrText = collect($taxonomiesCollection->hits()['hits'])->map(fn($taxonomy) => $taxonomy['_source']['text_vi'])->all();
                $skills_str = implode(',', $arrText);
            }
        }

        $keywordBlog = !empty($skills_str) ? $skills_str : $keyword;
        $blogs = $this->blogRepository->searchOnElasticsearch(
            strtolower(str_replace(['++', '#'], '', (string) $keywordBlog)),
            array_filter(
                array_merge($request->all(), [
                    'page_size' => $pageSize,
                ])
            )
        );

        return PostTechblogCollection::fromElasticsearch($blogs, $pageSize)
            ->response();
    }
}
