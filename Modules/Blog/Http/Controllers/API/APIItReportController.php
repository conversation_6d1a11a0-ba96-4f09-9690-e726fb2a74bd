<?php

namespace Modules\Blog\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Modules\Blog\Http\Requests\ValidateParams;
use Modules\Blog\Repositories\Contracts\ItReportRepositoryInterface;
use Modules\Blog\Transformers\ItReportCollection;

class APIItReportController extends Controller
{
    /**
     * @var ItReportRepositoryInterface
     */
    protected $itReportRepository;

    /**
     * Create instance.
     */
    public function __construct(ItReportRepositoryInterface $itReport)
    {
        $this->itReportRepository = $itReport;
    }

    /**
     * Search all meetup.
     */
    public function all(ValidateParams $request)
    {
        $size = $request->page_size ?? 10;

        $posts = $this->itReportRepository->searchOnElasticsearch(
            $request->keyword,
            array_merge($request->all(), [
                //
            ])
        );

        return ItReportCollection::fromElasticsearch($posts, $size)
                                ->response()
                                ->setStatusCode(200);
    }
}
