<?php

namespace Modules\Blog\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Modules\Blog\Http\Requests\ValidateParams;
use Modules\Blog\Repositories\Contracts\MeetupRepositoryInterface;
use Modules\Blog\Transformers\EventMeetupCollection;

class ApiMeetupController extends Controller
{
    /**
     * @var MeetupRepositoryInterface
     */
    protected $meetupRepository;

    /**
     * Create instance.
     */
    public function __construct(MeetupRepositoryInterface $meetup)
    {
        $this->meetupRepository = $meetup;
    }

    /**
     * Search all meetup.
     */
    public function all(ValidateParams $request)
    {
        return response()->json(['data' => null]);

        $size = $request->page_size ?? 10;

        $posts = $this->meetupRepository->searchOnElasticsearch(
            $request->keyword,
            array_merge($request->all(), [
                //
            ])
        );

        return EventMeetupCollection::fromElasticsearch($posts, $size)
                                ->response()
                                ->setStatusCode(200);
    }
}
