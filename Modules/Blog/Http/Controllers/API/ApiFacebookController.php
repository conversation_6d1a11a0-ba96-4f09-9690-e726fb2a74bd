<?php

namespace Modules\Blog\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Modules\Blog\Http\Requests\ValidateParams;
use Modules\Blog\Repositories\Contracts\FacebookRepositoryInterface;
use Modules\Blog\Transformers\PostFacebookCollection;

class ApiFacebookController extends Controller
{
    /**
     * @var FacebookRepositoryInterface
     */
    protected $facebookRepository;

    /**
     * Create instance TaxonomyController.
     */
    public function __construct(FacebookRepositoryInterface $fb)
    {
        $this->facebookRepository = $fb;
    }

    /**
     * Search all company.
     */
    public function all(ValidateParams $request)
    {
        $size = $request->page_size ?? 10;

        $posts = $this->facebookRepository->searchOnElasticsearch(
            $request->keyword,
            array_merge($request->all(), [
                //
            ])
        );

        return PostFacebookCollection::fromElasticsearch($posts, $size)
                                ->response()
                                ->setStatusCode(200);
    }
}
