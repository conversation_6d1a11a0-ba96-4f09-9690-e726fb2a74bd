<?php

namespace Modules\Blog\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Modules\Blog\Http\Requests\ValidateParams;
use Modules\Blog\Repositories\Contracts\YoutubeRepositoryInterface;
use Modules\Blog\Transformers\PostYoutubeCollection;

class ApiYoutubeController extends Controller
{
    /**
     * @var YoutubeRepositoryInterface
     */
    protected $youtubeRepository;

    /**
     * Create instance TaxonomyController.
     */
    public function __construct(YoutubeRepositoryInterface $youtube)
    {
        $this->youtubeRepository = $youtube;
    }

    /**
     * Search all company.
     */
    public function all(ValidateParams $request)
    {
        $size = $request->page_size ?? 10;

        $posts = $this->youtubeRepository->searchOnElasticsearch(
            $request->keyword,
            array_merge($request->all(), [
                //
            ])
        );

        return PostYoutubeCollection::fromElasticsearch($posts, $size)
                                ->response()
                                ->setStatusCode(200);
    }
}
