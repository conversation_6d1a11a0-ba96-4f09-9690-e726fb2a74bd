<?php

namespace Modules\Blog\Transformers;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource as Resource;

class EventObjectMeetupResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'city' => [
                'id' => $this['city']['id'] ?? null,
                'city_name' => $this['city']['city_name'] ?? null,
            ],
            'address' => (string) $this['address'],
            'date_start' => Carbon::createFromFormat('Y-m-d H:i:s', $this['date_start'])->format('d-m-Y'),
            'date_end' => Carbon::createFromFormat('Y-m-d H:i:s', $this['date_end'])->format('d-m-Y'),
            'date_sale' => Carbon::createFromFormat('Y-m-d H:i:s', $this['date_sale'])->format('d-m-Y'),
            'date_close' => Carbon::createFromFormat('Y-m-d H:i:s', $this['date_close'])->format('d-m-Y'),
            'is_closed' => (int) $this['is_closed'],
        ];
    }
}
