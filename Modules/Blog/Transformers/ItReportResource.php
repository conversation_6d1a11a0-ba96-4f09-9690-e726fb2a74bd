<?php

namespace Modules\Blog\Transformers;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource as Resource;

class ItReportResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => (int) $this->id,
            'title' => (string) $this->title,
            'content' => (string) $this->content,
            'status' => (string) $this->status,
            'created_at' => Carbon::createFromFormat('Y-m-d H:i:s', $this->created_at)->format('d-m-Y'),
            'updated_at' => Carbon::createFromFormat('Y-m-d H:i:s', $this->updated_at)->format('d-m-Y'),
            'expires_at' => (!empty($this->published_at)) ? Carbon::createFromFormat('Y-m-d H:i:s', $this->expires_at)->format('d-m-Y') : null,
            'published_at' => (!empty($this->published_at)) ? Carbon::createFromFormat('Y-m-d H:i:s', $this->published_at)->format('d-m-Y') : null,
            'refreshed_at' => (isset($this->published_at)) ? Carbon::createFromFormat('Y-m-d H:i:s', $this->refreshed_at)->format('d-m-Y') : null,
            'refreshed_at' => (isset($this->published_at)) ? Carbon::createFromFormat('Y-m-d H:i:s', $this->refreshed_at)->format('d-m-Y') : null,
            'files_itreport' => (string) $this->files_itreport,
            'image_thumbnail' => (string) $this->image_thumbnail,
            'detail_url' => (string) $this->detail_url,
        ];
    }
}
