<?php

namespace Modules\Blog\Transformers;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource as Resource;
use Illuminate\Support\Arr;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class PostTechblogResource extends Resource
{
    use ElasticCollectionTrait;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => (int) $this->ID,
            'post_content' => (string) $this->post_content,
            'post_title' => (string) $this->post_title,
            'post_name' => (string) $this->post_name,
            'permalink' => (string) $this->permalink,
            'post_excerpt' => static::limitText(strip_tags((string) $this->post_content), 250),
            'post_modified' => Carbon::createFromFormat('Y-m-d H:i:s', $this->post_modified)->format('d-m-Y'),
            'post_date' => Carbon::createFromFormat('Y-m-d H:i:s', $this->post_date)->format('m-d-Y'),
            'image' => Arr::get($this->thumbnail, 'src'),
            'terms' => empty($this->terms) ? null : (array) ($this->terms),
            'author' => empty($this->post_author) ? null : (array) ($this->post_author),
        ];
    }

    public static function limitText($text, $limit)
    {
        if (str_word_count((string) $text, 0) > $limit) {
            $words = str_word_count((string) $text, 2);
            $pos = array_keys($words);
            $text = substr((string) $text, 0, $pos[$limit]) . ' [...]';
        }

        return $text;
    }
}
