<?php

namespace Modules\Blog\Transformers;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource as Resource;

class PostYoutubeResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => (int) $this->id,
            'title' => (string) $this->title,
            'content' => (string) $this->content,
            'type' => (string) $this->type,
            'published_at' => Carbon::createFromFormat('Y-m-d H:i:s', $this->published_at)->format('d-m-Y'),
            'images_youtube' => (array) $this->images_youtube,
            'link_youtube' => (string) $this->link_youtube,
            'viewCount' => (int) $this->viewCount,
        ];
    }
}
