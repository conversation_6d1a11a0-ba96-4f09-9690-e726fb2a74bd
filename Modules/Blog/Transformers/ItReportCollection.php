<?php

namespace Modules\Blog\Transformers;

use Illuminate\Http\Resources\Json\ResourceCollection;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class ItReportCollection extends ResourceCollection
{
    use ElasticCollectionTrait;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return ItReportResource::collection($this->collection);
    }

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function withResponse($request, $response)
    {
        $elasticCollection = static::getElasticCollection();
        $json = array_merge([
            '_type' => 'it_report',
            'aggregations' => empty($elasticCollection) ? [] : $elasticCollection->aggregations(),
        ], json_decode($response->getContent(), true));

        $response->setContent(json_encode($json));
    }
}
