<?php

namespace Modules\Blog\Transformers;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource as Resource;

class EventMeetupResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => (int) $this->id,
            'title' => (string) $this->title,
            'alias' => (string) $this->alias,
            'logo' => (string) meetup_url($this->logo),
            'banner' => (string) meetup_url($this->banner),
            'description' => (string) $this->description,
            'status' => (string) $this->status,
            'created_at' => Carbon::createFromFormat('Y-m-d H:i:s', $this->created_at)->format('d-m-Y'),
            'updated_at' => Carbon::createFromFormat('Y-m-d H:i:s', $this->updated_at)->format('d-m-Y'),
            'object' => EventObjectMeetupResource::collection($this->object),
        ];
    }
}
