<?php

namespace Modules\Blog\Transformers;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource as Resource;

class PostFacebookResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => (int) $this->id,
            'title' => (string) $this->title,
            'content' => (string) $this->content,
            'type' => (string) $this->type,
            'published_at' => Carbon::createFromFormat('Y-m-d H:i:s', $this->published_at)->format('d-m-Y'),
            'refreshed_at' => Carbon::createFromFormat('Y-m-d H:i:s', $this->refreshed_at)->format('d-m-Y'),
            'images_facebook' => (array) $this->images_facebook,
            'video_inline' => [
                'title' => isset(($this->video_inline)['title']) ? (string) ($this->video_inline)['title'] : null,
                'link_video' => isset(($this->video_inline)['link_video']) ? (string) ($this->video_inline)['link_video'] : null,
            ],
        ];
    }
}
