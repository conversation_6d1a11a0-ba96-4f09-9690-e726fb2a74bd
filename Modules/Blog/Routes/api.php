<?php

use Illuminate\Support\Facades\Route;
use Modules\Blog\Http\Controllers\API\BlogSearchController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('/blogs')->group(function () {
    Route::get('/', 'APITechblogController@all');
    Route::get('post/{id}', 'APITechblogController@showTechblog');

    Route::get('/hr', 'APITechblogController@categoryHr');
    Route::get('/development', 'APITechblogController@categoryDevelopment');
    Route::get('/youtube', 'ApiYoutubeController@all');
    Route::get('/meetup', 'ApiMeetupController@all');
    Route::get('/facebook', 'ApiFacebookController@all');
    Route::get('/it-report', 'APIItReportController@all');
    Route::get('/search', [BlogSearchController::class, 'index'])
        ->middleware([
            'ensure_frontend_requests_are_stateful',
            'setlocaleapi'
        ])
        ->name('blog_search');;
});
