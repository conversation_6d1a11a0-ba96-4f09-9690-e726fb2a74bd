<?php

namespace Modules\Blog\Providers;

use Facebook\Facebook;
use Illuminate\Database\Eloquent\Factory;
use Illuminate\Support\ServiceProvider;
use Modules\Blog\Entities\FacebookDatabase;
use Modules\Blog\Entities\ItReportDatabase;
use Modules\Blog\Entities\Meetup\Event;
use Modules\Blog\Entities\TechBlog;
use Modules\Blog\Entities\YoutubeDatabase;
use Modules\Blog\Repositories\Contracts\FacebookRepositoryInterface;
use Modules\Blog\Repositories\Contracts\ItReportRepositoryInterface;
use Modules\Blog\Repositories\Contracts\MeetupRepositoryInterface;
use Modules\Blog\Repositories\Contracts\TechBlogRepositoryInterface;
use Modules\Blog\Repositories\Contracts\YoutubeRepositoryInterface;
use Modules\Blog\Repositories\Eloquents\FacebookEloquentRepository;
use Modules\Blog\Repositories\Eloquents\ItReportEloquentRepository;
use Modules\Blog\Repositories\Eloquents\MeetupEloquentRepository;
use Modules\Blog\Repositories\Eloquents\TechBlogEloquentRepository;
use Modules\Blog\Repositories\Eloquents\YoutubeEloquentRepository;

class BlogServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->registerFactories();
        $this->loadMigrationsFrom(module_path('Blog', 'Database/Migrations'));
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);
        $this->app->singleton(Facebook::class, function ($app) {
            $fb = new Facebook([
                'app_id' => env('FACEBOOK_APP_ID'),
                'app_secret' => env('FACEBOOK_APP_SECRET'),
            ]);
            $fb->setDefaultAccessToken(env('FACEBOOK_ACCESS_TOKEN'));

            return $fb;
        });

        $this->app->singleton(FacebookRepositoryInterface::class, fn() => new FacebookEloquentRepository(
            new FacebookDatabase
        ));

        $this->app->singleton(YoutubeRepositoryInterface::class, fn() => new YoutubeEloquentRepository(
            new YoutubeDatabase
        ));

        $this->app->singleton(TechBlogRepositoryInterface::class, fn() => new TechBlogEloquentRepository(
            new TechBlog
        ));

        $this->app->singleton(MeetupRepositoryInterface::class, fn() => new MeetupEloquentRepository(
            (new Event)
        ));

        $this->app->singleton(ItReportRepositoryInterface::class, fn() => new ItReportEloquentRepository(
            (new ItReportDatabase())
        ));
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            module_path('Blog', 'Config/config.php') => config_path('blog.php'),
        ], 'config');
        $this->mergeConfigFrom(
            module_path('Blog', 'Config/config.php'),
            'blog'
        );
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews()
    {
        $viewPath = resource_path('views/modules/blog');

        $sourcePath = module_path('Blog', 'Resources/views');

        $this->publishes([
            $sourcePath => $viewPath,
        ], 'views');

        $this->loadViewsFrom(array_merge(array_map(fn($path) => $path . '/modules/blog', \Config::get('view.paths')), [$sourcePath]), 'blog');
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/blog');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'blog');
        } else {
            $this->loadTranslationsFrom(module_path('Blog', 'Resources/lang'), 'blog');
        }
    }

    /**
     * Register an additional directory of factories.
     *
     * @return void
     */
    public function registerFactories()
    {
        if (!app()->environment('production') && $this->app->runningInConsole()) {
            app(Factory::class)->load(module_path('Blog', 'Database/factories'));
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }
}
