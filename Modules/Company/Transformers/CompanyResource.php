<?php

namespace Modules\Company\Transformers;

use App\Traits\AddDataResource;
use App\Traits\CustomFieldResource;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource as Resource;
use Illuminate\Http\Request;
use Modules\Job\Transformers\JobCollection;
use Redmix0901\ElasticResource\ElasticCollection;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class CompanyResource extends Resource
{
    use ElasticCollectionTrait;
    use CustomFieldResource;
    use AddDataResource;

    /**
     * Transform the resource collection into an array.
     *
     * @param  Request  $request
     *
     * @return array
     */
    public function toArray($request): array
    {
        $followed_companies = (isset((static::$listDataUser)['followed_companies']) && is_array((static::$listDataUser)['followed_companies'])) ? (static::$listDataUser)['followed_companies'] : null;

        $locale = $request->locale ?: 'en_US';
        $this->localeUrl($locale, 'detail_url');

        $fields = isset($request->fields['company'])
            ? explode(',', (string) $request->fields['company']) : [];

        $description_arr = (in_array('description_arr', $fields) && !empty($this->description_str)) ? \transformContent((string) $this->description, $this->id) : [];

        $companyId = $this->id;

        $jobs = null;
        $latestJobsxx = [];
        //when get just with resource
        if (static::$shouldGetRelation) {
            $latestJobsxx = preg_grep('/^(latest_jobs_)[0-9]+$/i', $fields);
            if (empty($latestJobsxx) && in_array('latest_jobs', $fields)) {
                $latestJobsxx = [];
                $latestJobsxx[] = 'latest_jobs_10000';
            }

            if (count($latestJobsxx) > 0) {
                $size = (explode('_', (string) reset($latestJobsxx)))[2] ?? 10;
                $jobs = $this->searchJobs($this->id, $size);
                if ($jobs != null) {
                    $jobs = (JobCollection::fromElasticsearch($jobs, $size))->listDataUser(static::$listDataUser)->shouldGetRelation(false);
                }
            }
        } else {
            //when get just with collection
            if (isset((static::$dataRelation)['jobs']['size'])) {
                $latestJobsxx = ['latest_jobs_' . ((static::$dataRelation)['jobs']['size'] ?? 10000)];
            }
            if (isset((static::$dataRelation)['jobs']['data'][$this->id])) {
                $jobs = new JobCollection(((static::$dataRelation)['jobs']['data'][$this->id])->take((static::$dataRelation)['jobs']['size'] ?? 10000));
                $jobs->shouldGetRelation(false);
            }
        }

        $data = [
            'id' => $this->id,
            'display_name' => $this->display_name,
            'image_logo' => $this->image_logo,
            'slug' => $this->slug,
            'detail_url' => $this->detail_url,

            'email' => $this->when(in_array('email', $fields), !empty(static::$listDataUser['id'] ?? null) ? (string) $this->email : ''),
            'phone' => $this->when(in_array('phone', $fields), !empty(static::$listDataUser['id'] ?? null) ? (string) $this->phone : ''),

            'logo_group' => $this->when(in_array('logo_group', $fields), $this->logo_group ?? null),
            'cover_group' => $this->when(in_array('cover_group', $fields), $this->cover_group ?? null),

            'company_id' => $this->when(in_array('company_id', $fields), (int) $this->id),
            'description' => $this->when(in_array('description', $fields), (string) $this->description),
            'description_str' => $this->when(in_array('description_str', $fields), (string) $this->description_str),
            'description_arr' => $this->when(in_array('description_arr', $fields), $description_arr),
            'draft' => $this->when(in_array('draft', $fields), (array) $this->draft),
            'website' => $this->when(in_array('website', $fields), (string) $this->website),
            'tagline' => $this->when(in_array('tagline', $fields), (string) $this->tagline),
            'company_size' => $this->when(in_array('company_size', $fields), (string) $this->company_size),
            'nationalities' => $this->when(in_array('nationalities', $fields), (array) $this->nationalities),
            'products' => $this->when(in_array('products', $fields), (array) $this->products),
            'introduces' => $this->when(in_array('introduces', $fields), (array) $this->introduces),
            'employees' => $this->when(in_array('employees', $fields), (array) $this->employees), //---
            'social_network' => $this->when(in_array('social_network', $fields), (array) $this->social_network),
            'addresses' => $this->when(in_array('addresses', $fields), (array) $this->addresses),
            'job_opening_url' => $this->when(in_array('job_opening_url', $fields), (string) $this->job_opening_url),
            'follow_url' => $this->when(in_array('follow_url', $fields), (string) $this->follow_url),
            'num_job_openings' => $this->when(in_array('num_job_openings', $fields), (int) $this->num_job_openings),
            'num_jobs' => $this->when(in_array('num_jobs', $fields), (int) $this->num_jobs),
            'num_viewers' => $this->when(in_array('num_viewers', $fields), (int) $this->num_viewers),
            'num_followeres' => $this->when(in_array('num_followeres', $fields), (int) $this->num_followeres),
            'num_employees' => $this->when(in_array('num_employees', $fields), (string) $this->num_employees), //---
            'image_cover' => $this->when(in_array('image_cover', $fields), empty($this->image_cover) ? (!current($this->image_galleries) ? null : current($this->image_galleries)) : $this->image_cover),
            'image_galleries' => $this->when(in_array('image_galleries', $fields), (array) $this->image_galleries),
            'benefits' => $this->when(in_array('benefits', $fields), (array) $this->handleBenefits($this->benefits)),
            'members' => $this->when(in_array('members', $fields), (array) $this->members), //---
            'nationalities_arr' => $this->when(in_array('nationalities_arr', $fields), (array) $this->nationalities_arr),
            'nationalities_str' => $this->when(in_array('nationalities_str', $fields), $this->nationalities_str),
            'features' => $this->when(in_array('features', $fields), (array) $this->features),

            'modified' => $this->when(in_array('modified', $fields), (array) (empty($this->updated_at) ? [] : [
                'date' => Carbon::createFromFormat('Y-m-d H:i:s', $this->updated_at)->format('d-m-Y'),
                'since' => Carbon::createFromFormat('Y-m-d H:i:s', $this->updated_at)->locale($locale)->diffForHumans(),
            ])),
            'status_display' => $this->when(in_array('status_display', $fields), (string) $this->status_display),
            'extra_skills' => $this->when(in_array('extra_skills', $fields), (array) $this->extra_skills), //---
            'skills_ids' => $this->when(in_array('skills_ids', $fields), (array) $this->skills_ids), //---
            'skills_str' => $this->when(in_array('skills_str', $fields), (string) $this->skills_str),
            'skills_arr' => $this->when(in_array('skills_arr', $fields), (array) $this->skills_arr),
            'categories_ids' => $this->when(in_array('categories_ids', $fields), (array) $this->categories_ids), //---
            'categories_str' => $this->when(in_array('categories_str', $fields), (string) $this->categories_str),
            'categories_arr' => $this->when(in_array('categories_arr', $fields), (array) $this->categories_arr),
            'industries_ids' => $this->when(in_array('industries_ids', $fields), (array) $this->industries_ids),
            'industries_arr' => $this->when(in_array('industries_arr', $fields), (array) $this->industries_arr),
            'industries_str' => $this->when(in_array('industries_str', $fields), (string) $this->industries_str),
            'spotlight' => $this->when(in_array('spotlight', $fields), (string) $this->spotlight),
            'is_spotlight' => $this->when(in_array('spotlight', $fields), (bool) $this->is_spotlight),
            'highlight' => $this->when(in_array('highlight', $fields), (string) $this->highlight),
            'is_highlight' => $this->when(in_array('highlight', $fields), (bool) $this->is_highlight),

            'latest_jobs' => $this->when(count($latestJobsxx) > 0, $jobs),
            'is_followed' => ($followed_companies != null && in_array($this->id, $followed_companies)),
            'news' => $this->when(
                in_array('news', $fields),
                collect((array) $this->news)->map(function ($new) use ($companyId) {
                    $new['name'] = !empty($new['name']) ? html_entity_decode((string) $new['name']) : null;
                    $new['publish'] = !empty($new['publish']) ? Carbon::parse($new['publish'])->format('d-m-Y H:i:s') : null;
                    if (!empty($new['description'])) {
                        $new['description_arr'] = \transformContent((string) $new['description'], $companyId);
                    }

                    return $new;
                })->toArray()
            ),
            'group_url' => $this->when(in_array('group_url', $fields), ($this->id == 83771) ? 'https://topdev.vn/companies/group/korean-it-companies-83771' : null),

            /*'meta_title' => $this->when(in_array('meta_title', $fields), $locale == 'en_US' ? $this->meta_title_en : $this->meta_title_vi),
            'meta_keywords' => $this->when(in_array('meta_keywords', $fields), $locale == 'en_US' ? $this->meta_keywords_en : $this->meta_keywords_vi),
            'meta_description' => $this->when(in_array('meta_description', $fields), $locale == 'en_US' ? $this->meta_description_en : $this->meta_description_vi),*/

            'schema_local_business' => $this->when(in_array('schema_local_business', $fields), (string) $this->schema_local_business),
            'schema_job_posting' => $this->when(in_array('schema_job_posting', $fields), (string) $this->schema_job_posting),
            'schema_employer_aggregate_rating' => $this->when(in_array('schema_employer_aggregate_rating', $fields), isset($this->schema_employer_aggregate_rating) ? ((string) $this->schema_employer_aggregate_rating) : null),

            'faqs' => $this->when(in_array('faqs', $fields), $this->faqs),
            'recruitment_process' => $this->when(in_array('recruitment_process', $fields), $this->recruitment_process),

            'logo' => $this->when(in_array('logo', $fields), (isset($this->logo) && !empty($this->logo)) ? (array) $this->logo : []),
            'cover' => $this->when(in_array('cover', $fields), (isset($this->cover) && !empty($this->cover)) ? (array) $this->cover : []),
        ];

        $arrSalt = preg_grep('/[a-zA-Z_]+(_([0-9]+)x([0-9]+))/i', $fields);
        if ($arrSalt) {
            $data = array_merge($this->saltForNameField($arrSalt), $data);
        }

        return $data;
    }

    //search Jobs with id of Company by ES
    private function searchJobs($id, $size)
    {
        $jobs = (app(\Modules\Job\Repositories\Contracts\JobRepositoryInterface::class))->searchOnElasticsearch(
            null,
            [
                'company' => $id,
                'ordering' => 'newest_job',
                'page_size' => $size,
                'status' => 3,
            ]
        );

        $total = (new ElasticCollection($jobs))->total();

        return ($total > 0) ? ($jobs) : null;
    }

    /**
     * handle string benefit html
     * @param $benefits
     * @return array
     */
    public function handleBenefits($benefits)
    {
        if (count($benefits) > 1) return $benefits;
        if (request()->header('X-Topdev-Source') == 'MobileApp') {
            if (request()->has('app_version')) {
                $version = request()->input('app_version');

                $version = (int) str_replace('.', '', $version);

                if ($version >= 233) {
                    return $benefits;
                }
            }

            $benefits = collect($benefits)->pluck('value')->implode('<br>');
            $benefits = strip_tags($benefits);

            $benefits = html_entity_decode($benefits, ENT_QUOTES, 'UTF-8');

            $benefits = preg_replace('/\s+/', ' ', $benefits);

            return [
                [
                    'icon' => null,
                    'value' => trim($benefits)
                ]
            ];
        }
        return $benefits;
    }
}
