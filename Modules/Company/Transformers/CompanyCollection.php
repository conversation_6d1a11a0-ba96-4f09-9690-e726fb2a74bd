<?php

namespace Modules\Company\Transformers;

use App\Traits\AddDataCollection;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Modules\Job\Transformers\JobCollection;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class CompanyCollection extends ResourceCollection
{
    use ElasticCollectionTrait;
    use AddDataCollection;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        if ($this->shouldGetRelation == true) {
            $this->dataRelation = $this->dataRelation($request->all());
        }

        return CompanyResource::listDataUser($this->listDataUser)->shouldGetRelation(false)->dataRelation($this->dataRelation)->collection($this->collection);
    }

    public function withResponse($request, $response)
    {
        $elasticCollection = static::getElasticCollection();

        $json = array_merge([
            '_type' => 'companies',
            'aggregations' => empty($elasticCollection) ? [] : $this->transformerAggregations($elasticCollection->aggregations()),
        ], json_decode($response->getContent(), true));

        $response->setContent(json_encode($json));
    }

    public function transformerAggregations($aggregations)
    {
        $industries_ids = collect($aggregations['industries_ids'])->map(function ($industries_ids) {
            if (isset($industries_ids['industries_str']['buckets'][0]['key'])) {
                $industries_ids = array_merge(
                    $industries_ids,
                    ['text' => $industries_ids['industries_str']['buckets'][0]['key']]
                );
            }

            unset($industries_ids['industries_str']);

            return $industries_ids;
        })->toArray();

        $aggregations['industries_ids'] = $industries_ids;

        $region_ids = collect($aggregations['region_ids'])->map(function ($region) {
            $province = collect($region['province']['hits']['hits'][0]['_source']['addresses']['collection_addresses'])->pluck('province')->where('id', $region['key'])->first();

            if (!empty($province)) {
                $region = array_merge(
                    $region,
                    ['text' => $province['value']]
                );
            }

            unset($region['province']);

            return $region;
        })->toArray();

        $aggregations['region_ids'] = $region_ids;

        return $aggregations;
    }

    private function dataRelation($rq)
    {
        $data = [];
        $fields = isset($rq['fields']['company']) ? explode(',', (string) $rq['fields']['company']) : [];
        $latestJobsxx = preg_grep('/^(latest_jobs_)[0-9]+$/i', $fields);
        if (empty($latestJobsxx) && in_array('latest_jobs', $fields)) {
            $latestJobsxx = [];
            $latestJobsxx[] = 'latest_jobs_10000';
        }

        if (count($latestJobsxx) > 0) {
            $size = (explode('_', (string) reset($latestJobsxx)))[2] ?? 10;
            $companyIds = $this->collection->pluck('id')->all();
            $data['jobs'] = [];
            $data['jobs']['size'] = $size;
            $data['jobs']['data'] = $this->getDataRelationModal(
                'jobs',
                [
                    'company' => implode(',', $companyIds),
                    'ordering' => 'newest_job',
                    'page_size' => 10000,
                    'status' => 3,
                ]
            );

            $data['jobs']['data'] = JobCollection::fromElasticsearch($data['jobs']['data'])->shouldGetRelation(false)->groupBy('owned_id');
        }

        return $data;
    }
}
