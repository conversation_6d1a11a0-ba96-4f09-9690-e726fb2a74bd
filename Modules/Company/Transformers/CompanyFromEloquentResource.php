<?php

namespace Modules\Company\Transformers;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource as Resource;
use Modules\VietnamArea\Transformers\AddressResource;

class CompanyFromEloquentResource extends Resource
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        $locale = $request->locale ?: 'en_US';
        $fields = isset($request->fields['company'])
                    ? explode(',', (string) $request->fields['company']) : [];

        return [

            /*
            |--------------------------------------------------------------------------
            | Info basic
            |--------------------------------------------------------------------------
            */
            'id' => $this->when(in_array('id', $fields), (int) $this->id),
            'display_name' => $this->when(in_array('display_name', $fields), (string) $this->display_name),
            'description' => $this->when(in_array('description', $fields), (string) $this->description),
            'description_str' => $this->when(in_array('description_str', $fields), (string) $this->description_str),
            'website' => $this->when(in_array('website', $fields), (string) $this->website),
            'tagline' => $this->when(in_array('tagline', $fields), (string) $this->tagline),
            'social_network' => $this->when(in_array('social_network', $fields), (array) $this->social_network),
            'benefits' => $this->when(in_array('benefits', $fields), (array) $this->benefits),

            $this->mergeWhen($this->relationLoaded('childrens'), [
                'members' => $this->when(in_array('members', $fields), (array) $this->members),
            ]),

            /*
            |--------------------------------------------------------------------------
            | Taxonomy
            |--------------------------------------------------------------------------
            */
            $this->mergeWhen($this->relationLoaded('taxonomies'), [
                'nationalities' => $this->when(in_array('nationalities', $fields), (array) $this->nationalities_id->all()),

                'extra_skills' => $this->when(in_array('extra_skills', $fields), (array) $this->extra_skills_name->all()),

                'best_skills' => $this->when(in_array('extra_skills', $fields), (array) $this->skills_id->all()),
                'skills_ids' => $this->when(in_array('skills_ids', $fields), (array) $this->skills_id->all()),
                'skills_arr' => $this->when(in_array('skills_arr', $fields), (array) $this->skills_id->all()),
                'skills_str' => $this->when(in_array('skills_str', $fields), (string) $this->skills_name->implode(', ')),

                'categories_ids' => $this->when(in_array('categories_ids', $fields), (array) $this->categories_id->all()),
                'categories_arr' => $this->when(in_array('categories_arr', $fields), (array) $this->categories_id->all()),
                'categories_str' => $this->when(in_array('categories_str', $fields), (string) $this->categories_name->implode(', ')),

                'industries_ids' => $this->when(in_array('industries_ids', $fields), (array) $this->industries_id->all()),
                'industries_arr' => $this->when(in_array('industries_arr', $fields), (array) $this->industries_id->all()),
                'industries_str' => $this->when(in_array('industries_str', $fields), (string) $this->industries_name->implode(', ')),
            ]),

            'products' => $this->when(in_array('products', $fields), ProductResource::collection($this->whenLoaded('products'))),
            'introduces' => $this->when(in_array('introduces', $fields), IntroduceResource::collection($this->whenLoaded('introduces'))),
            'employees' => $this->when(in_array('employees', $fields), EmployeeResource::collection($this->whenLoaded('employees'))),
            'addresses' => $this->when(in_array('addresses', $fields), AddressResource::collection($this->whenLoaded('addresses'))),

            /*
            |--------------------------------------------------------------------------
            | Urls
            |--------------------------------------------------------------------------
            */
            'follow_url' => $this->when(in_array('follow_url', $fields), (string) $this->follow_url),
            'detail_url' => $this->when(in_array('detail_url', $fields), (string) $this->detail_url),

            /*
            |--------------------------------------------------------------------------
            | Aggregations
            |--------------------------------------------------------------------------
            */
            'num_job_openings' => $this->when(in_array('num_job_openings', $fields), (int) $this->num_job_openings),
            'num_jobs' => $this->when(in_array('num_jobs', $fields), (int) $this->num_jobs),
            'num_viewers' => $this->when(in_array('num_viewers', $fields), (int) $this->num_viewers),
            'num_followeres' => $this->when(in_array('num_followeres', $fields), (int) $this->num_followeres),
            'num_employees' => $this->when(in_array('num_employees', $fields), (string) $this->getTaxonomies('num_employees')->pluck('id')->first()),

            /*
            |--------------------------------------------------------------------------
            | Media
            |--------------------------------------------------------------------------
            */
            $this->mergeWhen($this->relationLoaded('media'), [
                'image_logo' => $this->when(in_array('image_logo', $fields), $this->image_logo->toArray()),
                'image_cover' => $this->when(in_array('image_cover', $fields), $this->image_cover->toArray()),
                'image_galleries' => $this->when(in_array('image_galleries', $fields), $this->image_galleries->toArray()),
            ]),

            /*
            |--------------------------------------------------------------------------
            | Timestamps
            |--------------------------------------------------------------------------
            */
            'modified' => $this->when(in_array('modified', $fields), (array) (empty($this->updated_at) ? [] : [
                'date' => Carbon::createFromFormat('Y-m-d H:i:s', $this->updated_at)->format('d-m-Y'),
                'since' => Carbon::createFromFormat('Y-m-d H:i:s', $this->updated_at)->locale($locale)->diffForHumans(),
            ])),
        ];
    }
}
