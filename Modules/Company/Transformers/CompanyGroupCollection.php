<?php

namespace Modules\Company\Transformers;

use Illuminate\Http\Resources\Json\ResourceCollection;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class CompanyGroupCollection extends ResourceCollection
{
    use ElasticCollectionTrait;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return CompanyGroupResource::collection($this->collection);
    }

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function withResponse($request, $response)
    {
        $elasticCollection = static::getElasticCollection();
        $json = array_merge([
            '_type' => 'companies_groups',
            'aggregations' => empty($elasticCollection) ? [] : $this->transformerAggregations($elasticCollection->aggregations()),
        ], json_decode($response->getContent(), true));

        $response->setContent(json_encode($json));
    }

    public function transformerAggregations($aggregations)
    {
        $industries_ids = collect($aggregations['industries_ids'])->map(function ($industries_ids) {
            if (isset($industries_ids['industries_str']['buckets'][0]['key'])) {
                $industries_ids = array_merge(
                    $industries_ids,
                    ['text' => $industries_ids['industries_str']['buckets'][0]['key']]
                );
            }

            unset($industries_ids['industries_str']);

            return $industries_ids;
        })->toArray();

        $aggregations['industries_ids'] = $industries_ids;

        $region_ids = collect($aggregations['region_ids'])->map(function ($region) {
            if (isset($region['region_str']['buckets'][0]['key'])) {
                $region = array_merge(
                    $region,
                    ['text' => $region['region_str']['buckets'][0]['key']]
                );
            }

            unset($region['region_str']);

            return $region;
        })->toArray();

        $aggregations['region_ids'] = $region_ids;

        return $aggregations;
    }
}
