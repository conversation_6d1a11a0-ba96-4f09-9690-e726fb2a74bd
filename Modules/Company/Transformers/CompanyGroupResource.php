<?php

namespace Modules\Company\Transformers;

use Illuminate\Http\Resources\Json\JsonResource as Resource;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class CompanyGroupResource extends Resource
{
    use ElasticCollectionTrait;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => (int) $this->id,
            'display_name' => (string) $this->display_name,
            'image_cover' => (string) $this->image_cover,
            'members_count' => ($this->members && is_array($this->members)) ? count($this->members) : 0,
            'group_url' => ($this->id == 83771) ? 'https://topdev.vn/companies/group/korean-it-companies-83771' : null,
        ];
    }
}
