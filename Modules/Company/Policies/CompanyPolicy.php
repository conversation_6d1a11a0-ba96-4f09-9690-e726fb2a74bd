<?php

namespace Modules\Company\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Company\Entities\Company;
use Modules\User\Entities\User;

class CompanyPolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if replies can be created by the user.
     */
    public function create(User $user): bool
    {
        return true;
    }

    /**
     * Determine if the given reply can be updated by the user.
     */
    public function update(User $user, Company $reply): bool
    {
        return true;
    }

    /**
     * Determine if the given reply can be deleted by the user.
     */
    public function delete(User $user, Company $reply): bool
    {
        return true;
    }
}
