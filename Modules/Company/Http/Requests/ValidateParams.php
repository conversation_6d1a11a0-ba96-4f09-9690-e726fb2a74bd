<?php

namespace Modules\Company\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ValidateParams extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            // 'region_ids' => 'nullable|regex:'.config('validate.params.company.region_ids'),
            'ids' => 'nullable|regex:' . config('validate.params.ids'),
            'skills_id' => 'nullable|regex:' . config('validate.params.ids'),
            'page' => 'nullable|integer|min:1',
            'job_levels_ids' => 'nullable|regex:' . config('validate.params.ids'),
            'job_types_ids' => 'nullable|regex:' . config('validate.params.ids'),
            'categories_ids' => 'nullable|regex:' . config('validate.params.ids'),
        ];
    }

    public function messages()
    {
        return [
            // 'region_ids.regex' => "The format of region_ids isn't correct?",
            'ids.regex' => "The format of ids isn't correct?",
            'page.integer' => "The format of page isn't correct?",
            'page.min' => 'Page require min is 1',
            'job_levels_ids.regex' => "The format of job_levels_ids isn't correct?",
            'job_types_ids.regex' => "The format of job_types_ids isn't correct?",
        ];
    }
}
