<?php

namespace Modules\Company\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Helpers\GetDataUser;
use App\Helpers\QueueHeplers;
use App\Helpers\ValidateParamsHelper;
use App\Http\Controllers\Controller;
use Modules\Company\Entities\Company;
use Modules\Company\Http\Requests\StoreCompanyRequest;
use Modules\Company\Http\Requests\UpdateCompanyRequest;
use Modules\Company\Http\Requests\ValidateParams;
use Modules\Company\Jobs\CreateCompanyProcess;
use Modules\Company\Jobs\UpdateCompanyProcess;
use Modules\Company\Repositories\Contracts\CompanyRepositoryInterface;
use Modules\Company\Transformers\CompanyCollection;
use Modules\Company\Transformers\CompanyGroupCollection;
use Modules\Company\Transformers\CompanyResource;
use Modules\Job\Repositories\Contracts\JobRepositoryInterface;
use Modules\Job\Transformers\JobCollection;
use Redmix0901\Core\Http\Responses\BaseHttpResponse;

class CompanyController extends Controller
{
    /**
     * @var CompanyRepositoryInterface
     */
    protected $companyRepository;

    public function __construct(CompanyRepositoryInterface $company)
    {
        $this->companyRepository = $company;
    }

    /**
     * Search all company.
     */
    public function all(ValidateParams $request)
    {
        if (!empty($request->job_features) && empty($request->ids) && empty($request->company)) {
            $jobs = app(JobRepositoryInterface::class)->searchOnElasticsearch(
                null,
                [
                    'page' => 1,
                    'page_size' => 10000,
                    'status' => 3,
                    'features' => $request->job_features,
                ]
            );

            $request['ids'] = collect($jobs['hits']['hits'])->pluck('_source.company.id')->unique()->implode(',');
            if (empty($request['ids'])) {
                $request['ids'] = -999999;
            }
        }

        $size = $request->page_size ?? 10;
        if (isset($request->region_ids) && !empty($request->region_ids)) {
            $request['region_ids'] = app(ValidateParamsHelper::class)->validateRegionIds($request->region_ids);
        }

        $companies = $this->companyRepository->searchOnElasticsearch(
            strtolower(str_replace(['++', '#'], '', (string) $request->keyword)),
            array_filter(
                array_merge($request->all(), [
                    'status' => Company::STATUS_ACTIVE,
                ])
            )
        );

        $user = auth('api')->user();
        $listDataUser = app(GetDataUser::class)->getDataUser($user, 'applied_jobs,followed_jobs,blacklist_companies,followed_companies');

        app(QueueHeplers::class)->trackingUserV2(['companies::'=>$request->ids], 'search', $user, $request);

        return CompanyCollection::fromElasticsearch($companies, $size)
                                ->listDataUser($listDataUser)
                                ->response()
                                ->setStatusCode(200);
    }

    /**
     * Get company members.
     *
     * @param  Company  $company
     * @param  ValidateParams  $request
     *
     * @return JsonResponse
     */
    public function members(Company $company, ValidateParams $request)
    {
        $size = $request->page_size ?? 10;
        $members = $this->companyRepository->searchOnElasticsearch(
            $request->keyword,
            array_merge(
                $request->all(),
                [
                    'ids' => implode(',', $company->childrens->pluck('id')->all()),
                ]
            )
        );

        $listDataUser = app(GetDataUser::class)->getDataUser(auth('api')->user(), 'applied_jobs,followed_jobs,blacklist_companies,followed_companies');

        return CompanyCollection::fromElasticsearch($members, $size)
                                ->listDataUser($listDataUser)
                                ->response()
                                ->setStatusCode(200);
    }

    /**
     * Show detail company.
     *
     * @param  int  $id
     *
     * @return CompanyResource
     */
    public function show(int $id)
    {
        $company = $this->companyRepository->searchOnElasticsearch(null, [
            'ids' => $id,
        ]);

        $listDataUser = app(GetDataUser::class)->getDataUser(auth('api')->user(), 'applied_jobs,followed_jobs,blacklist_companies,followed_companies');

        return CompanyResource::listDataUser($listDataUser)->fromElasticsearch($company);
    }

    /**
     * Create company.
     */
    public function store(StoreCompanyRequest $request, BaseHttpResponse $response)
    {
        $data = json_decode($request->getContent(), true);

        $this->dispatch(new CreateCompanyProcess(
            $request->except(['status']),
            $request->user()
        ));

        return $response->setCode(201)
                    ->setMessage('Creating');
    }

    /**
     * Update company.
     */
    public function update(Company $company, UpdateCompanyRequest $request, BaseHttpResponse $response)
    {
        $data = json_decode($request->getContent(), true);

        $this->dispatch(new UpdateCompanyProcess(
            $company,
            $request->except(['status']),
            $request->user(),
            $request->method()
        ));

        return $response->setMessage('Updating');
    }

    public function groups(ValidateParams $request)
    {
        $size = $request->page_size ?? 10;

        $companies = $this->companyRepository->searchOnElasticsearch(
            $request->keyword,
            array_merge($request->all(), [
                'is_group' => true,
            ])
        );

        return CompanyGroupCollection::fromElasticsearch($companies, $size)
                                ->response()
                                ->setStatusCode(200);
    }

    public function getJobOfCompany($id, ValidateParams $request)
    {
        $size = $request->page_size ?? 10;
        $page = $request->page ?? 1;

        $company = $this->companyRepository->searchOnElasticsearch(
            null,
            [
                'ids' => $id,
                'page_size' => 1,
            ]
        );

        $companyMembers = (isset($company['hits']['hits'][0]['_source']['members'])) ? implode(',', $company['hits']['hits'][0]['_source']['members']) : '';

        if ($request->ordering == 'filter_jobs') {
            $id = null;
        }

        $jobs = app(JobRepositoryInterface::class)->searchOnElasticsearch(
            null,
            array_merge($request->all(), [
                'page' => 1,
                'page_size' => 10000,
                'company' =>  implode(',', array_filter([
                    $id,
                    $companyMembers,
                ])),
                'ordering' => $request->ordering ?? 'newest_job',
                'status' => 3,
            ])
        );

        $jobs['hits']['hits'] = collect($jobs['hits']['hits'])->forPage($page, $size)->all();

        $listDataUser = app(GetDataUser::class)->getDataUser(auth('api')->user(), 'applied_jobs,followed_jobs,blacklist_companies,followed_companies');

        return JobCollection::fromElasticsearch($jobs, $size)
                            ->listDataUser($listDataUser)
                            ->response()
                            ->setStatusCode(200);
    }

    function search(Request $request, BaseHttpResponse $response)
    {
        $companies = app(CompanyRepositoryInterface::class)->searchOnElasticsearch(
            null,
            [
                'display_name' => $request->keyword,
                'status' => Company::STATUS_ACTIVE,
                'page_size' => $request->page_size ?? 10
            ],
            [
                'id',
                'display_name'
            ]
        );

        $data = collect($companies['hits']['hits'])->pluck('_source');

        return $response->setCode(200)->setData($data);
    }
}
