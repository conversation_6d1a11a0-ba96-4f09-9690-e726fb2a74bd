<?php

namespace Modules\Company\Http\Controllers;

use App\Helpers\ValidateParamsHelper;
use App\Http\Controllers\Controller;
use Modules\Company\Repositories\Contracts\CompanyRepositoryInterface;
use Modules\Company\Transformers\CompanyCollection;
use Modules\Job\Entities\Job;
use Modules\Job\Http\Requests\JobSearchRequest;

class CompanySearchController extends Controller
{
    protected CompanyRepositoryInterface $companyRepository;

    public function __construct(CompanyRepositoryInterface $company) {
        $this->companyRepository = $company;
    }

    public function index(JobSearchRequest $request) {
        if (isset($request->region_ids) && !empty($request->region_ids)) {
            $request['region_ids'] = app(ValidateParamsHelper::class)->validateRegionIds($request->region_ids);
        }

        $pageSize = $request->page_size ?? 15;
        $companies = $this->companyRepository->searchOnElasticsearch(
            strtolower(str_replace(['++', '#'], '', $request->keyword)),
            array_merge($request->all(), [
                'page_size' => $pageSize,
                'status' => Job::STATUS_OPEN,
            ])
        );

        return CompanyCollection::fromElasticsearch($companies, $pageSize)
            ->response();
    }
}
