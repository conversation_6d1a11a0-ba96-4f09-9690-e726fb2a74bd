<?php

namespace Modules\Company\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Company\Repositories\Contracts\CompanyRepositoryInterface;

class CreateCompanyProcess implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var CompanyRepositoryInterface
     */
    private $companyRepository;

    /**
     * Create a new job instance.
     *
     * @return void
     * @param mixed[] $params
     */
    public function __construct(private $params, private $author = null)
    {
        $this->companyRepository = app(CompanyRepositoryInterface::class);
        $this->onConnection(config('queue.ams'));
    }

    /**
     * Thực thi công việc, Tạo company nếu:
     * là user admin thì sẽ trực tiếp tạo cty,
     * là user thường thì sẽ tạo cty ở bản draft.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->params['display_name'] = $this->params['business_name'];

        $company = $this->companyRepository->create(
            $this->params
        );

        $this->setOwnerCompany($company);

        return $company;
    }

    /**
     * @param $company
     *
     * @return void
     */
    private function setOwnerCompany($company): void
    {
        if (!empty($this->author)) {
            $this->author->company()->associate($company);
            $this->author->save();
        }
    }
}
