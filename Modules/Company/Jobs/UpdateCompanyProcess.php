<?php

namespace Modules\Company\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Company\Entities\Company;

class UpdateCompanyProcess implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public const HTTP_PUT = 'put';
    public const HTTP_PATCH = 'patch';

    /**
     * Create a new job instance.
     *
     * @return void
     * @param mixed[] $params
     */
    public function __construct(private $company, private $params, private $author, private $method = 'put')
    {
        $this->onConnection(config('queue.ams'));
    }

    /**
     * Thực thi công việc, Update company nếu:
     * là user admin thì sẽ trực tiếp update cty,
     * là user thường thì sẽ update cty ở bản draft.
     *
     * @return void
     */
    public function handle()
    {
        if ($this->method == static::HTTP_PATCH) {
            return $this->company->fill($this->params)->save();
        }

        return $this->company->update($this->params);
    }
}
