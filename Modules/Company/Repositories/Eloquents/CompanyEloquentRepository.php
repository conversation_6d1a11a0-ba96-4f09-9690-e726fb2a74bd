<?php

namespace Modules\Company\Repositories\Eloquents;

use Modules\Company\Entities\Company;
use Modules\Company\Repositories\Contracts\CompanyRepositoryInterface;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\RangeAggregation;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\SignificantTermsAggregation;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\TermsAggregation;
use ONGR\ElasticsearchDSL\Aggregation\Metric\TopHitsAggregation;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\Compound\FunctionScoreQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MatchQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MultiMatchQuery;
use ONGR\ElasticsearchDSL\Query\MatchAllQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\IdsQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\RangeQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermsQuery;
use ONGR\ElasticsearchDSL\Sort\FieldSort;
use Redmix0901\Core\Repositories\Eloquent\BaseRepository;

class CompanyEloquentRepository extends BaseRepository implements CompanyRepositoryInterface
{
    private $regionOther = [
        '01', '79', '48',
    ];

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = null, $params = [], $fields = '*')
    {
        return $this->querySearch($keyword, $params, $fields)->raw();
    }

    public function searchOnEloquent($keyword = null, $params = [], $fields = '*')
    {
        if (isset($params['page_size'])) {
            return $this->querySearch($keyword, $params, $fields)->paginate($params['page_size']);
        }

        return $this->querySearch($keyword, $params, $fields)->get();
    }

    /**
     * @inheritdoc
     */
    private function querySearch($keyword, $params, $fields)
    {
        $searchCore = Company::SEARCH_SCORE;

        return $this->model->search('*', function ($client, $body) use ($keyword, $params, $fields, $searchCore) {
            // Pagination
            $size = $params['page_size'] ?? 10;
            $from = isset($params['page']) ? $size * ($params['page'] - 1) : 0;

            $baseQuery = new BoolQuery();

            if ($fields !== '*') {
                $arrFields = is_array($fields) ? $fields : explode(',', (string) $fields);

                $body->setSource(array_filter(array_map('trim', $arrFields)));
            }

            if ($fields !== '*') {
                $arrFields = is_array($fields) ? $fields : explode(',', (string) $fields);

                $body->setSource(array_filter(array_map('trim', $arrFields)));
            }

            /*
             * Tìm kiếm keyword các trường title và tên của employer
             */
            if (!empty($keyword)) {
                // $baseQuery->add(new MultiMatchQuery(['skills_str', 'display_name', 'description', '_id'], $keyword, ['operator' => 'or']), BoolQuery::MUST);
                $baseQuery->add(new MultiMatchQuery(['skills_str', 'display_name'], $keyword, ['operator' => 'or']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'display_name')) {
                $baseQuery->add(new MatchQuery('display_name', $params['display_name']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'skills_str')) {
                $baseQuery->add(new MultiMatchQuery(['skills_str'], $params['skills_str'], ['operator' => 'or']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'skills_id')) {
                $baseQuery->add(new TermsQuery('skills_ids', array_filter(explode(',', (string) $params['skills_id']))), BoolQuery::MUST);
            }

            /*
             *  Lọc công ty có thành viên
             */
            if ($this->hasQuery($params, 'employees')) {
                $baseQuery->add(new TermsQuery('employees', $params['employees']), BoolQuery::MUST);
            }

            /*
             * Tìm kiếm keyword trên cột full_address
             */
            if ($this->hasQuery($params, 'full_address')) {
                $baseQuery->add(new MultiMatchQuery(['resume.addresses.full_address'], $params['full_address'], ['operator' => 'or']), BoolQuery::MUST);
            }

            /*
             * Tìm kiếm trên cột address_region_ids
             */
            if ($this->hasQuery($params, 'region_ids')) {
                if ($params['region_ids'] == 'other') {
                    $baseQuery->add(new TermsQuery('addresses.address_region_ids', $this->regionOther), BoolQuery::MUST_NOT);
                } elseif ($params['region_ids'] != 'all') {
                    $baseQuery->add(new TermsQuery('addresses.address_region_ids', explode(',', (string) $params['region_ids'])), BoolQuery::MUST);
                }
            }

            /*
             * Tìm kiếm trên cột industries_ids
             */
            if ($this->hasQuery($params, 'industries_ids')) {
                $baseQuery->add(new TermsQuery('industries_ids', explode(',', (string) $params['industries_ids'])), BoolQuery::MUST);
            }

            // Search with spotlight condition
            if ($this->hasQuery($params, 'spotlight')) {
                $baseQuery->add(new TermQuery('is_spotlight', $params['spotlight']), BoolQuery::MUST);
            }
            // Search with highlight condition
            if ($this->hasQuery($params, 'highlight')) {
                $baseQuery->add(new TermQuery('is_highlight', $params['highlight']), BoolQuery::MUST);
            }
            // Search with popular condition
            if ($this->hasQuery($params, 'popular')) {
                $baseQuery->add(new TermQuery('is_distinction', true), BoolQuery::MUST);
                // $rangeQuery = new RangeQuery(
                //     'num_job_openings',
                //     [
                //         'gte' => 1,
                //         'lte' => 1000,
                //     ]
                // );
                // $body->addQuery($rangeQuery);
                // $body->addSort(new FieldSort('scoring_by_distinction', null, ['order' => FieldSort::DESC]));
            }
            //features
            if ($this->hasQuery($params, 'features')) {
                $baseQuery->add(new TermsQuery('features', explode(',', (string) $params['features'])), BoolQuery::MUST);
            }

            //
            if ($this->hasQuery($params, 'packages')) {
                $baseQuery->add(new TermsQuery('packages', explode(',', (string) $params['packages'])), BoolQuery::MUST);
            }
            //

            /*
             * Lọc các công cty có id sau
             */
            if (array_key_exists('company', $params)) {
                $baseQuery->add(new IdsQuery(empty($params['company']) ? [] : explode(',', (string) $params['company'])), BoolQuery::MUST);
            } elseif (array_key_exists('ids', $params)) {
                $baseQuery->add(new IdsQuery(empty($params['ids']) ? [] : explode(',', (string) $params['ids'])), BoolQuery::MUST);
            }

            //search is group
            if (array_key_exists('is_group', $params)) {
                $baseQuery->add(new MatchQuery('is_group', 'true'), BoolQuery::MUST);
            }

            if (empty($keyword) && empty($params)) {
                $baseQuery = new MatchAllQuery();
            }

            // sort
            //$body->addSort(new FieldSort('scoring_by_packages', null, ['order' => FieldSort::DESC]));

            //Sort
            if ($this->hasOrderBy($params, 'newest_company')) {
                $body->addSort(new FieldSort('scoring_by_distinction', null, ['order' => FieldSort::DESC]));
                // $body->addSort(new FieldSort('scoring_by_basicplus', null, ['order' => FieldSort::DESC]));
                $body->addSort(new FieldSort('updated_at', null, ['order' => FieldSort::DESC]));
            } elseif ($this->hasOrderBy($params, 'prioritized')) {
                $body->addSort(new FieldSort('num_job_openings', null, ['order' => FieldSort::DESC]));
                $body->addSort(new FieldSort('updated_at', null, ['order' => FieldSort::DESC]));
                $body->addSort(new FieldSort('scoring_by_distinction', null, ['order' => FieldSort::DESC]));
                $body->addSort(new FieldSort('scoring_by_basicplus', null, ['order' => FieldSort::DESC]));
            } elseif ($this->hasOrderBy($params, 'fresherpage')) {
                $body->addSort(new FieldSort('latest_posted_at', null, ['order' => FieldSort::DESC]));
                $body->addSort(new FieldSort('updated_at', null, ['order' => FieldSort::DESC]));
            }

            //----filter hiring
            $num_jobs = 0;
            if ($this->hasQuery($params, 'hiring')) {
                $num_jobs = $params['hiring'];
                $rangeQuery = new RangeQuery(
                    'num_job_openings',
                    [
                        'gte' => $num_jobs,
                    ]
                );
                $body->addQuery($rangeQuery);
            }
            //----

            if ($this->hasQuery($params, 'status')) {
                $baseQuery->add(new MatchQuery('status', $params['status']), BoolQuery::MUST);
            }

            //order by score
            if ($this->hasQuery($params, 'prioritized_company')) {
                $priorityCompany = ($this->credentialsScore($params['prioritized_company']));
                if (!empty($priorityCompany)) {
                    foreach ($priorityCompany as $e) {
                        // dd($e,$searchCore[$e]);
                        $baseQueryScore = new FunctionScoreQuery($baseQuery);
                        $socreQuery = new IdsQuery([$e]);
                        $baseQueryScore->addWeightFunction($searchCore[$e], $socreQuery);
                        $body->addQuery($baseQueryScore);
                    }
                }
            }

            if ($this->hasQuery($params, 'random')) {
                $baseQueryScore = new FunctionScoreQuery($baseQuery);
                $baseQueryScore->addRandomFunction();
                $body->addQuery($baseQueryScore);
            }

            $body->setSize($size);
            $body->setFrom($from);
            $body->addQuery($baseQuery);
            $body->addAggregation(new TermsAggregation('status', 'status'));

            //----location and name location
            //add location
            $locationAggregation = new TermsAggregation('region_ids', 'addresses.address_region_ids.keyword');

            //add nam location
            $topHitsProvinceAggregationLast = new TopHitsAggregation('province', 1, null);
            $topHitsProvinceAggregationLast->addParameter('_source', [
                'include' => ['addresses.collection_addresses.province'],
            ]);

            $locationAggregation->addAggregation($topHitsProvinceAggregationLast);
            $body->addAggregation($locationAggregation);

            //----industries and name industries
            //add name industries
            $industriesStrAggregation = new SignificantTermsAggregation('industries_str', 'industries_arr.keyword');
            $industriesStrAggregation->addParameter('size', 1);
            $industriesStrAggregation->addParameter('min_doc_count', 1);
            //add industries
            $industriesAggregation = new TermsAggregation('industries_ids', 'industries_ids');
            $industriesAggregation->addParameter('size', 100);
            $industriesAggregation->addAggregation($industriesStrAggregation);
            $body->addAggregation($industriesAggregation);

            // add count_feature
            if ($this->hasQuery($params, 'count_feature') && $params['count_feature'] == true) {
                $body->addAggregation(new TermsAggregation('count_feature', 'features'));
            }

            //---- hiring
            $termFilter = new RangeAggregation(
                'hiring',
                'num_job_openings',
                [
                    ['from' => $num_jobs],
                ]
            );
            $body->addAggregation($termFilter);

            return $client->search(['index' => $this->model->searchableAs(), 'body' => $body->toArray()])->asArray();
        })
            ->query(function ($builder) {
                $builder->with('meta', 'media', 'addresses', 'products.media', 'introduces.media', 'taxonomies.term', 'employees', 'childrens')
                    ->withCount('onlyJobOpen', 'views', 'followers', 'jobs');
            });
    }

    /**
     * Check if has query.
     */
    private function hasQuery($query, $key)
    {
        return (bool) (isset($query[$key]) && !is_null($query[$key]));
    }

    private function hasOrderBy($params, $key)
    {
        return isset($params['ordering'])
            && in_array($key, explode(',', (string) $params['ordering']));
    }

    /**
     * Save model with draft.
     */
    public function saveAsDraft(array $condition, array $data)
    {
        $item = null;

        if (!empty($condition)) {
            $item = $this->getFirstBy($condition);
        }

        $parent = is_null($item) ? null : $item->getKey();
        $draft = $this->create($data);
        $draft->markAsPending($parent);

        return $draft;
    }

    private function credentialsScore($collection)
    {
        return collect(explode(',', (string) $collection))->filter(fn($value, $key) => array_key_exists($value, Company::SEARCH_SCORE))->all();
    }
}
