<?php

namespace Modules\Company\Repositories\Caches;

use Modules\Company\Repositories\Contracts\CompanyRepositoryInterface;
use Redmix0901\Core\Repositories\Caches\BaseCacheDecorator;
use Redmix0901\Core\Repositories\Interfaces\BaseRepositoryInterface;

class CompanyCacheDecorator extends BaseCacheDecorator implements CompanyRepositoryInterface
{
    public function __construct(BaseRepositoryInterface $base)
    {
        //$this->time = 100;

        // Invoke parent
        parent::__construct($base);
    }

    /**
     * @inheritdoc
     */
    public function searchOnEloquent($keyword = null, $params = [], $fields = '*')
    {
        return $this->getWithCache(__FUNCTION__, func_get_args());
    }

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = '*', $params = [], $fields = '*')
    {
        return $this->getWithCache(__FUNCTION__, func_get_args());
    }
}
