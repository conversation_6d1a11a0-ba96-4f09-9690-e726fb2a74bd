<?php

namespace Modules\Company\Repositories\Contracts;

use Redmix0901\Core\Repositories\Interfaces\BaseRepositoryInterface;

interface CompanyRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * @inheritdoc
     */
    public function searchOnEloquent($keyword = null, $params = [], $fields = '*');

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = '*', $params = [], $fields = '*');
}
