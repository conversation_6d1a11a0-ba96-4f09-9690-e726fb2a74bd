<?php

use Illuminate\Support\Facades\Route;
use Modules\Company\Http\Controllers\CompanySearchController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

/*
 * Search all company
 */
/*
 * @OA\Get(
 *     path="/companies",
 *     summary="Search all company",
 *     tags={"companies"},
 *     operationId="all",
 *     @OA\Parameter(
 *         example="10",
 *         name="page_size",
 *         in="query",
 *         @OA\Schema(
 *           type="interger",
 *         ),
 *         style="form"
 *     ),
 *     @OA\Parameter(
 *         example="id,display_name",
 *         name="fields[company]",
 *         in="query",
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Response(
 *          response="200",
 *          description="Display a listing of company.",
 *     )
 * )
 */

/*
 * Get all members
 */
/*
 * @OA\Get(
 *     path="/companies/{company}/members",
 *     summary="Get all memberss",
 *     tags={"companies"},
 *     operationId="members",
 *     @OA\Parameter(
 *         example="49866",
 *         name="company",
 *         in="path",
 *         @OA\Schema(
 *           type="interger",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="fields[company]",
 *         example="id,display_name",
 *         in="query",
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Response(
 *          response="200",
 *          description="Display list members",
 *     ),
 *     security={{"bearer_token":{}}}
 * )
 */

/*
 * Show detail company.
 */
/*
 * @OA\Get(
 *     path="/companies/{id}",
 *     summary="Show detail company",
 *     tags={"companies"},
 *     operationId="show",
 *     @OA\Parameter(
 *         example="49866",
 *         name="id",
 *         in="path",
 *         @OA\Schema(
 *           type="interger",
 *         ),
 *     ),
 *     @OA\Parameter(
 *         name="fields[company]",
 *         example="id,display_name",
 *         in="query",
 *         @OA\Schema(
 *           type="string",
 *         ),
 *     ),
 *     @OA\Response(
 *          response="200",
 *          description="Display detail company.",
 *     ),
 *   security={
 *     {
 *       "oauth2": {"read:oauth2"},
 *     }
 *   }
 * )
 */

Route::prefix('/companies')->group(function () {
    Route::middleware('auth:api')->group(function () {
        Route::get('viewable/{uuid}/{collection}', 'CompanyController@viewable');
        //        Route::post('/', 'CompanyController@store')->middleware('can:create,Modules\Company\Entities\Company');
        //        Route::put('/{company}', 'CompanyController@update')->middleware('can:update,company');
        //        Route::patch('/{company}', 'CompanyController@update')->middleware('can:update,company');
    });

    Route::get('/', 'CompanyController@all')->middleware('ensure_frontend_requests_are_stateful');
    Route::get('/{id}', 'CompanyController@show')->where('id', '[0-9]+');
    Route::get('/groups', 'CompanyController@groups');
    Route::get('{company}/members', 'CompanyController@members');
    Route::get('/{id}/jobs', 'CompanyController@getJobOfCompany')->where('id', '[0-9]+');
    Route::get('/search', 'CompanyController@search');
    Route::get('/search/v2', [CompanySearchController::class, 'index'])
        ->middleware([
            'ensure_frontend_requests_are_stateful',
            'setlocaleapi'
        ])
        ->name('company_search_v2');
});
