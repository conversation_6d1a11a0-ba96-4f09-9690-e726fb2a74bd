<?php

namespace Modules\Company\Traits;

use Modules\Company\Entities\Product;

trait HasProduct
{
    /**
     * Boot the HasProduct trait for the model.
     *
     * @return void
     */
    public static function bootHasProduct()
    {
        static::deleted(function (self $model) {

            if (!method_exists($model, 'runSoftDelete') || $model->isForceDeleting()) {
                $model->products()->forceDelete();
            }
        });
    }

    /**
     * Get the products for the model.
     */
    public function products()
    {
        return $this->hasMany(Product::class, 'owned_id');
    }

    public function getProducts()
    {
        return $this->products->map(fn($product, $key) => [
            'name' => $product->name,
            'description' => $product->description,
            'image' => $product->image,
            'images' => $product->getMedia('images')->map->toArray()->all(),
        ])->all();
    }

    public function setProductsAttribute($products)
    {
        if (is_array($products)) {

            Product::where('owned_id', $this->getKey())->delete();

            $this->products()->createMany($products);
        }
    }
}
