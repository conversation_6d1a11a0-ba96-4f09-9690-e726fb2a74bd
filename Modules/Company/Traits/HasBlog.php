<?php

namespace Modules\Company\Traits;

use Modules\Company\Entities\Blog;

trait HasBlog
{
    /**
     * Boot the HasNews trait for the model.
     *
     * @return void
     */
    public static function bootHasBlog()
    {
        static::deleted(function (self $model) {

            if (!method_exists($model, 'runSoftDelete') || $model->isForceDeleting()) {
                $model->introduces()->forceDelete();
            }
        });
    }

    /**
     * Get the news for the model.
     */
    public function blogs()
    {
        return $this->hasMany(Blog::class, 'owned_id');
    }

    public function getBlogs()
    {
        return $this->blogs->map(fn($blog, $key) => [
            'name' => $blog->name,
            'description' => $blog->description,
            'link' => $blog->link_blog,
            'image' => $blog->image_blog,
            'tag' => $blog->tag_blog,
            'author' => $blog->author_blog,
            'publish' => $blog->date_blog,
        ])->all();
    }

    public function setBlogAttribute($blog)
    {
        if (is_array($blog)) {

            Blog::where('owned_id', $this->getKey())->delete();

            $this->blog()->createMany($blog);
        }
    }
}
