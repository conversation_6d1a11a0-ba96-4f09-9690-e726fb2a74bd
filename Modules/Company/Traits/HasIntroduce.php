<?php

namespace Modules\Company\Traits;

use Modules\Company\Entities\Introduce;

trait HasIntroduce
{
    /**
     * Boot the HasIntroduce trait for the model.
     *
     * @return void
     */
    public static function bootHasIntroduce()
    {
        static::deleted(function (self $model) {

            if (!method_exists($model, 'runSoftDelete') || $model->isForceDeleting()) {
                $model->introduces()->forceDelete();
            }
        });
    }

    /**
     * Get the introduces for the model.
     */
    public function introduces()
    {
        return $this->hasMany(Introduce::class, 'owned_id');
    }

    public function getIntroduces()
    {
        return $this->introduces->map(fn($introduce, $key) => [
            'name' => $introduce->name,
            'description' => $introduce->description,
            'image' => $introduce->image,
            'images' => $introduce->getMedia('images')->toArray(),
        ])->all();
    }

    public function setIntroducesAttribute($introduces)
    {
        if (is_array($introduces)) {

            Introduce::where('owned_id', $this->getKey())->delete();

            $this->introduces()->createMany($introduces);
        }
    }
}
