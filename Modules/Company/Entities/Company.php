<?php

namespace Modules\Company\Entities;

use App\Traits\HasSlug;
use App\Traits\UseUuid;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laravel\Scout\Searchable;
use Modules\Company\Traits\HasIntroduce;
use Modules\Company\Traits\HasProduct;
use Modules\File\Traits\HasFile;
use Modules\SearchCandidate\Entities\CompanyUnlockSearchCandidate;
use Modules\SearchCandidate\Entities\SearchCandidate;
use Modules\Subscription\Traits\HasSubscriptions;
use Modules\Taxonomy\Traits\Taxoable;
use Modules\VietnamArea\Traits\Addressable;
use Spatie\MediaLibrary\HasMedia;

class Company extends Model implements HasMedia
{
    use Taxoable;
    use UseUuid;
    use SoftDeletes;
    use Searchable;
    use HasFile;
    use HasProduct;
    use HasIntroduce;
    use Addressable;
    use HasSlug;

    /**
     * Has Subscriptions.
     */
    use HasSubscriptions;

    public const FEATURE_ENABLE = 'on';
    public const FEATURE_DISABLE = 'off';
    public const STATUS_ACTIVE = 1;
    public const STATUS_INACTIVE = 2;
    public const STATUS_REVIEW = 3;
    public const STATUS_WAITING = 4;

    public const SEARCH_SCORE = [
        83771 => 10,
    ];

    /**
     * @inheritdoc
     */
    protected $table = 'companies';

    /**
     * @inheritdoc
     */
    protected $fillable = ['display_name', 'addresses', 'industries', 'skills', 'tax_number'];

    /**
     * @inheritdoc
     */
    protected $attributes = [
        'slug' => null,
        'status' => self::STATUS_WAITING,
    ];

    /**
     * @inheritdoc
     */
    public function sluggable(): string
    {
        return $this->display_name;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function childrens()
    {
        return $this->hasMany($this, 'parent_id');
    }

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'companies_ams_v3';
    }

    /**
     * Get the value used to index the model.
     *
     * @return mixed
     */
    public function getScoutKey()
    {
        return $this->id;
    }

    /**
     * Get status for the model.
     *
     * @return array
     */
    public static function getStatusDescription()
    {
        return [
            static::STATUS_ACTIVE => 'Active',
            static::STATUS_INACTIVE => 'Inactive',
        ];
    }

    public function getImageLogoAttribute()
    {
        return $this->getMedia('image_logo');
    }

    public function getImageCoverAttribute()
    {
        return $this->getMedia('image_cover');
    }

    public function getImageGalleriesAttribute()
    {
        return $this->getMedia('image_galleries');
    }

    public function setSkillsAttribute($value)
    {
        return $this->addTaxonomies($value, 'skills');
    }

    public function setIndustriesAttribute($value)
    {
        return $this->addTaxonomies($value, 'industries');
    }

    /**
     * Determine if the model should be searchable.
     *
     * @return bool
     */
    public function shouldBeSearchable()
    {
        if ($this->isInactive() || $this->trashed()) {
            return false;
        }

        return true;
    }

    /**
     * @return bool
     */
    public function isInactive()
    {
        return $this->status == self::STATUS_INACTIVE;
    }

    /**
     * Get all members id.
     *
     * @return array
     */
    public function getMembersAttribute()
    {
        return (array) $this->childrens->pluck('id')->all();
    }

    public function unlockedCandidates()
    {
        return $this->belongsToMany(
            SearchCandidate::class,
            CompanyUnlockSearchCandidate::class,
            'company_id',
            'search_candidate_id',
        )->withPivot('created_at');
    }
    public function getUrlImageLogoAttribute()
    {
        return $this->getFirstMediaUrl('image_logo');
    }
}
