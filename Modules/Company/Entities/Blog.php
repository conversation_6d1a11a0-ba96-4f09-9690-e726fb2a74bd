<?php

namespace Modules\Company\Entities;

use Amscore\Admin\Form\Field;
use App\Traits\UseUuid;
use Modules\Post\Entities\Post;
use Spatie\MediaLibrary\File;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class Blog extends Post
{
    use UseUuid;

    /**
     * @inheritdoc
     */
    protected $type = 'blog';

    /**
     * @inheritdoc
     */
    protected $attributes = [
        'slug' => null,
        'status' => 1,
    ];

    /**
     * @inheritdoc
     */
    protected $appends = [
        'name', 'description', 'images', 'image', 'link_blog', 'image_blog', 'tag_blog', 'author_blog',
        'date_blog',
    ];

    /**
     * @inheritdoc
     */
    protected $fillable = [
        'image', 'images', 'name', 'description', 'link_blog', 'image_blog', 'tag_blog', 'author_blog',
        'date_blog',
    ];

    /**
     * @inheritdoc
     */
    protected $cachePrefix = 'blog-prefix';

    /**
     * Boot model model.
     */
    protected static function boot()
    {
        static::creating(function ($user) {
            if (empty($user->name)) {
                $user->name = $user->display_name;
            }
        });

        parent::boot();
    }

    /**
     * @inheritdoc
     */
    public function sluggable(): string
    {
        if (!empty($this->name)) {
            return $this->name;
        }

        return $this->display_name;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function company()
    {
        return $this->belongsTo(Company::class, 'owned_id')
            ->withoutGlobalScopes()
            ->withDefault();
    }

    /**
     * @inheritdoc
     */
    public function ensureBeforeExpires()
    {
    }

    /**
     * @inheritdoc
     */
    public function ensureBeforeUnpublish()
    {
    }

    public function parseInfoBlog($url)
    {
        $slugURL = str_replace('blog/', '', trim(parse_url((string) $url, PHP_URL_PATH), '/'));

        $infoBlog = file_get_contents('https://topdev.vn/blog/wp-json/wp/v2/posts?slug=' . $slugURL . '&_embed=author,wp:term,wp:featuredmedia');

        return json_decode($infoBlog);
    }

    public function getTagBlog($value)
    {
        $tags = [];

        foreach ($value as $item => $tag) {
            if (!isset($tag[$item])) {
                break;
            }

            $tags[] = $tag[0]->name;
        }

        return $tags;
    }

    /**
     * Get title of blog.
     *
     * @return mixed
     */
    public function getInfoBlog()
    {
        if (!empty($this->link_blog)) {
            $info = $this->parseInfoBlog($this->link_blog);

            return [
                'name' => $info[0]->title->rendered ?? null,
                'description' => $info[0]->excerpt->rendered ?? null,
                'image_blog' => $info[0]->_embedded->{'wp:featuredmedia'}[0]->source_url ?? null,
                'tag_blog' => $this->getTagBlog($info[0]->_embedded->{'wp:term'}) ?? null,
                'author_blog' => $info[0]->_embedded->author[0]->name ?? null,
                'date_blog' => $info[0]->date ?? null,
            ];
        }
    }

    /**
     * Register media for model.
     *
     * @var void
     */
    public function registerMediaCollections()
    {
        $this->addMediaCollection('images')
            ->acceptsFile(fn(File $file) => $file->mimeType === 'image/jpeg'
            || $file->mimeType === 'image/png')->singleFile();
    }

    /**
     * Get link blog in meta.
     *
     * @return mixed
     */
    public function getLinkBlogAttribute()
    {
        return $this->getMeta('link_blog');
    }

    /**
     * Set link blog in meta.
     *
     * @return mixed
     */
    public function setLinkBlogAttribute($value)
    {
        $this->setMeta('link_blog', $value);
    }

    /**
     * Get image blog in meta.
     *
     * @return mixed
     */
    public function getImageBlogAttribute()
    {
        return $this->getMeta('image_blog');
    }

    /**
     * Set image blog in meta.
     *
     * @return mixed
     */
    public function setImageBlogAttribute($value)
    {
        $this->setMeta('image_blog', $value);
    }

    /**
     * Set name.
     *
     * @return mixed
     */
    public function setNameAttribute($value)
    {
        $this->setAttribute('title', $value);
    }

    /**
     * Get name.
     *
     * @return string
     */
    public function getNameAttribute()
    {
        return $this->title;
    }

    /**
     * Get image blog.
     *
     * @return string
     */
    public function getImageBlockAttribute()
    {
        return $this->getMeta('image_blog');
    }

    /**
     * Get name company.
     *
     * @return string
     */
    public function getDisplayNameAttribute()
    {
        return $this->company->display_name;
    }

    /**
     * Set description.
     *
     * @return mixed
     */
    public function setDescriptionAttribute($value)
    {
        $this->setAttribute('content', $value);
    }

    /**
     * Get tag blog.
     *
     * @return mixed
     */
    public function getTagBlogAttribute()
    {
        return $this->getMeta('tag_blog');
    }

    /**
     * Set tag blog.
     *
     * @return mixed
     */
    public function setTagBlogAttribute($value)
    {
        $this->setMeta('tag_blog', $value);
    }

    /**
     * Get author blog.
     *
     * @return mixed
     */
    public function getAuthorBlogAttribute()
    {
        return $this->getMeta('author_blog');
    }

    /**
     * Set author blog.
     *
     * @return mixed
     */
    public function setAuthorBlogAttribute($value)
    {
        $this->setMeta('author_blog', $value);
    }

    /**
     * Get date blog.
     *
     * @return mixed
     */
    public function getDateBlogAttribute()
    {
        return $this->getMeta('date_blog');
    }

    /**
     * Set date blog.
     *
     * @return mixed
     */
    public function setDateBlogAttribute($value)
    {
        $this->setMeta('date_blog', $value);
    }

    /**
     * Get link.
     *
     * @return string
     */
    public function getLinkAttribute()
    {
        return $this->slug;
    }

    /**
     * Set description.
     *
     * @return mixed
     */
    public function getDescriptionAttribute()
    {
        return $this->content;
    }

    /**
     * Set first iamge.
     *
     * @return mixed
     */
    public function getImageAttribute()
    {
        return is_null($this->getFirstMedia('images')) ? null : $this->getFirstMedia('images')->getFullUrl();
    }

    /**
     * Set all iamges.
     *
     * @return mixed
     */
    public function getImagesAttribute()
    {
        return collect($this->getMedia('images'))
            ->mapWithKeys(fn($media, $key) => [$media->getKey() => $media->getFullUrl()])
            ->toArray();
    }

    /**
     * Handle when someone viewed the blog.
     */
    public function recentlyViewedBy($visitor)
    {
    }

    /**
     * Set images.
     *
     * @return mixed
     */
    public function setImageAttribute($values)
    {
        $key = 'images';

        $flag = Field::FILE_DELETE_FLAG;

        if ($flag == request()->get($key)) {
            return $this->deleteMedia(request()->get($flag));
        }

        collect($values)->each(function ($file, $index) use ($key) {
            $file_name = file_name_random($file);

            if (filter_var($file, FILTER_VALIDATE_URL)) {
                $this->addMediaFromUrl($file)
                    ->usingFileName($file_name)
                    ->toMediaCollection($key);
            } elseif ($file instanceof UploadedFile) {
                $this->addMedia($file)->usingFileName($file_name)
                    ->toMediaCollection($key);
            } elseif (is_string($file)) {
                $this->addMediaFromDisk($file)
                        ->usingFileName($file_name)
                        ->preservingOriginal()
                        ->toMediaCollection($key);
            }
        });
    }

    /**
     * Set images.
     *
     * @return mixed
     */
    public function setImagesAttribute($values)
    {
        $key = 'images';

        $flag = Field::FILE_DELETE_FLAG;

        if ($flag == request()->get($key)) {
            return $this->deleteMedia(request()->get($flag));
        }

        collect($values)->each(function ($file, $index) use ($key) {
            if (filter_var($file, FILTER_VALIDATE_URL)) {
            } elseif ($file instanceof UploadedFile) {
                $this->addMedia($file)->usingFileName($file_name)
                    ->toMediaCollection($key);
            } elseif (is_string($file)) {
                $this->addMediaFromDisk($file)
                    ->usingFileName($file_name)
                    ->preservingOriginal()
                    ->toMediaCollection($key);
            }
        });
    }
}
