<?php

namespace Modules\Company\Entities;

use App\Traits\UseUuid;
use Modules\Admin\Form\Field;
use Modules\Post\Entities\Post;
use Spatie\MediaLibrary\File;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class Product extends Post
{
    use UseUuid;

    /**
     * @inheritdoc
     */
    protected $type = 'product';

    /**
     * @inheritdoc
     */
    protected $attributes = [
        'slug' => null,
        'status' => 1,
    ];

    /**
     * @inheritdoc
     */
    protected $appends = [
        'images', 'image', 'name', 'description',
    ];

    /**
     * @inheritdoc
     */
    protected $fillable = [
        'image', 'images', 'name', 'description',
    ];

    /**
     * @inheritdoc
     */
    protected $cachePrefix = 'product-prefix';

    /**
     * Boot model model.
     */
    protected static function boot()
    {
        parent::boot();
    }

    /**
     * @inheritdoc
     */
    public function sluggable(): string
    {
        return $this->name;
    }

    /**
     * @inheritdoc
     */
    public function ensureBeforeExpires()
    {
    }

    /**
     * @inheritdoc
     */
    public function ensureBeforeUnpublish()
    {
    }

    /**
     * Handle when someone viewed the introduce.
     */
    public function recentlyViewedBy($visitor)
    {
    }

    /**
     * Register media for model.
     *
     * @var void
     */
    public function registerMediaCollections()
    {
        $this->addMediaCollection('images')
            ->acceptsFile(fn(File $file) => $file->mimeType === 'image/jpeg'
            || $file->mimeType === 'image/png')->singleFile();
    }

    /**
     * Set name.
     *
     * @return mixed
     */
    public function setNameAttribute($value)
    {
        $this->setAttribute('title', $value);
    }

    /**
     * Get name.
     *
     * @return string
     */
    public function getNameAttribute()
    {
        return $this->title;
    }

    /**
     * Set description.
     *
     * @return mixed
     */
    public function setDescriptionAttribute($value)
    {
        $this->setAttribute('content', $value);
    }

    /**
     * Set description.
     *
     * @return mixed
     */
    public function getDescriptionAttribute()
    {
        return $this->content;
    }

    /**
     * Set first iamge.
     *
     * @return mixed
     */
    public function getImageAttribute()
    {
        return is_null($this->getFirstMedia('images')) ? null : $this->getFirstMedia('images')->getFullUrl();
    }

    /**
     * Set all iamges.
     *
     * @return mixed
     */
    public function getImagesAttribute()
    {
        return collect($this->getMedia('images'))
            ->mapWithKeys(fn($media, $key) => [$media->getKey() => $media->getFullUrl()])
            ->toArray();
    }

    /**
     * Set images.
     *
     * @return mixed
     */
    public function setImageAttribute($values)
    {
        $key = 'images';

        $flag = Field::FILE_DELETE_FLAG;

        if ($flag == request()->get($key)) {
            return $this->deleteMedia(request()->get($flag));
        }

        collect($values)->each(function ($file, $index) use ($key) {
            $file_name = file_name_random($file);

            if (filter_var($file, FILTER_VALIDATE_URL)) {
                $this->addMediaFromUrl($file)
                    ->usingFileName($file_name)
                    ->toMediaCollection($key);
            } elseif ($file instanceof UploadedFile) {
                $this->addMedia($file)->usingFileName($file_name)
                    ->toMediaCollection($key);
            } elseif (is_string($file)) {
                $this->addMediaFromDisk($file)
                        ->usingFileName($file_name)
                        ->preservingOriginal()
                        ->toMediaCollection($key);
            }
        });
    }

    /**
     * Set images.
     *
     * @return mixed
     */
    public function setImagesAttribute($values)
    {
        $key = 'images';

        $flag = Field::FILE_DELETE_FLAG;

        if ($flag == request()->get($key)) {
            return $this->deleteMedia(request()->get($flag));
        }

        collect($values)->each(function ($file, $index) use ($key) {
            if (filter_var($file, FILTER_VALIDATE_URL)) {
            } elseif ($file instanceof UploadedFile) {
                $this->addMedia($file)->usingFileName($file_name)
                    ->toMediaCollection($key);
            } elseif (is_string($file)) {
                $this->addMediaFromDisk($file)
                    ->usingFileName($file_name)
                    ->preservingOriginal()
                    ->toMediaCollection($key);
            }
        });
    }
}
