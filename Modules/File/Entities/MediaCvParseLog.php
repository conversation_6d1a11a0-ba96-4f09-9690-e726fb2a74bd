<?php

namespace Modules\File\Entities;

use Illuminate\Database\Eloquent\Model;

class MediaCvParseLog extends Model
{
    protected $table = 'media_cv_parse_logs';

    public const PARSE_STATUS_PROCESSING = 'processing';
    public const PARSE_STATUS_DONE = 'done';
    public const PARSE_STATUS_ERROR = 'error';

    /**
     * Belongs to media
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function media()
    {
        return $this->belongsTo(Media::class, 'media_id');
    }

    /**
     * Check log is success
     * @param \Illuminate\Database\Eloquent\Builder $builder
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSuccess($builder)
    {
        return $builder->where('status', self::PARSE_STATUS_DONE);
    }
}
