<?php

namespace Modules\File\Entities;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Laravel\Scout\Searchable;
use Modules\File\Contracts\HasConvert;
use Modules\File\Traits\ConvertMarkdown;
use Modules\Job\Entities\Candidate;
use Modules\User\Entities\User;
use Modules\User\Traits\DetectFile;
use Modules\User\Entities\UserMainCv;
use Spatie\MediaLibrary\MediaCollections\Filesystem;
use Spatie\MediaLibrary\MediaCollections\Models\Media as BaseMedia;

/**
 * Modules\File\Entities\Media
 *
 * @property int $id
 * @property string $model_type
 * @property int $model_id
 * @property string $collection_name
 * @property string $name
 * @property string $file_name
 * @property string|null $raw_text
 * @property string|null $raw_text_at
 * @property string|null $mime_type
 * @property string $disk
 * @property int $size
 * @property array $manipulations
 * @property array $custom_properties
 * @property array $responsive_images
 * @property int|null $order_column
 * @property int|null $creator_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property string|null $path_folder
 * @property string|null $md5_hash
 * @property string|null $parsed_at
 * @property-read \Illuminate\Database\Eloquent\Collection|Candidate[] $candidates
 * @property-read UserMainCv|null $mainCv
 * @property-read int|null $candidates_count
 * @property-read mixed $display_name
 * @property-read string $extension
 * @property-read string $human_readable_size
 * @property-read string $type
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $model
 * @property-read User|null $uploadBy
 * @method static Builder|Media newModelQuery()
 * @method static Builder|Media newQuery()
 * @method static \Illuminate\Database\Query\Builder|Media onlyTrashed()
 * @method static Builder|Media ordered()
 * @method static Builder|Media query()
 * @method static Builder|Media whereCollectionName($value)
 * @method static Builder|Media whereCreatedAt($value)
 * @method static Builder|Media whereCreatorId($value)
 * @method static Builder|Media whereCustomProperties($value)
 * @method static Builder|Media whereDeletedAt($value)
 * @method static Builder|Media whereDisk($value)
 * @method static Builder|Media whereFileName($value)
 * @method static Builder|Media whereId($value)
 * @method static Builder|Media whereManipulations($value)
 * @method static Builder|Media whereMd5Hash($value)
 * @method static Builder|Media whereMimeType($value)
 * @method static Builder|Media whereModelId($value)
 * @method static Builder|Media whereModelType($value)
 * @method static Builder|Media whereName($value)
 * @method static Builder|Media whereOrderColumn($value)
 * @method static Builder|Media whereParsedAt($value)
 * @method static Builder|Media wherePathFolder($value)
 * @method static Builder|Media whereRawText($value)
 * @method static Builder|Media whereRawTextAt($value)
 * @method static Builder|Media whereResponsiveImages($value)
 * @method static Builder|Media whereSize($value)
 * @method static Builder|Media whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|Media withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Media withoutTrashed()
 * @mixin \Eloquent
 */
class Media extends BaseMedia implements HasConvert
{
    use Searchable;
    use SoftDeletes;

    public const COLLECTION_FILE_CV_UPLOAD = [
        'UploadFromTopDev',
        'UploadFromMobile',
        'files_cv',
        'UploadFromApplyToAll',
    ];

    public const FILE_CV_UPLOAD_FROM_USER_PROFILE = 'UploadFromUserProfile';
    public const USER_PROFILE_AVATAR = 'UserProfileAvatar';
    public const FILE_CV_UPLOAD_FROM_CV_BUILDER = 'UploadFromCvBuilder';

    const COLLECTION_NAME_TOPDEV_CV = 'files_topdev_cv';// media create by user profile

    /**
     * Convert file resource with new markdown.
     */
    use ConvertMarkdown;

    /**
     * @inheritdoc
     */
    use DetectFile;

    protected $appends = ['path_folder'];

    /**
     * @inheritdoc
     */
    protected $table = 'media';

    /**
     * @inheritdoc
     */
    protected string $cachePrefix = 'media-cache-prefix';

    /**
     * @inheritdoc
     */
    protected int $cacheCooldownSeconds = 5;

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'path_folder' => null,
    ];

    /**
     * @inheritdoc
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function (self $media) {
            if (in_array(SoftDeletes::class, class_uses_recursive($media))) {
                if (!$media->isForceDeleting()) {
                    return;
                }
            }

            // disable event bởi vì laravel-medialibrary có sự kiện xóa file nếu model bị xóa
            // nhung neu disable event se disable luon event build cache & elasticsearch
            $media::unsetEventDispatcher();
        });

        static::saving(function (self $media) {
            $media->uploadBy()->associate(
                auth()->user()
            );
        });
    }

    /**
     * Get user upload pathMedia.
     *
     * @return BelongsTo
     */
    public function uploadBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * @inheritdoc
     */
    public function convertFromPath()
    {
        return $this->getPath();
    }

    /**
     * @inheritdoc
     */
    public function convertTargetPath()
    {
        return $this->getPath();
    }

    /**
     * @inheritdoc
     */
    public function beforeConverting()
    {
        $this->cloneFile();
    }

    /**
     * Transform the resource into a JSON array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->getKey(),
            'url' => $this->getFullUrl(),
            'path' => $this->getRelativePath(),
            'name' => $this->display_name,
            'uploaded' => [
                'date' => $this->created_at->format('d-m-Y'),
                'datetime' => $this->created_at->format('Y-m-d H:i:s'),
                'since' => $this->created_at->diffForHumans(),
            ],
        ];
    }

    /**
     * Get "path_folder".
     */
    public function getPathFolder($type)
    {
        if (empty($this->path_folder)) {
            $directory = $this->created_at->format('Y/m/d');

            return empty($type) ? $directory : $type . '/' . $directory;
        }

        return $this->path_folder;
    }

    /**
     * Get attribute "display_name".
     */
    public function getDisplayNameAttribute()
    {
        return $this->name . strrchr((string) $this->file_name, '.');
    }

    /**
     * Get attribute "path_folder".
     */
    public function getPathFolderAttribute()
    {
        return $this->attributes['path_folder'];
    }

    /**
     * Set attribute "path_folder".
     */
    public function setPathFolderAttribute($value)
    {
        $this->attributes['path_folder'] = $value;
    }

    /**
     * Get relative path.
     */
    public function getRelativePath()
    {
        $diskRootPath = config('filesystems.disks.' . config('filesystems.default') . '.root');

        return Str::after($this->getPath(), realpath($diskRootPath));
    }

    /**
     * Clone file resource.
     *
     * @return string
     */
    public function cloneFile()
    {
        $clone = pathinfo((string) $this->getPath(), PATHINFO_DIRNAME) . DIRECTORY_SEPARATOR . 'orginal-' . $this->file_name;

        if (is_file($clone)) {
            return false;
        }

        app(Filesystem::class)->copyFromMediaLibrary($this, $clone);

        return $clone;
    }

    /**
     * Get all candidate.
     *
     * @return string
     */
    public function candidates()
    {
        return $this->belongsToMany(Candidate::class, Mediable::class, 'media_id', 'mediable_id')
                    ->withPivot(['created_at', 'updated_at', 'mediable_type']);
    }

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs(): string
    {
        return 'media_ams_v1';
    }

    /**
     * Determine if the model should be searchable.
     *
     * @return bool
     */
    public function shouldBeSearchable(): bool
    {
        return $this->deleted_at == null;
    }

    public function toSearchableArray(): array
    {
        return [
            'id' => $this->getKey(),
            'url' => $this->getFullUrl(),
            'path' => $this->getRelativePath(),
            'model_type' => $this->model_type,
            'model_id' => $this->model_id,
            'collection_name' => $this->collection_name,
            'name' => $this->name,
            'display_name' => $this->display_name,
            'file_name' => $this->file_name,
            'disk' => $this->disk,
            'size' => $this->size,
            'manipulations' => $this->manipulations,
            'custom_properties' => $this->custom_properties,
            'responsive_images' => $this->responsive_images,
            'order_column' => $this->order_column,
            'creator_id' => $this->creator_id,
            'path_folder' => $this->path_folder,
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }

    public function mainCv(): MorphOne
    {
        return $this->morphOne(UserMainCv::class, 'cv');
    }

    public function cvParseLog()
    {
        return $this->hasOne(MediaCvParseLog::class, 'media_id');
    }

    public function scopeOnlyCV($query)
    {
        return $query->whereIn('collection_name', [
            'files_cvbuilder',
            self::FILE_CV_UPLOAD_FROM_CV_BUILDER,
            'files_cv',
            'UploadFromTopDev',
            'UploadFromApplyToAll',
            'file_cvbuilder',
            'file_cvbuilders',
            self::FILE_CV_UPLOAD_FROM_USER_PROFILE,
        ]);
    }
}
