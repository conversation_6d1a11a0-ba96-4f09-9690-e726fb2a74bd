<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::middleware('auth:api')->get('/file', function (Request $request) {
//     return $request->user();
// });

Route::prefix('files')->group(function () {
    Route::post('/upload', 'MediaController@upload');
    Route::post('/', 'MediaController@uploadV2')->middleware('auth:api');
    // Route::get('/candidate/{candidate}', 'MediaController@employerViewedAt');
    Route::post('profile_upload', 'MediaController@profile_upload')->middleware('auth:api');
    Route::post('profile_avatar', 'MediaController@profileAvatar')->middleware('auth:api');
});

Route::prefix('galleries')->middleware('auth:api')->group(function () {
    Route::post('/companies/{company}', 'CompanyController@galleries');
});

Route::prefix('media')->group(function () {
    Route::middleware('auth:api')->group(function () {
        Route::delete('/{media}', 'MediaController@destroy');
    });
});
