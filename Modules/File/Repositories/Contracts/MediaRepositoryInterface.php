<?php

namespace Modules\File\Repositories\Contracts;

use Redmix0901\Core\Repositories\Interfaces\BaseRepositoryInterface;

interface MediaRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = '*', $params = [], $fields = '*');

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearchStatistics($keyword = '*', $params = [], $fields = '*');
}
