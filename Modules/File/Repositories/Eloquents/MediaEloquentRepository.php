<?php

namespace Modules\File\Repositories\Eloquents;

use Modules\File\Repositories\Contracts\MediaRepositoryInterface;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\DateHistogramAggregation;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\TermsAggregation;
use ONGR\ElasticsearchDSL\Aggregation\Metric\CardinalityAggregation;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MultiMatchQuery;
use ONGR\ElasticsearchDSL\Query\MatchAllQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\IdsQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\RangeQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermsQuery;
use ONGR\ElasticsearchDSL\Sort\FieldSort;
use Redmix0901\Core\Repositories\Eloquent\BaseRepository;

class MediaEloquentRepository extends BaseRepository implements MediaRepositoryInterface
{
    private $arrPeriod = [
        'daily' => 'day',
        'monthly' => 'month',
        'yearly' => 'year',
        'weekly' => 'week',
    ];

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = null, $params = [], $fields = '*')
    {
        \Log::info('Search MediaEloquentRepository form Elasticsearch');

        return $this->querySearch($keyword, $params, $fields)->raw();
    }

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearchStatistics($keyword = null, $params = [], $fields = '*')
    {
        return $this->querySearchStatistics($keyword, $params, $fields)->raw();
    }

    /**
     * @inheritdoc
     */
    private function querySearch($keyword, $params, $fields)
    {
        return $this->model->search('*', function ($client, $body) use ($keyword, $params) {
            // Pagination
            $size = $params['page_size'] ?? 10;
            $from = isset($params['page']) ? $size * ($params['page'] - 1) : 0;

            $baseQuery = new BoolQuery();

            /*
            * Tìm kiếm keyword các trường title và tên của employer
            */
            if (!empty($keyword)) {
                $baseQuery->add(new MultiMatchQuery(
                    ['name', 'display_name', 'file_name'],
                    $keyword,
                    ['operator' => 'or']
                ), BoolQuery::MUST);
            }

            /*
             * Lọc các công cty có id sau
             */
            if (array_key_exists('ids', $params)) {
                $baseQuery->add(new IdsQuery(empty($params['ids']) ? [] : explode(',', (string) $params['ids'])), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'model_type')) {
                $baseQuery->add(new TermQuery('model_type', $params['model_type']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'model_id')) {
                $baseQuery->add(new TermQuery('model_id', $params['model_id']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'model_ids')) {
                $baseQuery->add(new TermsQuery('model_id', $params['model_ids']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'collection_name')) {
                $baseQuery->add(new TermQuery('collection_name', $params['collection_name']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'collections')) {
                $baseQuery->add(new TermsQuery('collection_name', explode(',', (string) $params['collections'])), BoolQuery::MUST);
            }

            if (empty($keyword) && empty($params)) {
                $baseQuery = new MatchAllQuery();
            }

            $body->setSize($size);
            $body->setFrom($from);
            $body->addQuery($baseQuery);
            $body->addSort(new FieldSort('created_at', null, ['order' => FieldSort::DESC]));

            return $client->search(['index' => $this->model->searchableAs(), 'body' => $body->toArray()])->asArray();
        })
        ->query(function ($builder) {
            //$builder->with('tags', 'salary', 'company', 'author', 'addresses');
        });
    }

    private function hasQuery($query, $key)
    {
        return (bool) (isset($query[$key]) && !is_null($query[$key]));
    }

    private function querySearchStatistics($keyword, $params, $fields)
    {
        return $this->model->search('*', function ($client, $body) use ($keyword, $params) {
            // Pagination
            $size = $params['page_size'] ?? 10;
            $from = isset($params['page']) ? $size * ($params['page'] - 1) : 0;

            $baseQuery = new BoolQuery();

            if ($this->hasQuery($params, 'model_type')) {
                $baseQuery->add(new TermQuery('model_type', $params['model_type']), BoolQuery::MUST);
            }

            //query by period
            if ($this->hasQuery($params, 'created_at')) {
                $dateRangeAggregation = new RangeQuery(
                    'created_at',
                    [
                                    'gte' => $params['created_at']['from'],
                                    'lte' => $params['created_at']['to'],
                                    'format' => 'yyyy-MM-dd HH:mm:ss',
                                ]
                );

                $body->addQuery($dateRangeAggregation);
            }

            $body->setSize($size);
            $body->setFrom($from);
            $body->addQuery($baseQuery);

            $dateHist = new DateHistogramAggregation('statis');
            $dateHist->setField('created_at');
            if ($this->hasQuery($params, 'period')) {
                if (array_key_exists($params['period'], $this->arrPeriod)) {
                    $dateHist->setInterval($this->arrPeriod[$params['period']]);
                }
            } else {
                $dateHist->setInterval('month');
            }

            // UploadFromCvBuilder
            $collection = new TermsAggregation('collection_name', 'collection_name', "doc['collection_name'].value.toLowerCase().trim()");
            $collection->addParameter('size', 10000);

            $cardinalityAggregation = new CardinalityAggregation('model_id_count');
            $cardinalityAggregation->setField('model_id');
            $collection->addAggregation($cardinalityAggregation);

            $dateHist->addAggregation($collection);

            $body->addAggregation($dateHist);
            $body->addSort(new FieldSort('created_at', null, ['order' => FieldSort::DESC]));

            return $client->search(['index' => $this->model->searchableAs(), 'body' => $body->toArray()])->asArray();
        })
        ->query(function ($builder) {
        });
    }
}
