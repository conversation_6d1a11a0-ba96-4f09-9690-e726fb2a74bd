<?php

namespace Modules\File\Repositories\Caches;

use Modules\File\Repositories\Contracts\MediaRepositoryInterface;
use Redmix0901\Core\Repositories\Caches\BaseCacheDecorator;
use Redmix0901\Core\Repositories\Interfaces\BaseRepositoryInterface;

class MediaCacheDecorator extends BaseCacheDecorator implements MediaRepositoryInterface
{
    public function __construct(BaseRepositoryInterface $base)
    {
        //$this->time = 100;

        // Invoke parent
        parent::__construct($base);
    }

    public function searchOnElasticsearch($keyword = '*', $params = [], $fields = '*')
    {
    }

    public function searchOnElasticsearchStatistics($keyword = '*', $params = [], $fields = '*')
    {
    }
}
