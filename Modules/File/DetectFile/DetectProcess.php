<?php

namespace Modules\File\DetectFile;

use Modules\File\Contracts\HasConvert;
use Symfony\Component\Process\Process;

abstract class DetectProcess
{
    /**
     * Build coomand line run process.
     *
     * @return array
     */
    abstract public function getCommandLine();

    /**
     * @return HasConvert
     */
    protected $path;

    /**
     * detect .pdf to .txt
     * $path_info['dirname'] . DIRECTORY_SEPARATOR .$path_info['filename'] . '.txt'.
     * @return string
     */
    protected $path_output;

    /**
     * @return Process
     */
    protected $process;

    /**
     * Redirect the user of the application to the provider's authentication screen.
     *
     * @return void
     */
    public function detect()
    {
        $this->process->run();

        return $this;
    }

    public function output()
    {
        return $this->process->getOutput();
    }

    public function success()
    {
        return $this->process->isSuccessful();
    }

    /**
     * @return Process
     */
    public function getProcess()
    {
        return $this->process;
    }

    /**
     * @return self
     */
    public function from($path, $path_output): self
    {
        $this->path = $path;
        $this->path_output = $path_output;
        $this->process = new Process(
            $this->getCommandLine()
        );

        return $this;
    }
}
