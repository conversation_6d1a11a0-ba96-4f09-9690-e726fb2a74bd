<?php

namespace Modules\File\Engines;

use Modules\File\Contracts\HasConvert;
use Symfony\Component\Process\Process;

abstract class ConvertProcess
{
    /**
     * Build coomand line run process.
     *
     * @return array
     */
    abstract public function getCommandLine();

    /**
     * @return HasConvert
     */
    protected $source;

    /**
     * @return Process
     */
    protected $process;

    /**
     * Redirect the user of the application to the provider's authentication screen.
     *
     * @return void
     */
    public function convert()
    {
        $this->source->beforeConverting();

        //$this->process->run();
        exec($this->process->getCommandLine());

        return $this;
    }

    /**
     * @return Process
     */
    public function getProcess()
    {
        return $this->process;
    }

    /**
     * @return self
     */
    public function fromSource(HasConvert $source): self
    {
        $this->source = $source;
        $this->process = new Process(
            $this->getCommandLine()
        );

        return $this;
    }
}
