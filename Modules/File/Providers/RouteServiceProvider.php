<?php

namespace Modules\File\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * Called before routes are registered.
     *
     * Register any model bindings or pattern based filters.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map()
    {
        Route::prefix(env('API_PREFIX'))
            ->middleware('api')
            ->namespace('Modules\File\Http\Controllers\API')
            ->group(__DIR__ . '/../Routes/api.php');
    }
}
