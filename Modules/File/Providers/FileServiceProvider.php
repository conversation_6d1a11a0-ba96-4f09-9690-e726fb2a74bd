<?php

namespace Modules\File\Providers;

use Illuminate\Database\Eloquent\Factory;
use Illuminate\Support\ServiceProvider;
use Modules\File\Entities\Media;
use Modules\File\Repositories\Contracts\MediaRepositoryInterface;
use Modules\File\Repositories\Eloquents\MediaEloquentRepository;

class FileServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->registerFactories();
        $this->loadMigrationsFrom(__DIR__ . '/../Database/Migrations');
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);

        $this->app->singleton(MediaRepositoryInterface::class, fn() => new MediaEloquentRepository(
            new Media
        ));
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            __DIR__ . '/../Config/config.php' => config_path('file.php'),
        ], 'config');
        $this->mergeConfigFrom(
            __DIR__ . '/../Config/config.php',
            'file'
        );
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews()
    {
        $viewPath = resource_path('views/modules/file');

        $sourcePath = __DIR__ . '/../Resources/views';

        $this->publishes([
            $sourcePath => $viewPath,
        ], 'views');

        $this->loadViewsFrom(array_merge(array_map(fn($path) => $path . '/modules/file', \Config::get('view.paths')), [$sourcePath]), 'file');
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/file');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'file');
        } else {
            $this->loadTranslationsFrom(__DIR__ . '/../Resources/lang', 'file');
        }
    }

    /**
     * Register an additional directory of factories.
     *
     * @return void
     */
    public function registerFactories()
    {
        if (!app()->environment('production') && $this->app->runningInConsole()) {
            app(Factory::class)->load(__DIR__ . '/../Database/factories');
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }

    /**
     * @inheritdoc
     */
    public static function import()
    {
        // parent::createMenu('Media manager', 'media', 'fa-file');
        $media_menu = Menu::create([
            'title'     => 'Media manager',
            'icon'      => 'fa-file',
            'uri'       => 'media',
        ]);
        // add role to menu.
        Menu::find($media_menu->id)->roles()->save(Role::first());
    }
}
