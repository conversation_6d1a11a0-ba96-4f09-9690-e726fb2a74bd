<?php

namespace Modules\File\Transformers;

use App\Traits\AddDataResource;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource as Resource;
use Modules\File\Entities\Media;
use Modules\User\Entities\User;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class AttachedResumeResource extends Resource
{
    use ElasticCollectionTrait;
    use AddDataResource;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        $features = User::getFeatures();

        $source = 'Uploaded from computer';
        $type = 'media';
        $assets = [
            [
                'id' => $this->id ?? null,
                'download_url' => $this->url ?? null,
                'name' => $this->name ?? null,
            ],
        ];
        $candidates = [];
        $candidates = isset($this->id) ? ((static::$dataRelation)['candidateByMediaIds'][$this->id] ?? []) : [];

        if (isset($this->resume_id) && !empty($this->resume_id)) {
            $this->id = $this->resume_id;
            $this->name = $this->resume_name;

            $assets = [];
            $type = 'cvbuilder';
            $source = 'Created on TopDev';

            $features['edit'] = true;
            $features['apply'] = false;
            if (!empty($this->progress_value) && $this->progress_value == '100') {
                $features['apply'] = true;
            }

            $candidates = (static::$dataRelation)['candidateByCvBuilders'][$this->id] ?? [];
        }

        if (!empty($candidates)) {
            $features['edit'] = false;
            $features['delete'] = false;
            $features['duplicate'] = true;
            $candidates = $this->getCandidies($candidates);
        }
        $collectionName = $this->collection_name ?? '';
        if ($collectionName === Media::COLLECTION_NAME_TOPDEV_CV) {
            $type = 'topdev_cv';
            $string = $this->name ?? '';
            $underscorePos = strrpos($string, '_');
            $this->name = substr($string, 0, $underscorePos);
        }
        return [
            'features' => $features,
            'id' => (string) $this->id,
            'source' => $source,
            'name' => $this->name ?? '',
            'created_at' => isset($this->created_at) ? strtotime(Carbon::createFromFormat('Y-m-d H:i:s', $this->created_at)->format('H:i:s d-m-Y')) : null,
            'updated_at' => isset($this->updated_at) ? strtotime(Carbon::createFromFormat('Y-m-d H:i:s', $this->updated_at)->format('H:i:s d-m-Y')) : null,
            'type' => $type,
            'assets' => $assets,
            'applies' => $candidates,
        ];
    }

    private function getCandidies($candidates)
    {
        $companies = (static::$dataRelation)['companies'] ?? [];
        $jobs = (static::$dataRelation)['jobs'] ?? [];
        $candidates = $candidates->map(function ($candidate) use ($companies, $jobs) {
            $job = $jobs[$candidate->job['id']] ?? null;
            if (empty($job)) {
                \Log::info('---------candidate empty job');
                \Log::info($candidate->id);
            }

            try {
                $company = !empty($job->owned_id) ? $companies[$job->owned_id] : null;
            } catch (\Exception) {
                $company = (object) $job->company;
            }

            return [
                'id' => $candidate->id,
                'applied_at' => strtotime(Carbon::createFromFormat('Y-m-d H:i:s', $candidate->created_at)->format('H:i:s d-m-Y')),
                'job_id' => $candidate->job['id'] ?? null,
                'job_title' => $job->title ?? null,
                'company_id' => $company->id ?? null,
                'company_slug' => $company->slug ?? null,
                'company_image_logo' => $company->image_logo ?? null,
                'company_name' => $company->display_name ?? null,
                'recalled_at' => $candidate->recalled_at ? strtotime(Carbon::createFromFormat('Y-m-d H:i:s', $candidate->recalled_at)->format('H:i:s d-m-Y')) : null,
                'is_remove_cv' => $candidate->is_remove_cv ?? false,
            ];
        });

        return $candidates;
    }
}
