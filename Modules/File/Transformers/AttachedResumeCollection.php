<?php

namespace Modules\File\Transformers;

use App\Traits\AddDataCollection;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Redmix0901\ElasticResource\ElasticCollection;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class AttachedResumeCollection extends ResourceCollection
{
    use ElasticCollectionTrait;
    use AddDataCollection;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        $this->dataRelation = $this->dataRelation($request->all());

        return AttachedResumeResource::dataRelation($this->dataRelation)->collection($this->collection);
    }

    private function dataRelation($rq)
    {
        $data = [];
        $mediaIds = array_filter($this->collection->pluck('id')->toArray());
        $resumeIds = array_filter($this->collection->pluck('resume_id')->toArray());

        $candidateByMediaIds = $this->getDataRelationModal(
            'candidate',
            [
                'resume_id' => $this->getUserId(),
                'media_ids' => implode(',', $mediaIds),
                'page_size' => 10000,
            ]
        );

        $candidateByMediaIds = (new ElasticCollection($candidateByMediaIds))->all();
        $data['candidateByMediaIds'] = $candidateByMediaIds->groupBy('media_id');
        $jobsByMedia = $candidateByMediaIds->pluck('job.id')->toArray();

        $candidateByCvBuilders = $this->getDataRelationModal(
            'candidate',
            [
                'resume_id' => $this->getUserId(),
                'cvbuilder_ids' => implode(',', $resumeIds),
                'page_size' => 10000,
            ]
        );

        $candidateByCvBuilders = (new ElasticCollection($candidateByCvBuilders))->all();
        $data['candidateByCvBuilders'] = $candidateByCvBuilders->groupBy('cvbuilder_id');
        $jobsByCvbuilders = $candidateByCvBuilders->pluck('job.id')->toArray();

        $jobIds = array_filter(array_merge($jobsByMedia, $jobsByCvbuilders));
        if ($jobIds) {
            $jobs = $this->getDataRelationModal(
                'jobs',
                [
                    'ids' => implode(',', $jobIds),
                    'page_size' => 10000,
                    // 'status' => 3
                ]
            );

            $jobs = (new ElasticCollection($jobs))->all();
            $data['jobs'] = $jobs->keyBy('id');

            $companyIds = $jobs->pluck('owned_id')->unique()->toArray();
            if ($companyIds) {
                $companies = $this->getDataRelationModal(
                    'company',
                    [
                        'ids' => implode(',', $companyIds),
                        'page_size' => 10000,
                    ]
                );

                $companies = (new ElasticCollection($companies))->all();
                $data['companies'] = $companies->keyBy('id');
            }
        }

        return $data;
    }
}
