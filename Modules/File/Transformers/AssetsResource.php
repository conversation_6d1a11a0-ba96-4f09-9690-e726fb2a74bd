<?php

namespace Modules\File\Transformers;

use Illuminate\Http\Resources\Json\JsonResource as Resource;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class AssetsResource extends Resource
{
    use ElasticCollectionTrait;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'download_url' => $this->getFullUrl(),
            'name' => $this->name,
        ];
    }
}
