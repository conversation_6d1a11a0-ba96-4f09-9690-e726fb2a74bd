<?php

namespace Modules\File\Transformers;

use Illuminate\Http\Resources\Json\ResourceCollection;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class AssetsResourceCollection extends ResourceCollection
{
    use ElasticCollectionTrait;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        return AssetsResource::collection($this->collection);
    }
}
