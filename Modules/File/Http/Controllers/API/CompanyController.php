<?php

namespace Modules\File\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Company\Entities\Company;
use Redmix0901\Core\Http\Responses\BaseHttpResponse;

class CompanyController extends Controller
{
    public function galleries(Company $company, Request $request, BaseHttpResponse $response)
    {
        $request->validate([
            'image_galleries' => 'required|array',
            'image_galleries.*' => 'required|mimes:jpg,png,jpeg|max:5120',
        ], [
            'image_galleries.required' => 'Please choose image',
            'image_galleries.required' => 'Image Input must be array',
            'image_galleries.*.required' => 'File is required',
            'image_galleries.*.mimes' => 'File extension is not accepted. Please choose jpeg, jpg or png file.',
            'image_galleries.*.max' => 'File size is too big. Maximum is 5MB',
        ]);

        $directoryPath = '/galleries' . date('Y/m/d');

        $filePathList = [];
        foreach ($request->image_galleries as $gallery) {
            $filePathList[] = $gallery->storeAs($directoryPath, 'galleries' . time() . $gallery->getClientOriginalName());
        }

        $company->update([
            'image_galleries' => $filePathList,
        ]);

        return $response->setData(
            $company->image_galleries->take(-1 * count($request->image_galleries))->values()->toArray()
        );
    }
}
