<?php

namespace Modules\File\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Jobs\DeleteFileCvBuilder;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Modules\File\Entities\Media;
use Modules\File\Http\Requests\UploadFile;
use Modules\File\Jobs\UpdateEmployerViewedAt;
use Modules\Job\Repositories\Contracts\CandidateRepositoryInterface;
use Modules\User\Entities\User;
use Modules\User\Entities\UserMainCv;
use Modules\User\Entities\UserProfile;
use Modules\User\Repositories\Contracts\UserRepositoryInterface;
use Modules\User\Repositories\Contracts\UserResumeRepositoryInterface;
use Redmix0901\Core\Http\Responses\BaseHttpResponse;
use Redmix0901\ElasticResource\ElasticCollection;

class MediaController extends Controller
{
    /**
     * Delete media.
     *
     * @param $media
     * @param  Request  $request
     * @param  BaseHttpResponse  $response
     *
     * @return BaseHttpResponse
     * @throws Exception
     */
    public function destroy($media, Request $request, BaseHttpResponse $response)
    {
        $user = $request->user('api');
        if (ctype_digit((string) $media)) {
            $media = Media::where('id', $media)->where('model_id', $user->id)->first();

            if ($media instanceof Media) {
                $model = $media->model;
                $media->delete();

                if ($media->mainCv instanceof UserMainCv
                    && $media->mainCv->user instanceof User
                    && $media->mainCv->user->userProfile instanceof UserProfile
                ) {
                    $media->mainCv->user->setMainCv($media->mainCv->user->userProfile->id, UserMainCv::CV_TYPE_TOPDEV_CV);
                }

                // Hot fix
                if (method_exists($model, 'disableCache')) {
                    $model = $media->model()->disableCache()->first();
                }

                if (method_exists($model, 'searchable')) {
                    if ($model->shouldBeSearchable()) {
                        $model->searchable();
                    }
                }
            }
        } else {
            try {
                Log::info('-------------media delete----');
                Log::info(print_r([
                    'email' => $request->user('api')->email,
                    'page_size' => 1,
                    'id' => $media,
                ], true));
                Log::info('-------------media delete result----');
                $userResume = app(UserResumeRepositoryInterface::class)->searchOnElasticsearch(
                    null,
                    [
                        'email' => $request->user('api')->email,
                        'page_size' => 1,
                        'id' => $media,
                    ]
                );

                Log::info(print_r($userResume['hits']['hits'], true));

                if (isset($userResume['hits']['hits']) && !empty($userResume['hits']['hits'])) {

                    $deleteCvBuilder = $this->destroyFileCvBuilder($media);

                    // $deleteCvBuilder = new UserResume(['id'=>$media]);
                    // $deleteCvBuilder->unsearchable();

                    DeleteFileCvBuilder::dispatch($media);
                }
            } catch (\Throwable) {
                $userResume = null;
            }
        }

        return $response->setMessage('Deleting');
    }

    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:pdf,docx,doc|max:10240',
        ], [
            'file.required' => 'File is required',
            'file.mimes' => 'File extension is not accepted. Please choose pdf, docx or doc file.',
            'file.max' => 'File size is too big. Maximum is 10MB',
        ]);

        $directoryPath = 'files/' . date('Y/m/d');

        $filePath = $request->file->store($directoryPath);

        return response()->json(['path' => $filePath]);
    }

    public function uploadV2(UploadFile $request)
    {
        $user = $request->user('api');

        if (!$user) {
            if (isset($request['email']) && !empty($request['email'])) {
                $user = $this->resolveUser($request);
            } else {
                return response()->json([
                    'messages' => ['email is required'],
                    'success' => false,
                ]);
            }
        }

        if (!$user) {
            return response()->json([
                'messages' => ['errors'],
                'success' => false,
            ]);
        }

        // Disable queue for scout index new file
        config(['scout.queue' => false]);

        $user->addFile($request->files, $request->upload_from);

        return response()->json([
            'messages' => ['upload success'],
            'success' => true,
            'data' => $user->getMedia($request->upload_from)->sortByDesc('created_at')->first()->toArray(),
        ]);
    }

    public function employerViewedAt($candidate_id, Request $request, BaseHttpResponse $response)
    {
        $time = Carbon::now();
        $candidate = app(CandidateRepositoryInterface::class)->searchOnElasticsearch(
            null,
            [
                'candidate_id' => $candidate_id,
            ]
        );

        $candidate = new ElasticCollection($candidate);

        if ($candidate->total() > 0) {
            $url = ($candidate->hits())['hits'][0]['_source']['files_cv'][0]['url'] ?? null;
            if (!empty($url)) {
                $this->dispatch(new UpdateEmployerViewedAt($candidate_id, $time));

                return redirect($url);
            }

            return $response->setMessage('File cv not found');
        }

        return $response->setMessage('Candidate not found');
    }

    public function profile_upload(Request $request)
    {
        $request->validate([
            'files' => 'required|mimes:pdf,docx,doc|max:5120',
        ], [
            'files.required' => 'File is required',
            'files.mimes' => 'File extension is not accepted. Please choose pdf, docx or doc file.',
            'files.max' => 'File size is too big. Maximum is 5MB',
        ]);

        $user = $request->user('api');

        if (!$user) {
            return response()->json(['message' => 'Not found user', 'error' => true]);
        }

        $user->addFile($request->files, Media::FILE_CV_UPLOAD_FROM_USER_PROFILE);
        $media = $user->getLatestMedia(Media::FILE_CV_UPLOAD_FROM_USER_PROFILE);

        return response()->json(['message' => 'Upload success', 'error' => false, 'media_id' => $media->id]);
    }

    public function profileAvatar(Request $request)
    {
        $request->validate([
            'files' => 'required|mimes:jpg,jpeg,png,bmp,tiff|max:5120',
        ], [
            'files.required' => 'Avatar is required',
            'files.mimes' => 'Avatar extension is not accepted. Please choose jpg, jpeg, png, bmp, tiff file.',
            'files.max' => 'Avatar size is too big. Maximum is 5MB',
        ]);

        $user = $request->user('api');

        if (!$user) {
            return response()->json(['message' => 'Not found user', 'error' => true], 500);
        }

        // Disable queue for scout index new file
        config(['scout.queue' => false]);
        $user->addFile($request->files, Media::USER_PROFILE_AVATAR);
        $media = $user->getLatestMedia(Media::USER_PROFILE_AVATAR);

        return response()->json(['message' => 'Upload success', 'error' => false, 'avatar_url' => $media->getFullUrl()]);
    }

    private function resolveUser($request)
    {
        $user = app(UserRepositoryInterface::class)->getModel()
        ->where('email', $request->email)->first();

        if (!$user) {
            $user = app(UserRepositoryInterface::class)->firstOrCreate(
                ['email' => $request->email],
                array_merge(
                    (array) $request->all(),
                    [
                        'type' => User::RESUME_TYPE,
                    ]
                )
            );
        }

        return $user;
    }

    /**
     * Destroy file cv builder.
     */
    private function destroyFileCvBuilder($resume_id)
    {
        $userResume = app(UserResumeRepositoryInterface::class)->detroyByQueryId($resume_id);
        Log::info('-------destroyFileCvBuilder------');
        Log::info(print_r($userResume, true));
        if (isset($userResume['deleted']) && (int) $userResume['deleted'] > 0) {
            DeleteFileCvBuilder::dispatch($resume_id);

            return true;
        }

        return false;
    }
}
