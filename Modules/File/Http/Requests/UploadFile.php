<?php

namespace Modules\File\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UploadFile extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'upload_from' => 'required',
            'email' => 'nullable|email',
            'files' => 'required|mimes:pdf,docx,doc|max:10240',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {
        return [
            'upload_from.required' => 'The field upload_from is required!',
            'email.email' => "The format of the email address isn't correct?",
            // 'upload_from.regex' => "The format of upload_from isn't correct?",
            'files.required' => 'File is required',
            'files.mimes' => 'File extension is not accepted. Please choose pdf, docx or doc file.',
            'files.max' => 'File size is too big. Maximum is 10MB',
        ];
    }
}
