<?php

namespace Modules\File\Listeners;

use App\Helpers\QueueHeplers;

class PublishFileEvent
{
    public function sendRabbitmq($event)
    {
        app(QueueHeplers::class)->createQueueRabbitmq($event);
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @param  Illuminate\Events\Dispatcher  $events
     * @return array
     */
    public function subscribe($events)
    {
        $events->listen(
            'Spatie\MediaLibrary\Events\MediaHasBeenAdded',
            'Modules\File\Listeners\PublishFileEvent@sendRabbitmq'
        );
    }
}
