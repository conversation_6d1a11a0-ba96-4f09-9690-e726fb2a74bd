<?php

namespace Modules\File\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Spatie\MediaLibrary\Events\MediaHasBeenAdded;

class HandleMediaUploaded implements ShouldQueue
{
    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     */
    public function handle(MediaHasBeenAdded $event)
    {
        $media = $event->media;

        // @phpstan-ignore-next-line
        if ($media->collection_name == 'files_hackerrank') { // hard code :v
            $media->queueMakeConvertMediaWithMarkdown();
        }
    }
}
