<?php

namespace Modules\File\Services;

use Illuminate\Support\Manager;
use Modules\File\DetectFile\DocProvider;
use Modules\File\DetectFile\DocxProvider;
use Modules\File\DetectFile\PdfProvider;

class DetectNewFile extends Manager
{
    /**
     * Get a driver instance.
     *
     * @param  string|null  $name
     * @return mixed
     */
    public function provider($provider = null)
    {
        return $this->driver($provider);
    }

    /**
     * Get the default driver name.
     *
     * @return string
     */
    public function getDefaultDriver()
    {
        return 'null';
    }

    /**
     * Create a doc driver.
     *
     * @return DocProvider
     */
    public function createDocDriver()
    {
        return new DocProvider;
    }

    /**
     * Create a docx driver.
     *
     * @return DocxProvider
     */
    public function createDocxDriver()
    {
        return new DocxProvider;
    }

    /**
     * Create a pdf driver.
     *
     * @return PdfProvider
     */
    public function createPdfDriver()
    {
        return new PdfProvider;
    }
}
