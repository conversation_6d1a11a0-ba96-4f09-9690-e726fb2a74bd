<?php

namespace Modules\File\Services;

use Spatie\MediaLibrary\Models\Media;
use Spatie\MediaLibrary\PathGenerator\PathGenerator as BasePathGenerator;

class PathGenerator implements BasePathGenerator
{
    public function getPath(Media $media) : string
    {
        // @phpstan-ignore-next-line
        if ($media->mime_type == 'image/jpeg' || $media->mime_type == 'image/png') {
            return $this->pathMedia($media, 'images');
        }

        return $this->pathMedia($media, 'files');
    }

    public function getPathForConversions(Media $media) : string
    {
        return $this->getPath($media) . 'conversions/';
    }

    public function getPathForResponsiveImages(Media $media): string
    {
        return $this->getPath($media) . 'responsive-images/';
    }

    public function pathMedia(Media $media, $type)
    {
        return $media->getPathFolder($type) . '/';
    }
}
