<?php

namespace Modules\File\Services;

use Illuminate\Support\Manager;
use Modules\File\Engines\EnhancvProvider;
use Modules\File\Engines\NullProvider;
use Modules\File\Engines\TopcvProvider;
use Modules\File\Engines\WowcvProvider;

class ConvertNewMarkdownManager extends Manager
{
    /**
     * Get a driver instance.
     *
     * @param  string|null  $name
     * @return mixed
     */
    public function provider($provider = null)
    {
        return $this->driver($provider);
    }

    /**
     * Get the default driver name.
     *
     * @return string
     */
    public function getDefaultDriver()
    {
        return 'null';
    }

    /**
     * Create a null driver.
     *
     * @return NullProvider
     */
    public function createNullDriver()
    {
        return new NullProvider;
    }

    /**
     * Create a enhancv driver.
     *
     * @return EnhancvProvider
     */
    public function createEnhancvDriver()
    {
        return new EnhancvProvider;
    }

    /**
     * Create a topcv driver.
     *
     * @return TopcvProvider
     */
    public function createTopcvDriver()
    {
        return new TopcvProvider;
    }

    /**
     * Create a wowcv driver.
     *
     * @return WowcvProvider
     */
    public function createWowcvDriver()
    {
        return new WowcvProvider;
    }
}
