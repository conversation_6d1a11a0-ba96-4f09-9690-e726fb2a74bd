<?php

namespace Modules\File\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class EmployersProgressUpdate
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(private $candidate)
    {
    }

    /**
     * id = 1.
     */
    public function candidate()
    {
        return $this->candidate;
    }
}
