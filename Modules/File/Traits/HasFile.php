<?php

namespace Modules\File\Traits;

use Illuminate\Support\Str;
use Modules\Admin\Form\Field;
use Spatie\MediaLibrary\InteractsWithMedia;
use Symfony\Component\HttpFoundation\File\UploadedFile;

trait HasFile
{
    use InteractsWithMedia;

    public function getFile($key, $field = null)
    {
        if ($field == 'url') {
            return collect($this->getMedia($key))
                ->mapWithKeys(fn($media) => [$media->getKey() => $media->getFullUrl()])
                ->toArray();
        }

        return $this->getMedia($key);
    }

    public function addFile($values, $collection)
    {
        $flag = Field::FILE_DELETE_FLAG;

        // Bug xoa tat ca file
        if (request()->has($flag)) {
            return $this->deleteMedia(request()->get($flag));
        }

        collect($values)->each(function ($file, $index) use ($collection) {
            $file_name = config('constant.FREFIX_NAME_FILE.FILE_UPLOAD') . '-' . file_name_random($file);

            if (filter_var($file, FILTER_VALIDATE_URL)) {
            } elseif ($file instanceof UploadedFile) {
                $this
                    ->addMedia($file)
                    ->usingFileName($file_name)
                    ->withProperties([
                        'md5_hash' => md5_file($file->getRealPath()),
                    ])
                    ->toMediaCollection($collection);
            } elseif (is_string($file)) {
                $this->addMediaFromDisk($file)
                        ->usingFileName($file_name)
                        ->preservingOriginal()
                        ->toMediaCollection($collection);
            }
        });

        return $this;
    }

    /**
     * Get the value of an term attribute.
     *
     * @param  string  $attribute
     * @return mixed
     */
    public function fileAttribute($attribute)
    {
        if (empty($attribute)) {
            return null;
        }

        if (in_array($attribute, $this->getArrayableFiles())) {
            return $this->getFile($attribute);
        }

        $field = Str::afterLast($attribute, '_');
        $attribute = Str::beforeLast($attribute, '_');

        if (in_array($attribute, $this->getArrayableFiles())) {
            return $this->getFile($attribute, $field);
        }

        return null;
    }

    /**
     * @param string $attribute
     * @return bool|string
     */
    public function isPropertyFile($attribute)
    {
        if (in_array($attribute, $this->getArrayableFiles())) {
            return $attribute;
        }

        $attribute = Str::beforeLast($attribute, '_');

        if (in_array($attribute, $this->getArrayableFiles())) {
            return $attribute;
        }

        return false;
    }

    /**
     * Get all of the files values that are arrayable.
     *
     * @return array
     */
    protected function getArrayableFiles()
    {
        //ensure public property
        return property_exists($this, 'files') ? (array) $this->files : [];
    }
}
