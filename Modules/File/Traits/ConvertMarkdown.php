<?php

namespace Modules\File\Traits;

use Mo<PERSON>les\File\Jobs\ConvertNewMarkdown;
use Modules\File\Services\ConvertNewMarkdownManager;

trait ConvertMarkdown
{
    /**
     * Dispatch a job to its make convert media with markdown.
     *
     * @return void
     */
    public function queueMakeConvertMediaWithMarkdown($markdown = null)
    {
        dispatch(new ConvertNewMarkdown($this, $markdown));
    }

    /**
     * Get the convert engine for the model.
     *
     * @return mixed
     */
    public function convertNewMarkdownUsing($provider = null)
    {
        return app(ConvertNewMarkdownManager::class)->provider($provider);
    }

    /**
     * @return string
     */
    public function convertNewMarkdownUsingQueue()
    {
        return 'queue.processing.candidates';
    }
}
