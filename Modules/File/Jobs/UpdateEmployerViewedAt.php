<?php

namespace Modules\File\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Job\Repositories\Contracts\CandidateRepositoryInterface;

class UpdateEmployerViewedAt implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(private $candidate_id, private $time_viewed_at)
    {
        $this->onConnection(config('queue.ams'));
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $candidate = app(CandidateRepositoryInterface::class)->findById(
            $this->candidate_id,
            []
        );

        if (!empty($candidate)) {
            $candidate->employer_viewed_at = $this->time_viewed_at;
            $candidate->searchable();
        }
    }
}
