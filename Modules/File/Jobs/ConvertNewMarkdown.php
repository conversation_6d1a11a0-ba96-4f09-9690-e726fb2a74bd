<?php

namespace Modules\File\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\File\Entities\Media;
use Modules\File\Events\ConvertMediaWithMarkdownSuccessful;
use Symfony\Component\Process\Process;
use Telegram\Bot\Api;

class ConvertNewMarkdown implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels, Queueable;

    /**
     * @var Media
     */
    protected $media;

    /**
     * Create a new job instance.
     *
     * @return void
     * @param string|null $markdown
     */
    public function __construct(Media $media, protected $markdown = null)
    {
        $this->media = $media;
        $this->onConnection(config('queue.ams'));
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $media = $this->media;
        $markdown = $this->markdown;

        $factory = $media->convertNewMarkdownUsing($markdown)
                        ->fromSource($media)->convert();

        $media->touch();
        event(new ConvertMediaWithMarkdownSuccessful($media));

        // if ($factory->getProcess()->isSuccessful()) {

        //     $media->touch();
        //     event(new ConvertMediaWithMarkdownSuccessful($media));

        // } else {
        //     $telegram = new Api(env('TELEGRAM_BOT_TOKEN'));
        //     $telegram->sendMessage([
        //         'chat_id'   => \App::environment('production') ? '-262974614' : env('CHAT_ID'),
        //         'text'      => "ConvertMediaWithMarkdownSuccessful failed: ". $factory->getProcess()->getExitCodeText() . "\n@sonnx",
        //     ]);
        // }
    }

    /**
     * The job failed to process.
     *
     * @return void
     */
    public function failed(Exception $exception)
    {
        \Log::info('Media convert with markdown error: ' . $exception->getMessage());
    }
}
