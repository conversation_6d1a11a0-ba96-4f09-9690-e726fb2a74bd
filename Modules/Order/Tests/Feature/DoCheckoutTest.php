<?php

namespace Modules\Order\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\Order\Entities\Cart;
use Modules\Order\Entities\CartItem;
use Modules\Order\Entities\Order;
use Modules\Payment\Entities\PaymentMethod;
use Modules\Payment\Entities\PaymentVendor;
use Modules\User\Entities\User;

class DoCheckoutTest extends TestCase
{
    use RefreshDatabase;

    private function fake_cart_data_for_testing()
    {
        factory(Cart::class)->create()->each(function ($cart) {
            $cart->items()->saveMany(factory(CartItem::class, 3)->make());
        });
    }

    public function test_cart_data()
    {
        $this->fake_cart_data_for_testing();

        $this->assertDatabaseHas('carts', ['user_id' => 3158369]);
    }

    public function test_user_data()
    {
        factory(User::class)->create();

        $this->assertDatabaseHas('users', ['company_id' => 1]);
    }

    public function test_user_does_not_have_cart()
    {
        $user = factory(User::class)->create(['id' => 3158369]);
        $user->createToken('testing');

        $response = $this->actingAs($user, 'sanctum')->post('/td/v3/orders', []);

        $response->assertStatus(403);
    }

    public function test_validate_required_fields_failed()
    {
        $user = factory(User::class)->create(['id' => 3158369]);
        $user->createToken('testing');

        $this->fake_cart_data_for_testing();

        $response = $this->actingAs($user, 'sanctum')->post('/td/v3/orders', [
            'buyer_name' => '',
            'buyer_phone' => '',
            'company_tax_number' => '',
            'crm_company_id' => '',
            'company_business_name' => '',
            'company_phone' => '',
            'payment_method_id' => '',
            'item_total' => '',
            'order_total' => '',
            'discount_total' => '',
            'quantity' => '',
            'term_of_use' => '',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'message',
                'errors' => [
                    'buyer_name',
                    'buyer_phone',
                    'company_tax_number',
                    'crm_company_id',
                    'company_business_name',
                    'payment_method_id',
                    'item_total',
                    'order_total',
                    'discount_total',
                    'quantity',
                    'term_of_use'
                ]
            ]);
    }

    public function test_validate_failed()
    {
        $user = factory(User::class)->create(['id' => 3158369]);
        $user->createToken('testing');

        $this->fake_cart_data_for_testing();

        $response = $this->actingAs($user, 'sanctum')->post('/td/v3/orders', [
            'buyer_name' => 'Hr Admin',
            'buyer_phone' => '333333',
            'company_tax_number' => '123',
            'crm_company_id' => '1',
            'company_business_name' => 'Topdev',
            'company_phone' => '2222',
            'payment_method_id' => '1',
            'item_total' => 100000,
            'order_total' => 10000,
            'discount_total' => 10000,
            'quantity' => 1,
            'term_of_use' => false,
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'message',
                'errors' => [
                    'buyer_phone',
                    'company_tax_number',
                    'crm_company_id',
                    'company_phone',
                    'payment_method_id',
                    'cart',
                    'term_of_use'
                ]
            ]);
    }

    public function test_checkout_successfull()
    {
        $user = factory(User::class)->create(['id' => 3158369]);
        $user->createToken('testing');

        PaymentVendor::factory()->megapay()->make()->save();
        PaymentMethod::factory()->DC()->make()->save();

        $cart = factory(Cart::class)->create(['user_id' => $user->id]);
        $cart->each(function ($cart) {
            $cart->items()->saveMany(factory(CartItem::class, 3)->make());
        });

        $cart = Cart::whereUserId($user->id)->first();

        $response = $this->actingAs($user, 'sanctum')->post('/td/v3/orders', [
            'buyer_name' => 'Hr Admin',
            'buyer_phone' => '0312239234',
            'company_tax_number' => '0314372496',
            'crm_company_id' => '1',
            'company_business_name' => 'Topdev',
            'company_phone' => '0394949444',
            'payment_method_id' => 1,
            'item_total' => $cart->item_total,
            'order_total' => $cart->order_total,
            'discount_total' => $cart->discount_total,
            'quantity' => $cart->quantity,
            'term_of_use' => true,
        ]);

        $response->assertCreated()->assertJsonStructure([
            'error',
            'data' => [
                'merId',
                'currency',
                'amount',
                'invoiceNo',
                'goodsNm',
                'payType',
                'callBackUrl',
                'notiUrl',
                'reqDomain',
                'description',
                'merchantToken',
                'userLanguage',
                'timeStamp',
                'merTrxId',
                'windowColor',
            ],
            'message'
        ]);

        $this->assertDatabaseHas('orders', ['user_id' => $user->id]);

        $order = Order::whereUserId($user->id)->first();

        $this->assertDatabaseHas('order_items', ['order_id' => $order->id]);

        $this->assertDatabaseHas('payment_transactions', ['order_id' => $order->id]);

        $this->assertDatabaseMissing('cart_items', ['cart_id' => $cart->id]);
        $this->assertDatabaseMissing('carts', ['user_id' => $user->id]);


    }
}
