<?php

namespace Modules\Order\Tests\Feature\Http\Controllers;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Response;
use Modules\Order\Entities\CartItem;
use Modules\Order\Entities\Product;
use Modules\Order\Entities\ProductCategory;
use Modules\User\Entities\User;
use Tests\TestCase;

class CartControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private $user;
    private $product;

    private const API_ENDPOINT = 'td/v3/carts';

    public function setUp(): void
    {
        parent::setUp();

        // Create an active user
        $this->user = factory(User::class)->state('user_employer')->create();

        // Create an active and an inactive product
        $this->product = factory(Product::class)->create();

        // Authenticate the user
        $this->actingAs($this->user, 'sanctum');
    }


    public function test_authenticated()
    {
        $this->assertAuthenticated();
    }

    /**
     * @group index
     */
    public function test_api_get_cart_success()
    {
        $cart = $this->user->cart()->create();
        $totalItems = 5;
        $quantity = 2;

        factory(ProductCategory::class)->create()->each(
            fn ($category) => $category->products()->saveMany(
                factory(Product::class, $totalItems)->create()->each(
                    fn ($product)  => $cart->items()->create([
                        'product_id' => $product->id,
                        'name' => $product->name,
                        'name_vi' => $product->name,
                        'price' => $product->price,
                        'anchoring_price' => $product->anchoring_price,
                        'quantity' => $quantity
                    ])
                )
            )
        );

        $this->getJson(self::API_ENDPOINT)
            ->assertOk()
            ->assertJsonStructure(
                [
                    'error',
                    'data' => [
                        'warnings' => [
                            'status',
                            'price',
                            'anchoring_price',
                        ],
                        'uuid',
                        'items' => [
                            [
                                'product_id',
                                'name',
                                'translables' => [
                                    'en' => [
                                        'name'
                                    ],
                                    'vi' => [
                                        'name'
                                    ]
                                ],
                                'quantity',
                                'price',
                                'total_price',
                                'anchoring_price',
                                'total_anchoring_price',
                                'category_code',
                            ]
                        ],
                        'promotions' => [],
                        'quantity',
                        'price_total',
                        'order_total',
                        'discount_total',
                        'tax_name',
                        'total_tax',
                        'items_count'
                    ],
                    'message'
                ]
            )
            ->assertJsonCount($totalItems, 'data.items')
            ->assertJson([
                'data' => [
                    'uuid' => $cart->uuid,
                    'quantity' => $totalItems * $quantity,
                    'items_count' => $totalItems
                ]
            ]);


        $cartItem = $cart->items()->first();
        $product = $cartItem->product;

        // Change the product's price
        $product->increment('price', 5000);

        $this->getJson(self::API_ENDPOINT)
            ->assertOk()
            ->assertJsonCount($totalItems, 'data.items')
            ->assertJson([
                'data' => [
                    'warnings' => [
                        'status' => [],
                        'price' => [$cartItem->name],
                        'anchoring_price' => [],
                    ],
                    'uuid' => $cart->uuid,
                    'quantity' => $totalItems * $quantity,
                    'items_count' => $totalItems
                ]
            ]);

        // Change the product's anchoring price
        $product->increment('anchoring_price', 5000);

        $this->getJson(self::API_ENDPOINT)
            ->assertOk()
            ->assertJsonCount($totalItems, 'data.items')
            ->assertJson([
                'data' => [
                    'warnings' => [
                        'status' => [],
                        'price' => [],
                        'anchoring_price' => [$cartItem->name],
                    ],
                    'uuid' => $cart->uuid,
                    'quantity' => $totalItems * $quantity,
                    'items_count' => $totalItems
                ]
            ]);

        // Set the product's status to inactive
        $product->update(['is_active' => Product::IS_INACTIVE]);

        $this->getJson(self::API_ENDPOINT)
            ->assertOk()
            ->assertJsonCount($totalItems - 1, 'data.items')
            ->assertJson([
                'data' => [
                    'warnings' => [
                        'status' => [$cartItem->name],
                        'price' => [],
                        'anchoring_price' => [],
                    ],
                    'uuid' => $cart->uuid,
                    'quantity' => ($totalItems * $quantity) - $cartItem->quantity,
                    'items_count' => $totalItems - 1
                ]
            ]);
    }

    /**
     * @group index
     */
    public function test_api_get_cart_empty()
    {
        // Test case: User does't have a cart
        $this->getJson(self::API_ENDPOINT)
            ->assertOk()
            ->assertJson([
                'error' => true,
                'data' => []
            ]);
    }

    /**
     * @group store
     */
    public function test_api_add_to_cart_pass_validation()
    {
        $this->httpRequest('postJson', Response::HTTP_OK, [
            'product_id' => $this->product->id,
            'quantity' => random_int(1, 10)
        ]);
    }

    /**
     * @group store
     */
    public function test_api_add_to_cart_failed_validation()
    {
        $productInactive = factory(Product::class)->create(['is_active' => Product::IS_INACTIVE]);
        $product = $this->product;

        // Test case: Product is inactive
        $this->validateStoreRequestFailed(['product_id' => $productInactive->id], [
            'product_id' => ['The product is not valid.']
        ]);

        // Test case: Not passing product_id
        $this->validateStoreRequestFailed([], [
            'product_id' => [trans('validation.required', ['attribute' => 'product id'])]
        ]);

        // Test case: Quantity is less than 1
        $this->validateStoreRequestFailed(['product_id' => $product->id, 'quantity' => 0], [
            'quantity' => [trans('validation.min.numeric', ['attribute' => 'quantity', 'min' => 1])]
        ]);

        // Test case: Quantity is not an integer
        $this->validateStoreRequestFailed(['product_id' => $product->id, 'quantity' => 'High'], [
            'quantity' => [trans('validation.integer', ['attribute' => 'quantity'])]
        ]);
    }

    /**
     * @group store
     */
    public function test_api_add_to_cart_success()
    {
        $user = $this->user;
        $product = $this->product;

        $this->postJson(self::API_ENDPOINT, [
            'product_id' => $product->id,
            'quantity' => random_int(1, 10)
        ])
            ->assertOk()
            ->assertJson([
                'error' => false,
                'data' => [
                    'uuid' => $user->cart->uuid
                ]
            ]);

        $this->assertDatabaseHas('cart_items', [
            'cart_id' => $user->cart->id,
            'product_id' => $product->id,
        ]);
    }

    /**
     * @group store
     */
    public function test_api_add_to_cart_failed()
    {
        $product = $this->product;
        $productId = $product->id;

        $this->httpRequest('postJson', Response::HTTP_OK, [
            'product_id' => $productId,
        ]);

        $this->httpRequest('postJson', Response::HTTP_BAD_REQUEST, [
            'product_id' => $productId,
        ], [
            'error' => true,
            'data' => null
        ]);
    }

    /**
     * @group update
     */
    public function test_update_quantity_of_product_cart_pass_validation()
    {
        $productId = $this->product->id;
        $quantity = 1;

        // Add new product to cart
        $this->generateCartWithProduct($quantity);

        // Test case: Update by quantity
        $quantity += 5;
        $this->validateUpdateRequestSuccess(['product_id' => $productId, 'quantity' => $quantity]);

        // Test case: Update by type is increment
        $this->validateUpdateRequestSuccess(['product_id' => $productId, 'type' => 'increment']);

        // Test case: Update by type is decrement
        $this->validateUpdateRequestSuccess(['product_id' => $productId, 'type' => 'decrement']);
    }

    /**
     * @group update
     */
    public function test_update_quantity_of_product_cart_failed_validation()
    {
        $productInactive = factory(Product::class)->create(['is_active' => Product::IS_INACTIVE]);
        $product = $this->product;

        $this->generateCartWithProduct(CartItem::MAXIMUM_QUANTITY);

        // Test case: Product is inactive
        $this->validateUpdateRequestFailed(['product_id' => $productInactive->id], [
            'product_id' => ['The product is not valid.']
        ]);

        // Test case: Not passing product_id
        $this->validateUpdateRequestFailed([], [
            'product_id' => [trans('validation.required', ['attribute' => 'product id'])]
        ]);

        // Test case: Quantity is less than 1
        $this->validateUpdateRequestFailed(['product_id' => $product->id, 'quantity' => 0], [
            'quantity' => [trans('validation.min.numeric', ['attribute' => 'quantity', 'min' => 1])]
        ]);

        // Test case: Quantity is greater than maximum
        $this->validateUpdateRequestFailed(['product_id' => $product->id, 'quantity' => CartItem::MAXIMUM_QUANTITY + 1], [
            'quantity' => [trans('validation.max.numeric', ['attribute' => 'quantity', 'max' => CartItem::MAXIMUM_QUANTITY])],
            'quantity_max' => ['Maximum number of products.']
        ]);

        // Test case: Type is increment with already maximum quantity
        $this->validateUpdateRequestFailed(['product_id' => $product->id, 'type' => 'increment'], [
            'quantity_max' => ['Maximum number of products.']
        ]);

        // Test case: Quantity is not an integer
        $this->validateUpdateRequestFailed(['product_id' => $product->id, 'quantity' => 'High'], [
            'quantity' => [trans('validation.integer', ['attribute' => 'quantity'])]
        ]);

        // Test case: Quantity missing
        $this->validateUpdateRequestFailed(['product_id' => $product->id]);

        // Test case: Type is number
        $this->validateUpdateRequestFailed(['product_id' => $product->id, 'type' => 9999], []);

        // Test case: Type is invalid data
        $this->validateUpdateRequestFailed(['product_id' => $product->id, 'type' => 'Hello'], []);
    }

    /**
     * @group update
     */
    public function test_update_quantity_of_product_cart_success()
    {
        $productId = $this->product->id;
        $quantity = 1;

        // Add new product to cart
        $this->generateCartWithProduct($quantity);

        // Update by quantity
        $this->testUpdateByQuantity($productId, $quantity += 5);

        // Update by type increment
        $this->testUpdateByType($productId, 'increment', $quantity += 1);

        // Update by type decrement
        $this->testUpdateByType($productId, 'decrement', $quantity -= 1);
    }

    /**
     * @group update
     */
    public function test_update_quantity_of_product_cart_failed()
    {
        $productId = $this->product->id;

        $this->generateCartWithProduct();

        $requestData = [
            'product_id' => $productId,
            'type' => 'decrement',
        ];

        $this->httpRequest('putJson', Response::HTTP_BAD_REQUEST, $requestData, [
            'error' => true,
            'data' => null
        ]);
    }

    /**
     * @group delete
     */
    public function test_delete_product_in_cart_success()
    {
        $productId = $this->product->id;
        $quantity = 2;

        $this->generateCartWithProduct($quantity);

        $cart = $this->user->cart;
        $cartId = $cart->id;
        $cartUuid = $cart->uuid;

        $this->assertDatabaseHas('cart_items', [
            'cart_id' => $cartId,
            'product_id' => $productId,
            'quantity' => $quantity
        ]);

        $this->assertDatabaseHas('carts', [
            'uuid' => $cartUuid,
            'quantity' => $quantity
        ]);

        // Delete the product
        $this->deleteJson(self::API_ENDPOINT . '/' . $productId)->assertStatus(Response::HTTP_OK);

        $this->assertDatabaseMissing('cart_items', [
            'cart_id' => $cartId,
            'product_id' => $productId,
        ]);

        $this->assertDatabaseHas('carts', [
            'uuid' => $cartUuid,
            'quantity' => 0
        ]);
    }

    /**
     * @group delete
     */
    public function test_delete_product_in_cart_failed()
    {
        $user = $this->user;

        // Delete with user does't have a cart
        $this->httpRequest('deleteJson', Response::HTTP_BAD_REQUEST, [], [], self::API_ENDPOINT . '/' . 999);

        // Delete with product_id not existing in cart
        $user->cart()->create();
        $this->httpRequest('deleteJson', Response::HTTP_BAD_REQUEST, [], [], self::API_ENDPOINT . '/' . 999);
    }

    private function validateStoreRequest(int $statusCode, array $requestData, array $expectedErrors = [])
    {
        $this->httpRequest('postJson', $statusCode, $requestData, $expectedErrors);
    }
    private function validateStoreRequestFailed(array $requestData, array $expectedErrors = [])
    {
        $this->validateStoreRequest(Response::HTTP_UNPROCESSABLE_ENTITY, $requestData, ['errors' => $expectedErrors]);
    }

    private function validateUpdateRequest(int $statusCode, array $requestData, array $expectedData = [])
    {
        $this->httpRequest('putJson', $statusCode, $requestData, $expectedData);
    }

    private function validateUpdateRequestFailed(array $requestData, array $expectedErrors = [])
    {
        $this->validateUpdateRequest(Response::HTTP_UNPROCESSABLE_ENTITY, $requestData, ['errors' => $expectedErrors]);
    }

    private function validateUpdateRequestSuccess(array $requestData, array $expectedData = [])
    {
        $this->validateUpdateRequest(Response::HTTP_OK, $requestData, $expectedData);
    }

    private function testUpdateByQuantity(int $productId, int $quantity)
    {
        $cartUuid = $this->user->cart->uuid;
        $requestData = ['product_id' => $productId, 'quantity' => $quantity];
        $expectedData = [
            'error' => false,
            'data' => [
                'uuid' => $cartUuid
            ]
        ];
        $this->httpRequest('putJson', Response::HTTP_OK, $requestData, $expectedData);

        $this->assertDatabaseHas('cart_items', [
            'product_id' => $productId,
            'quantity' => $quantity,
        ]);
        $this->assertDatabaseHas('carts', [
            'uuid' => $cartUuid,
            'quantity' => $quantity
        ]);
    }
    private function testUpdateByType(int $productId, string $type, int $quantityExpected)
    {
        $cartUuid = $this->user->cart->uuid;
        $requestData = ['product_id' => $productId, 'type' => $type];
        $expectedData = [
            'error' => false,
            'data' => [
                'uuid' => $cartUuid
            ]
        ];

        $this->httpRequest('putJson', Response::HTTP_OK, $requestData, $expectedData);

        $this->assertDatabaseHas('cart_items', [
            'product_id' => $productId,
            'quantity' => $quantityExpected,
        ]);

        $this->assertDatabaseHas('carts', [
            'uuid' => $cartUuid,
            'quantity' => $quantityExpected
        ]);
    }

    /**
     * Make a request to call requests HTTP.
     * @return void
     */
    private function httpRequest(string $method, int $statusCode, array $requestData = [], array $expectedData = [], string $uri = self::API_ENDPOINT)
    {
        $response = $this->{$method}($uri, $requestData)
            ->assertStatus($statusCode);

        if ($expectedData) {
            $response->assertJson($expectedData);
        }
    }

    private function generateCartWithProduct(int $quantity = 1)
    {
        $product = $this->product;

        $this->user->cart()->create()->items()->create([
            'product_id' => $product->id,
            'name' => $product->name,
            'price' => $product->price,
            'anchoring_price' => $product->anchoring_price,
            'quantity' => $quantity
        ]);
    }
}
