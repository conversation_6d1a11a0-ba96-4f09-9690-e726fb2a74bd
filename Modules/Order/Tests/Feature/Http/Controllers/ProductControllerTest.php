<?php

namespace Modules\Order\Tests\Feature\Http\Controllers;

use Tests\TestCase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Response;
use Modules\Order\Entities\Product;

class ProductControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private $product;

    private const API_ENDPOINT = '/td/v3/products/';

    public function setUp(): void
    {
        parent::setUp();

        $this->product = factory(Product::class)->create([
            'id' => 1,
            'vi' => [
                'name' => 'Gói Basic',
                'description' => 'Mô tả Gói Basic',
            ],
            'en' => [
                'name' => 'Basic Package',
                'description' => 'Basic Package description',
            ],
            'is_active' => 1,
            'price' => 10000000,
            'anchoring_price' => 11000000
        ]);
    }

    public function test_has_a_product_data()
    {
        $this->assertDatabaseHas('products', [
            'id' => $this->product->id,
            'name' => null,
            'is_active' => $this->product->is_active,
            'price' => $this->product->price,
            'anchoring_price' => $this->product->anchoring_price
        ]);

        $this->assertDatabaseHas('product_translations', [
            'product_id' => $this->product->id,
            'locale' => 'en',
            'name' => 'Basic Package',
            'description' => 'Basic Package description',
        ]);

        $this->assertDatabaseHas('product_translations', [
            'product_id' => $this->product->id,
            'locale' => 'vi',
            'name' => 'Gói Basic',
            'description' => 'Mô tả Gói Basic',
        ]);

        $this->assertDatabaseMissing('products', [
            'id' => 2
        ]);
    }

    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function test_get_product_detail_failed()
    {
        $response = $this->get(self::API_ENDPOINT . '2');

        $response->assertStatus(Response::HTTP_BAD_REQUEST);
    }

    public function test_get_product_detail_successful()
    {
        $response = $this->get(self::API_ENDPOINT . $this->product->id);

        $response->assertStatus(Response::HTTP_OK);
        $response->assertExactJson([
            'error' => false,
            'message' => "Ok",
            'data' => [
                'name' => $this->product->name,
                'translables' => [
                    'vi' => [
                        'name' => 'Gói Basic',
                        'description' => 'Mô tả Gói Basic',
                    ],
                    'en' => [
                        'name' => 'Basic Package',
                        'description' => 'Basic Package description',
                    ],
                ],
                'is_active' => true,
                'price' => $this->product->price,
                'anchoring_price' => $this->product->anchoring_price
            ]
        ]);
    }
}
