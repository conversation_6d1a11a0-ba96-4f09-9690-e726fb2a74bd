<?php

namespace Modules\Order\Tests\Feature\Http\Controllers;

use App\Helpers\CrmApi;
use Tests\TestCase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\Company\Entities\Company;
use Modules\Order\Entities\Cart;
use Modules\Order\Entities\CartItem;
use Modules\Order\Entities\Order;
use Modules\Payment\Entities\PaymentMethod;
use Modules\Payment\Entities\PaymentTransactions;
use Modules\Payment\Entities\PaymentVendor;
use Modules\Payment\Managers\Payment\PaymentManager;
use Modules\User\Entities\User;

class CheckoutControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private $user;
    private $cart;
    private $order;

    private const API_ENDPOINT = '/td/v3/orders';

    public function setUp(): void
    {
        parent::setUp();

        // Create an active user
        $this->user = factory(User::class)->state('user_employer')->create([
            'company_id' => 1
        ]);

        // fake Payment Method, Payment Vendor
        PaymentVendor::factory()->megapay()->create();
        $paymentMethod = PaymentMethod::factory()->DC()->create();

        // Create a cart
        $this->cart = factory(Cart::class)->create(['user_id' => $this->user->id]);
        $this->cart->each(function ($cart) {
            $cart->items()->saveMany(factory(CartItem::class, 3)->make());
        });

        // fake order
        $this->order = Order::factory()->create(['user_id' => $this->user->id]);

        PaymentManager::resolveDriverByMethod($paymentMethod->code)
            ->createPayment([
                'payment_method_id' => $paymentMethod->id,
                'order_id' => $this->order->id,
                'amount' => $this->order->order_total,
                'request_type' => $paymentMethod->code,
                'order_code' => $this->order->code,
                'order_info' => $this->order->order_info,
            ]);

        // fake last payment transaction
        $paymentTrans = PaymentTransactions::whereOrderId($this->order->id)->first();

        Order::whereUserId($this->user->id)
            ->whereId($this->order->id)->update(['last_payment_transaction_id' => $paymentTrans->id]);

        // Authenticate the user
        $this->actingAs($this->user, 'sanctum');
    }

    public function test_order_result_is_not_exist()
    {
        $orderCode = 'TOPDEV-xxxxxxx';
        $response = $this->get(self::API_ENDPOINT . '/result/' . $orderCode);

        $response->assertStatus(500)
            ->assertJson([
                'error' => false,
                'data' => null,
                'message' => 'No query results for model [Modules\\Order\\Entities\\Order].'
            ]);
    }

    public function test_get_order_detail_success()
    {
        $response = $this->get(self::API_ENDPOINT . '/result/' . $this->order->code);

        $order = $this->order->refresh();

        $response->assertOk()->assertSeeText($order->code);
        $response->assertJson([
            'error' => false,
            'data' => [
                'code' => $order->code,
                'created_at' => $order->created_at->format('H:i:s Y-m-d'),
                'paid_at' => null,
                'payment_method' => $order->payment_method,
                'status' => $order->order_status,
                'amount' => $order->amount
            ],
            'message' => 'Ok'
        ]);
    }

    public function test_user_resume_permission_throw_exception()
    {
        $user = factory(User::class)->state('user_employer')->create([
            'id' => 1123123,
            'username' => '1123123',
            'company_id' => 1,
            'type' => User::RESUME_TYPE
        ]);

        $this->fakeCrmRequest([
            'data' => [],
            'success' => true
        ]);

        $response = $this->actingAs($user, 'sanctum')->get(self::API_ENDPOINT . '/companies');

        $response->assertStatus(500);
        $response->assertSeeText('Permission denied');
    }

    public function test_checkout_controller_authenticated()
    {
        $this->assertAuthenticated();
    }

    public function test_the_company_is_not_existed()
    {
        $this->fakeCrmRequest([
            'data' => [],
            'success' => true
        ]);
        $response = $this->get(self::API_ENDPOINT . '/companies');

        $response->assertStatus(500);
    }

    private function fakeCrmRequest($mockResponse = [])
    {
        $this->mock(CrmApi::class, function (\Mockery\MockInterface $mock) use ($mockResponse) {
            $mock->shouldReceive('getCrmCompanies')
                ->andReturn($mockResponse);
        })->makePartial();
    }

    public function test_get_crm_companies_empty_data()
    {
        factory(Company::class)->state('not-mapping-yet')->create();
        $this->fakeCrmRequest([
            'data' => [],
            'success' => true
        ]);

        $response = $this->get(self::API_ENDPOINT . '/companies');

        $response->assertOk()->assertSeeText('No Tax Number Found.')
            ->assertExactJson([
                'error' => false,
                'data' => [],
                'message' => 'No Tax Number Found.'
            ]);
    }

    public function test_get_crm_companies_data_successfull_with_data()
    {
        factory(Company::class)->state('already-mapping')->make()->save();
        $this->fakeCrmRequest([
            'data' => [
                [
                    'id' => 1,
                    'business_name' => '1W1S',
                    'tax_number' => '0314372496',
                    'phone_number' => ''
                ]
            ],
            'success' => true
        ]);

        $response = $this->get(self::API_ENDPOINT . '/companies');

        $response->assertOk()->assertJsonStructure([
            'error',
            'message',
            'data' => [
                '*' => [
                    'id',
                    'business_name',
                    'tax_number',
                    'phone_number'
                ]
            ]
        ])
            ->assertSee('0314372496')
            ->assertSeeInOrder([
                'id' => 1,
                'business_name' => '1W1S',
                'tax_number' => '0314372496',
                'phone_number' => ''
            ]);
    }

    public function test_get_list_methods_successfull()
    {
        $response = $this->get(self::API_ENDPOINT . '/methods');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'error',
            'message',
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'code',
                    'type',
                    'payment_vendor'
                ]
            ]
        ]);
        $response->assertExactJson([
            'error' => false,
            'data' => [
                [
                    'id' => 1,
                    'name' => 'Thẻ ATM nội địa',
                    'code' => 'DC',
                    'type' => 'atm',
                    'payment_vendor' => 'megapay'
                ]
            ],
            'message' => 'Ok'
        ]);
    }

    public function test_get_methods_empty_data()
    {
        PaymentMethod::truncate();
        PaymentVendor::truncate();

        $response = $this->get(self::API_ENDPOINT . '/methods');

        $response->assertStatus(200);
        $response->assertExactJson([
            'error' => false,
            'data' => null,
            'message' => 'No Payment Method Found.'
        ]);
    }
}
