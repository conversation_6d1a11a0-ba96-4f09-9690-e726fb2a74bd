<?php

namespace Modules\Order\Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\Order\Entities\Product;
use Modules\Order\Entities\ProductBenefit;
use Modules\Order\Entities\ProductCategory;
use Tests\TestCase;

class ApiGetListProductsTest extends TestCase
{
    use RefreshDatabase;

    public function fake_database()
    {
        /* @phpstan-ignore-next-line */
        factory(ProductCategory::class)->create()->each(function ($category) {
            /* @phpstan-ignore-next-line */
            $category->products()->saveMany(factory(Product::class, 30)->make())->each(function ($product) {
                /* @phpstan-ignore-next-line */
                $product->benefits()->saveMany(factory(ProductBenefit::class, 7)->make());
            });
        });

        /* @phpstan-ignore-next-line */
        factory(ProductCategory::class)->make([
            'code' => 'credit',
        ])->each(function ($category) {
            /* @phpstan-ignore-next-line */
            $category->products()->saveMany(factory(Product::class, 3)->make())->each(function ($product) {
                /* @phpstan-ignore-next-line */
                $product->benefits()->saveMany(factory(ProductBenefit::class, 7)->make());
            });
        });

        /* @phpstan-ignore-next-line */
        factory(ProductCategory::class)->make([
            'code' => 'reward',
        ])->each(function ($category) {
            /* @phpstan-ignore-next-line */
            $category->products()->saveMany(factory(Product::class, 30)->make());
        });
    }

    public function test_get_all_products()
    {
        $response = $this->get('/td/v3/products?width=100&height=100');

        $response->assertJsonStructure([
            'error',
            'data' => [
                '*' => [
                    "id",
                    "name",
                    "description",
                    "category_code",
                    "benefits" => [
                        "*" => [
                            "id",
                            "name",
                            "order"
                        ]
                    ],
                    "price",
                    "anchoring_price",
                    "media_url"
                ]
            ],
            'message'
        ])
            ->assertStatus(200);
    }

    public function test_get_all_products_failed_validation()
    {
        $this->fake_database();

        $response = $this->get('/td/v3/products');

        $response->assertStatus(422)->assertExactJson([
            'errors' => [
                'width' => ['The width field is required.'],
                'height' => ['The height field is required.']
            ],
            'message' => 'The width field is required. (and 1 more error)'
        ]);
    }

    public function test_get_all_products_with_category()
    {
        $this->fake_database();

        $response = $this->get('/td/v3/products?category_code=package&width=100&height=100');

        $response->assertStatus(200)->assertDontSee('reward');
    }

    public function test_get_all_products_with_invalid_category()
    {
        $response = $this->get('/td/v3/products?category_code=invalid&width=100&height=100');

        $response->assertStatus(422)->assertExactJson([
            'errors' => [
                'category_code' => ['The category is not existed.']
            ],
            'message' => 'The category is not existed.'
        ]);
    }

    public function test_get_all_products_with_pagination()
    {
        $this->fake_database();

        $response = $this->get('/td/v3/products?width=100&height=100');

        $response->assertStatus(200)->assertJsonCount(10, 'data');
    }

    public function test_get_all_products_without_pagination()
    {
        $this->fake_database();

        $response = $this->get('/td/v3/products?page_size=-1&width=100&height=100');

        $response->assertStatus(200);
    }
}
