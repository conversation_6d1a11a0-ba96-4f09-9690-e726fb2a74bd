<?php

namespace Modules\Order\States\Order\Status;

use App\Jobs\SendOrderConfirmationEmail;
use Illuminate\Http\Request;
use Modules\Order\Jobs\ProcessCreateCrmInvoice;
use Modules\Order\Transformers\GetOrderItemResource;
use Modules\SearchCandidate\Entities\CompanySearchPackage;

class Completed extends OrderStatus
{
    public static $name = 'COMPLETED';

    public function handle()
    {
        $this->sendEmailConfirmOrder();
    }

    /**
     * Send order confirmation email
     */
    public function sendEmailConfirmOrder()
    {
        /**
         * @var \Modules\Order\Entities\Order $model
         */
        $model = $this->getModel();
        $user = $model->user;
        $companyName = $user->company->display_name ?? '';

        $data = [
            'company_name' => $companyName,
            'email' => $model->buyer_email,
            'order' => [
                'code' => $model->code,
                'paid_at' => $model->paid_at ? $model->paid_at->format('H:i:s - d/m/Y') : null,
                'payment_method' => $model->payment_method_name,
                'item_total' => $this->formatMoney($model->item_total),
                'discount_total' => $this->formatMoney($model->discount_total),
                'tax_total' => $this->formatMoney($model->tax_total),
                'tax_name' => $model->tax_name,
                'order_total' => $this->formatMoney($model->order_total),
            ],
            'company' => [
                'name' => $companyName,
                'business_name' => $model->company_business_name,
                'tax_number' => $model->company_tax_number,
                'phone' => $model->company_phone
            ],
            'buyer' => [
                'name' => $model->buyer_name,
                'email' => $model->buyer_email,
                'phone' => $model->buyer_phone
            ],
            'items' => GetOrderItemResource::collection($model->items)->toArray(new Request())
        ];

        dispatch(new SendOrderConfirmationEmail($data));

        // add credit for company when paid
        CompanySearchPackage::addCreditFromOrder($model->id);

        // Dispatch Job to create crm's invoice automatically
        ProcessCreateCrmInvoice::dispatch($model->id);

        // Approve employer account when paid success
        $user->approveEmployerAccount($model->id);
    }

    /**
     * @param $price
     */
    private function formatMoney($price)
    {
        return number_format($price, 0, ',', '.');
    }
}
