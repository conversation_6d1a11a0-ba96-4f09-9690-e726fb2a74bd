<?php

namespace Modules\Order\States\Order\Status;

use Spatie\ModelStates\Exceptions\InvalidConfig;
use Spatie\ModelStates\State;
use Spatie\ModelStates\StateConfig;

abstract class OrderStatus extends State
{
    abstract public function handle();

    /**
     * @throws InvalidConfig
     */
    public static function config(): StateConfig
    {
        return parent::config()
            ->default(AwaitingPayment::class)
            ->allowTransition(AwaitingPayment::class, Pending::class)
            ->allowTransition(AwaitingPayment::class, Completed::class)
            ->allowTransition(AwaitingPayment::class, Cancelled::class)
            ->allowTransition(AwaitingPayment::class, Error::class)
            ->allowTransition(Pending::class, Completed::class)
            ->allowTransition(Pending::class, Cancelled::class)
            ->allowTransition(Pending::class, Error::class)
            ->allowTransition(Completed::class, Refunded::class)
            ->allowTransition(Refunded::class, Error::class);
    }
}
