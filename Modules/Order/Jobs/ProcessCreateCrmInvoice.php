<?php

namespace Modules\Order\Jobs;

use App\Helpers\CrmApi;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Modules\Order\Entities\Order;
use Modules\Order\Entities\ProductCategory;
use Modules\SearchCandidate\Entities\CompanySearchPackage;

class ProcessCreateCrmInvoice implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private int $orderId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(int $orderId)
    {
        $this->orderId = $orderId;
    }

    public function getOrderId()
    {
        return $this->orderId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $orderId = $this->getOrderId();
        $order = Order::select('id', 'code', 'user_id', 'buyer_email', 'buyer_phone', 'buyer_name', 'crm_company_id', 'created_at')
            ->with([
                'items:order_id,product_id,quantity,price',
                'items.product:id,product_category_id,taxonomy_id',
                'items.product.category:id,code',
            ])
            ->whereId($orderId)
            ->isCompleted()
            ->firstOrFail();

        $companyId = $order->user->company_id ?? null;

        $invoiceData = [
            'ams_order_id' => $orderId,
            'ams_order_code' => $order->code,
            'crm_company_id' => $order->crm_company_id,
            'ams_company_id' => $order->user->company_id ?? null,
            'paid_at' => $order->paid_at ? $order->paid_at->format('Y-m-d H:i:s') : $order->created_at->format('Y-m-d H:i:s'),
            'fullname' => $order->buyer_name,
            'email' => $order->buyer_email,
            'phone' => $order->buyer_phone,
            'products' => $order->items->map(function ($orderItem) use ($orderId, $companyId) {
                $type = $orderItem->product->category->code;
                $searchPackageIds = $type == ProductCategory::CODE_CREDIT ? CompanySearchPackage::where([
                    'order_id' => $orderId,
                    'company_id' => $companyId,
                    'search_package_id' => $orderItem->product_id,
                ])->pluck('id')->toArray() : [];
                return [
                        'id' => $orderItem->product_id,
                        'type' => $orderItem->product->category->code,
                        'company_search_package_ids' => $searchPackageIds,
                        'taxonomy_id' => $orderItem->product->taxonomy_id,
                        'quantity' => $orderItem->quantity,
                        'unit_price' => $orderItem->price
                    ];
                })->toArray(),
        ];

        $response = app(CrmApi::class)->createCrmInvoice($invoiceData);
        if (isset($response['success']) && $response['success']) {
            Order::whereId($orderId)->update(['crm_invoice_id' => $response['data']['crm_invoice_id']]);
        } else {
            throw new \Exception($response['message'] ?? 'ProcessCreateCrmInvoice error');
        }
    }
}
