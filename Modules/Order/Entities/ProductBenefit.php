<?php

namespace Modules\Order\Entities;

use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Translatable;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;

/**
 * Modules\Order\Entities\ProductBenefit
 *
 * @property int $id
 * @property int $product_id
 * @property string $name
 * @property int $order
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit whereOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit whereUpdatedAt($value)
 * @mixin \Eloquent
 * @property-read \Modules\Order\Entities\ProductBenefitTranslation|null $translation
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\Order\Entities\ProductBenefitTranslation[] $translations
 * @property-read int|null $translations_count
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit listsTranslations(string $translationField)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit notTranslatedIn(?string $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit orWhereTranslation(string $translationField, $value, ?string $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit orWhereTranslationLike(string $translationField, $value, ?string $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit orderByTranslation(string $translationField, string $sortMethod = 'asc')
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit translated()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit translatedIn(?string $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit whereTranslation(string $translationField, $value, ?string $locale = null, string $method = 'whereHas', string $operator = '=')
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit whereTranslationLike(string $translationField, $value, ?string $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefit withTranslation()
 */
class ProductBenefit extends Model implements TranslatableContract
{
    use Translatable;
    public $table = 'product_benefits';

    protected $fillable = [
        'product_id', 'name', 'order'
    ];

    public $hidden = ['product_id'];

    /**
     * Transted attributes
     *
     * @var string[]
     */
    public $translatedAttributes = ['name'];

    /**
     * Set specific foreign key
     *
     * @var string
     */
    protected $translationForeignKey = 'product_benefit_id';

    protected $translationModel = ProductBenefitTranslation::class;
}
