<?php

namespace Modules\Order\Entities;

use Illuminate\Database\Eloquent\Model;

/**
 * Modules\Order\Entities\ProductCategory
 *
 * @property int $id
 * @property string $name
 * @property string $code
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ProductCategory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductCategory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductCategory query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductCategory whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductCategory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductCategory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductCategory whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductCategory whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class ProductCategory extends Model
{
    public $table = 'product_categories';

    protected $fillable = ['name', 'code'];

    const CODE_PACKAGE = 'package';

    const CODE_CREDIT = 'credit';

    const CODE_REWARD = 'reward';

    public function products()
    {
        return $this->hasMany(Product::class, 'product_category_id');
    }

    public function scopeIsCredit($query)
    {
        $query->whereCode(self::CODE_CREDIT);
    }
}
