<?php

namespace Modules\Order\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Order\Entities\Cart;
use Modules\Order\Entities\Product;

/**
 * Modules\Order\Entities\CartItem
 *
 * @property int $id
 * @property int $cart_id
 * @property int $product_id
 * @property string $name
 * @property int $quantity
 * @property int $price
 * @property int $total_price
 * @property int $anchoring_price
 * @property int $total_anchoring_price
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Cart $cart
 * @property-read Product|null $product
 * @method static \Illuminate\Database\Eloquent\Builder|CartItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CartItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CartItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|CartItem whereCartId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartItem whereDiscountPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartItem whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartItem wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartItem whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartItem whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartItem whereTotalDiscountPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartItem whereTotalPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartItem whereUpdatedAt($value)
 * @mixin \Eloquent
 * @property string|null $name_vi
 * @method static \Illuminate\Database\Eloquent\Builder|CartItem whereAnchoringPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartItem whereNameVi($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartItem whereTotalAnchoringPrice($value)
 */
class CartItem extends Model
{
    public const MAXIMUM_QUANTITY = 20;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'product_id',
        'name',
        'name_vi',
        'quantity',
        'price',
        'total_price',
        'anchoring_price',
        'total_anchoring_price'
    ];

    public function calculateTotalPrice(): int
    {
        return $this->price * $this->quantity;
    }

    public function calculateTotalAnchoringPrice(): int
    {
        return $this->anchoring_price * $this->quantity;
    }

    public function calculateAndSetTheTotal()
    {
        $this->total_price = $this->calculateTotalPrice();
        $this->total_anchoring_price = $this->calculateTotalAnchoringPrice();

        return $this;
    }

    public function calculateAndSetTheTotalWithLatestPrice()
    {
        $this->price = $this->product->price;
        $this->anchoring_price = $this->product->anchoring_price;

        return $this->calculateAndSetTheTotal();
    }

    public function updateQuantity($type, $quantity): bool
    {
        if ($type == 'decrement' && !$this->canDecrementQuantity()) {
            return false;
        }

        if ($quantity) {
            $this->quantity = $quantity;
        } elseif ($type == 'increment') {
            $this->quantity++;
        } elseif ($type == 'decrement') {
            $this->quantity--;
        }

        return $this->save();
    }

    public function canDecrementQuantity(): bool
    {
        return $this->quantity > 1;
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function cart(): BelongsTo
    {
        return $this->belongsTo(Cart::class);
    }
}
