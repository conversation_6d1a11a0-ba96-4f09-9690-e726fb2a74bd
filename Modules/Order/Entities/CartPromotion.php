<?php

namespace Modules\Order\Entities;

use Illuminate\Database\Eloquent\Model;

/**
 * Modules\Order\Entities\CartPromotion
 *
 * @property int $id
 * @property int $cart_id
 * @property int $promotion_id
 * @property string $promotion_code
 * @property int $discount_total
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|CartPromotion newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CartPromotion newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CartPromotion query()
 * @method static \Illuminate\Database\Eloquent\Builder|CartPromotion whereCartId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartPromotion whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartPromotion whereDiscountTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartPromotion whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartPromotion wherePromotionCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartPromotion wherePromotionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CartPromotion whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class CartPromotion extends Model
{
    public $table = 'cart_promotions';
    
    protected $fillable = [];
}
