<?php

namespace Modules\Order\Entities;

use Illuminate\Database\Eloquent\Model;

/**
 * Modules\Order\Entities\OrderItem
 *
 * @property int $id
 * @property int $order_id
 * @property int $product_id
 * @property string $name
 * @property int $quantity
 * @property int $price
 * @property int $total_price
 * @property int $anchoring_price
 * @property int $total_anchoring_price
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|OrderItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderItem whereAnchoringPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderItem whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderItem whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderItem wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderItem whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderItem whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderItem whereTotalAnchoringPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderItem whereTotalPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderItem whereUpdatedAt($value)
 * @mixin \Eloquent
 * @property string|null $name_vi
 * @property-read \Modules\Order\Entities\Product $product
 * @method static \Illuminate\Database\Eloquent\Builder|OrderItem whereNameVi($value)
 */
class OrderItem extends Model
{
    protected $fillable = [
        'id',
        'order_id',
        'product_id',
        'name',
        'name_vi',
        'quantity',
        'price',
        'total_price',
        'anchoring_price',
        'total_anchoring_price',
    ];

    /**
     * @param $price
     */
    public function formatMoney($price)
    {
        return number_format($price, 0, ',', '.');
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
