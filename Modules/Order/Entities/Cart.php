<?php

namespace Modules\Order\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Order\Entities\CartItem;
use Ramsey\Uuid\Uuid;

/**
 * Modules\Order\Entities\Cart
 *
 * @property int $id
 * @property string $uuid
 * @property int $user_id
 * @property int $quantity
 * @property int $item_total
 * @property int $order_total
 * @property int $discount_total
 * @property string|null $tax_name
 * @property int $tax_total
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|CartItem[] $items
 * @property-read int|null $items_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\Order\Entities\CartPromotion[] $promotions
 * @property-read int|null $promotions_count
 * @method static \Illuminate\Database\Eloquent\Builder|Cart newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Cart newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Cart query()
 * @method static \Illuminate\Database\Eloquent\Builder|Cart whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Cart whereDiscountTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Cart whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Cart whereItemTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Cart whereOrderTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Cart whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Cart whereTaxName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Cart whereTaxTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Cart whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Cart whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Cart whereUuid($value)
 * @mixin \Eloquent
 */
class Cart extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'uuid',
        'user_id',
        'quantity',
        'item_total',
        'order_total',
        'discount_total',
        'tax_name',
        'tax_total'
    ];

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function (Cart $cart) {
            $cart->uuid = Uuid::uuid4()->toString();
            $cart->tax_name = 'VAT (' . config('order.vat_percent') . '%)';
        });
    }

    public function items(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    public function promotions(): HasMany
    {
        return $this->hasMany(CartPromotion::class);
    }

    public function calculateAndUpdateCart()
    {
        $items = $this->items;

        $totalQuantity = $items->sum('quantity');
        $totalItemPrice = $items->sum('total_price');
        $totalDiscount = 0;

        $totalTax = $this->calculateTax($totalItemPrice, $totalDiscount);

        $this->update([
            'quantity' => $totalQuantity,
            'item_total' => $totalItemPrice,
            'discount_total' => $totalDiscount,
            'tax_total' => $totalTax,
            'order_total' => $this->calculateOrder($totalItemPrice, $totalDiscount, $totalTax)
        ]);
    }

    private function calculateTax($totalItemPrice, $totalDiscount): float
    {
        return round(($totalItemPrice - $totalDiscount) * config('order.vat_percent') / 100);
    }

    private function calculateOrder($totalItemPrice, $totalDiscount, $totalTax): int
    {
        return $totalItemPrice - $totalDiscount + $totalTax;
    }
}
