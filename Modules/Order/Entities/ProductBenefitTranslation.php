<?php

namespace Modules\Order\Entities;

use Illuminate\Database\Eloquent\Model;

/**
 * Modules\Order\Entities\ProductBenefitTranslation
 *
 * @property int $id
 * @property string $locale
 * @property int $product_benefit_id
 * @property string $name
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefitTranslation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefitTranslation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefitTranslation query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefitTranslation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefitTranslation whereLocale($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefitTranslation whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductBenefitTranslation whereProductBenefitId($value)
 * @mixin \Eloquent
 */
class ProductBenefitTranslation extends Model
{
    protected $fillable = ['id', 'name', 'locale'];
    
    /**
     * Set timestamps
     * 
     * @var boolean
     */
    public $timestamps = false;
}
