<?php

namespace Modules\Order\Entities;

use Illuminate\Database\Eloquent\Model;

/**
 * Modules\Order\Entities\ProductTranslation
 *
 * @property int $id
 * @property string $locale
 * @property int $product_id
 * @property string|null $name
 * @property string|null $description
 * @method static \Illuminate\Database\Eloquent\Builder|ProductTranslation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductTranslation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductTranslation query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductTranslation whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductTranslation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductTranslation whereLocale($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductTranslation whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductTranslation whereProductId($value)
 * @mixin \Eloquent
 */
class ProductTranslation extends Model
{
    protected $fillable = ['name', 'description'];
    
    /**
     * Set timestamps
     * 
     * @var boolean
     */
    public $timestamps = false;
}
