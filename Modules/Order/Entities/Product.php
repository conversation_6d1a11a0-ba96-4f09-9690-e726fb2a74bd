<?php

namespace Modules\Order\Entities;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Astrotomic\Translatable\Translatable;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * Modules\Order\Entities\Product
 *
 * @property int $id
 * @property int $product_category_id
 * @property string $name
 * @property int $price
 * @property int $anchoring_price
 * @property string $description
 * @property bool $is_active
 * @property int $created_by
 * @property int $updated_by
 * @property int $deleted_by
 * @property string $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\Order\Entities\ProductBenefit[] $benefits
 * @property-read int|null $benefits_count
 * @property-read \Modules\Order\Entities\ProductCategory $category
 * @method static \Illuminate\Database\Eloquent\Builder|Product newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Product newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Product query()
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereDeletedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereDiscountPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereProductCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereUpdatedBy($value)
 * @mixin \Eloquent
 * @property int $order
 * @property-read mixed $category_code
 * @property-read mixed $media_url
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\File\Entities\Media[] $media
 * @property-read int|null $media_count
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Product isActive()
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereAnchoringPrice($value)
 * @property-read \Modules\Order\Entities\ProductTranslation|null $translation
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\Order\Entities\ProductTranslation[] $translations
 * @property-read int|null $translations_count
 * @method static \Illuminate\Database\Eloquent\Builder|Product isCredit()
 * @method static \Illuminate\Database\Eloquent\Builder|Product listsTranslations(string $translationField)
 * @method static \Illuminate\Database\Eloquent\Builder|Product notTranslatedIn(?string $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Product orWhereTranslation(string $translationField, $value, ?string $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Product orWhereTranslationLike(string $translationField, $value, ?string $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Product orderByTranslation(string $translationField, string $sortMethod = 'asc')
 * @method static \Illuminate\Database\Eloquent\Builder|Product translated()
 * @method static \Illuminate\Database\Eloquent\Builder|Product translatedIn(?string $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereTranslation(string $translationField, $value, ?string $locale = null, string $method = 'whereHas', string $operator = '=')
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereTranslationLike(string $translationField, $value, ?string $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Product withTranslation()
 * @method Product translate(?string $locale = null, bool $withFallback = false)
 * @property int|null $taxonomy_id
 * @method static \Illuminate\Database\Eloquent\Builder|Product isCredit()
 * @method static \Illuminate\Database\Eloquent\Builder|Product whereTaxonomyId($value)
 */
class Product extends Model implements HasMedia, TranslatableContract
{
    use InteractsWithMedia;
    use Translatable;

    const IS_INACTIVE = 0;
    const IS_ACTIVE = 1;

    public $table = 'products';

    protected $fillable = [
        'product_category_id', 'name', 'price', 'anchoring_price',
        'description', 'is_active', 'created_by', 'updated_by',
        'deleted_by', 'deleted_at', 'order'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    /**
     * Transted attributes
     *
     * @var string[]
     */
    public $translatedAttributes = ['name', 'description'];

    /**
     * Set specific foreign key
     *
     * @var string
     */
    protected $translationForeignKey = 'product_id';

    protected $translationModel = ProductTranslation::class;

    public function benefits()
    {
        return $this->hasMany(ProductBenefit::class, 'product_id');
    }

    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'product_category_id');
    }

    public function getCategoryCodeAttribute()
    {
        return $this->category->code;
    }

    public function getMediaUrlAttribute()
    {
        if ($this->hasMedia('product_image')) {
            /** @var \Modules\File\Entities\Media $media */
            $media = $this->getMedia('product_image')->first();

            return $media->getFullUrl();
        }

        return null;
    }

    /**
     * Scope a query to only include active product
     *
     * @param  \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIsActive($query)
    {
        return $query->where('is_active', self::IS_ACTIVE);
    }

    public function scopeIsCredit($query)
    {
        $query->whereHas('category', fn($query) => $query->isCredit());
    }
}
