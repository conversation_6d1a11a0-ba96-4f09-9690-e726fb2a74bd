<?php

namespace Modules\Order\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Order\Database\Factories\OrderFactory;
use Modules\Order\States\Order\Status\AwaitingPayment;
use Modules\Order\States\Order\Status\Pending;
use Modules\Order\States\Order\Status\Cancelled;
use Modules\Order\States\Order\Status\Completed;
use Modules\Order\States\Order\Status\Error;
use Modules\Order\States\Order\Status\OrderStatus;
use Modules\Order\States\Order\Status\Refunded;
use Modules\Payment\Entities\PaymentTransactions;
use Modules\User\Entities\User;
use Spatie\ModelStates\HasStates;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * Modules\Order\Entities\Order
 *
 * @property int $id
 * @property int $user_id
 * @property string $code
 * @property OrderStatus $status
 * @property string $buyer_email
 * @property string $buyer_name
 * @property string $buyer_phone
 * @property int $crm_company_id
 * @property string $company_tax_number
 * @property string $company_business_name
 * @property string $company_phone
 * @property int $quantity
 * @property int $item_total
 * @property int $discount_total
 * @property string|null $tax_name
 * @property int $tax_total
 * @property int $order_total
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\Order\Entities\OrderItem[] $items
 * @property-read int|null $items_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\Order\Entities\OrderPromotion[] $promotions
 * @property-read int|null $promotions_count
 * @method static \Illuminate\Database\Eloquent\Builder|Order newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Order newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Order query()
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereBuyerEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereBuyerName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereBuyerPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereCompanyBusinessName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereCompanyPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereCompanyTaxNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereCrmCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereDiscountTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereItemTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereNotState(string $column, $states)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereOrderTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereState(string $column, $states)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereTaxName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereTaxTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereUserId($value)
 * @mixin \Eloquent
 * @property int|null $last_payment_transaction_id
 * @property-read mixed $amount
 * @property-read mixed $order_status
 * @property-read mixed $paid_at
 * @property-read mixed $payment_method
 * @property-read PaymentTransactions|null $lastPaymentTransaction
 * @property-read \Illuminate\Database\Eloquent\Collection|PaymentTransactions[] $paymentTransactions
 * @property-read int|null $payment_transactions_count
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereLastPaymentTransactionId($value)
 * @property-read mixed $order_info
 * @property string|null $refunded_at
 * @property int|null $refunded_by
 * @property-read mixed $payment_method_name
 * @property-read User $user
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereRefundedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereRefundedBy($value)
 * @property string|null $locale
 * @property int|null $crm_invoice_id
 * @method static \Illuminate\Database\Eloquent\Builder|Order isCompleted()
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereCrmInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereLocale($value)
 */
class Order extends Model
{
    use HasStates;
    use HasFactory;

    protected $fillable = [
        'user_id',
        'code',
        'last_payment_transaction_id',
        'status',
        'buyer_email',
        'buyer_name',
        'buyer_phone',
        'crm_company_id',
        'company_tax_number',
        'company_business_name',
        'company_phone',
        'quantity',
        'item_total',
        'discount_total',
        'tax_name',
        'tax_total',
        'order_total',
        'locale',
    ];
    protected $casts = [
        'status' => OrderStatus::class
    ];


    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class, 'order_id');
    }

    public function promotions(): HasMany
    {
        return $this->hasMany(OrderPromotion::class, 'order_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    protected static function boot()
    {
        parent::boot();

        self::creating(function ($model) {
            $model->code = self::generateUniqueCode();
        });
    }

    /**
     * Example: TOPDEV-xxxxxxxxx
     *
     * Prefix: TOPDEV-
     * Chuỗi 9 kí tự: numeric, auto generate random & unique
     */
    protected static function generateUniqueCode()
    {
        $newCode = 'TOPDEV-' . mt_rand(*********, *********);

        if (Order::whereCode($newCode)->exists()) {
            return self::generateUniqueCode();
        }

        return $newCode;
    }

    public function getPaymentMethodNameAttribute()
    {
        return $this->lastPaymentTransaction ? $this->lastPaymentTransaction->paymentMethod->name : null;
    }

    public function lastPaymentTransaction(): BelongsTo
    {
        return $this->belongsTo(PaymentTransactions::class, 'last_payment_transaction_id');
    }

    public function getPaymentMethodAttribute()
    {
        return $this->lastPaymentTransaction ? $this->lastPaymentTransaction->paymentMethod->type : null;
    }

    public function getAmountAttribute()
    {
        return $this->lastPaymentTransaction->amount;
    }

    public function getPaidAtAttribute()
    {
        return $this->lastPaymentTransaction ? $this->lastPaymentTransaction->paid_at : null;
    }

    public function isSuccess()
    {
        return $this->status->equals(Completed::class);
    }

    public function isFailed()
    {
        return $this->status->equals(Cancelled::class, Error::class);
    }

    public function isRefunded()
    {
        return $this->status->equals(Refunded::class);
    }

    public function isPending()
    {
        return $this->status->equals(AwaitingPayment::class, Pending::class);
    }

    public function getOrderStatusAttribute()
    {
        if ($this->isSuccess()) {
            return 'success';
        }

        if ($this->isFailed()) {
            return 'failed';
        }

        if ($this->isRefunded()) {
            return 'refunded';
        }

        return 'pending';
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function paymentTransactions()
    {
        return $this->hasMany(PaymentTransactions::class, 'order_id');
    }

    public function getOrderInfoAttribute()
    {
        return 'Thanh toan hoa don ' . $this->code;
    }

    public function scopeIsCompleted($query)
    {
        $query->whereState('status', Completed::class);
    }

    protected static function newFactory(): Factory
    {
        return OrderFactory::new();
    }
}
