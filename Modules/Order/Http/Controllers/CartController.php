<?php

namespace Modules\Order\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Order\Entities\CartItem;
use Modules\Order\Entities\Product;
use Modules\Order\Http\Requests\StoreCartRequest;
use Modules\Order\Http\Requests\UpdateCartRequest;
use Modules\Order\Transformers\CartResource;
use Redmix0901\Core\Http\Responses\BaseHttpResponse;
use Throwable;
use UnexpectedValueException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class CartController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return BaseHttpResponse
     */
    public function index(BaseHttpResponse $response)
    {
        $cart = auth()->user()->cart()->with([
            'items.product:id,product_category_id,is_active,price,anchoring_price',
            'items.product.category:id,code'
        ])->first();

        if (!$cart) {
            return $response->setError()->setData([]);
        }

        $items = $cart->items;

        $inactiveItems = $items->filter(fn ($item) => $item->product->is_active == Product::IS_INACTIVE);
        $priceChangedItems = $items->filter(fn ($item) => $item->price != $item->product->price);
        $anchoringPriceChangedItems = $items->filter(fn ($item) => $item->anchoring_price != $item->product->anchoring_price);

        $changedItems = $priceChangedItems->concat($anchoringPriceChangedItems)->unique();

        if ($inactiveItems->isNotEmpty() || $changedItems->isNotEmpty()) {
            $this->processUpdateCart($cart, $inactiveItems, $changedItems);
        }

        $cart->warnings = [
            'status' => $inactiveItems->pluck('name'),
            'price' => $priceChangedItems->pluck('name'),
            'anchoring_price' => $anchoringPriceChangedItems->pluck('name'),
        ];

        return $response->setData(CartResource::make($cart));
    }

    private function processUpdateCart($cart, $inactiveItems, $changedItems)
    {
        CartItem::withoutEvents(function () use ($cart, $inactiveItems, $changedItems) {
            try {
                $needRefreshCart = false;

                DB::beginTransaction();

                if ($inactiveItems->isNotEmpty()) {
                    CartItem::whereIn('id', $inactiveItems->pluck('id'))->delete();
                    $needRefreshCart = true;
                }

                if ($changedItems->isNotEmpty()) {
                    $changedItems->each(fn ($item) => $item->calculateAndSetTheTotalWithLatestPrice()->save());
                    $needRefreshCart = true;
                }

                if ($needRefreshCart) {
                    $cart->refresh()->loadMissing([
                        'items.product:id,product_category_id',
                        'items.product.category:id,code'
                    ])->calculateAndUpdateCart();
                }

                DB::commit();
            } catch (\Throwable $th) {
                DB::rollBack();
                throw $th;
            }
        });

        return $cart;
    }

    /**
     * Store a newly created resource in storage.
     * @return BaseHttpResponse
     */
    public function store(StoreCartRequest $request, BaseHttpResponse $response)
    {
        try {
            $user = auth()->user();

            // Create a new cart if it doesn't exist
            $cart = $user->cart()->firstOrCreate([]);

            // Check if the product is already in the cart
            if ($cart->items()->where('product_id', $request->product_id)->exists()) {
                throw new UnexpectedValueException('Unable to add duplicate product to the cart');
            }

            $product = Product::findOrFail($request->product_id);

            // Add the product to the cart
            $cart->items()->create([
                'product_id' => $product->id,
                'name' => $product->name,
                'name_vi' => $product->translate('vi')->name ?? null,
                'price' => $product->price,
                'anchoring_price' => $product->anchoring_price,
                'quantity' => $request->input('quantity', 1)
            ]);

            return $response->setData([
                'uuid' => $cart->uuid
            ]);
        } catch (Throwable $th) {
            return $response->setCode(Response::HTTP_BAD_REQUEST)
                ->setError()
                ->setMessage($th->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     * @return BaseHttpResponse
     */
    public function update(UpdateCartRequest $request, BaseHttpResponse $response)
    {
        try {
            $cart = auth()->user()->cart;

            $item = CartItem::where([
                'cart_id' => $cart->id,
                'product_id' => $request->product_id
            ])->firstOrFail();

            if ($item->updateQuantity($request->type, $request->quantity)) {
                return $response->setData([
                    'uuid' => $cart->uuid
                ]);
            } else {
                throw new UnexpectedValueException('Unable to update cart');
            }
        } catch (\Throwable $th) {
            return $response->setCode(Response::HTTP_BAD_REQUEST)
                ->setError()
                ->setMessage($th->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     * @param int $productId
     * @return BaseHttpResponse
     */
    public function destroy($productId, BaseHttpResponse $response)
    {
        try {
            $cart = auth()->user()->cart;

            if (!$cart) {
                throw new UnexpectedValueException('User does\'t have a cart');
            }

            CartItem::where([
                'cart_id' => $cart->id,
                'product_id' => $productId
            ])->firstOrFail()
                ->delete();

            return $response->setData([
                'uuid' => $cart->uuid
            ]);
        } catch (Throwable $th) {
            return $response->setCode(Response::HTTP_BAD_REQUEST)
                ->setError()
                ->setMessage($th->getMessage());
        }
    }
}
