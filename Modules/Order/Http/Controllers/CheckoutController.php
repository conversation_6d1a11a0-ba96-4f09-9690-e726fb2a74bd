<?php

namespace Modules\Order\Http\Controllers;

use App\Helpers\CrmApi;
use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Modules\Company\Entities\Company;
use Modules\Order\Entities\Cart;
use Modules\Order\Entities\CartItem;
use Modules\Order\Entities\CartPromotion;
use Modules\Order\Entities\Order;
use Modules\Order\Http\Requests\CheckoutRequest;
use Modules\Order\States\Order\Status\AwaitingPayment;
use Modules\Order\Transformers\ListPaymentMethodResource;
use Modules\Order\Transformers\GetOrderResultResource;
use Modules\Payment\Entities\PaymentMethod;
use Modules\Payment\Managers\Payment\PaymentManager;
use Modules\User\Entities\User;
use Redmix0901\Core\Http\Responses\BaseHttpResponse;

class CheckoutController extends Controller
{
    public function checkout(CheckoutRequest $request, BaseHttpResponse $response)
    {
        $validated = $request->validated();

        /**
         * @var \Modules\User\Entities\User $user
         */
        $user = Auth::user();
        $cart = Cart::whereUserId($user->id)->first();

        DB::beginTransaction();

        try {
            // create order
            $order = new Order(array_merge(
                $cart->makeVisible(['user_id', 'item_total', 'order_total', 'discount_total', 'quantity', 'tax_name', 'tax_total'])->toArray(),
                [
                    'status' => AwaitingPayment::class,
                    'buyer_email' => $user->email,
                    'buyer_name' => $validated['buyer_name'],
                    'buyer_phone' => $validated['buyer_phone'],
                    'crm_company_id' => $validated['crm_company_id'],
                    'company_tax_number' => $validated['company_tax_number'],
                    'company_business_name' => $validated['company_business_name'],
                    'company_phone' => $validated['company_phone'] ?? '',
                    'locale' => $validated['locale'] ?? 'en'
                ]
            ));
            $order->save();

            // create order items
            if ($cart->items->count()) {
                $order->items()->createMany($cart->items->makeHidden(['id', 'cart_id'])->toArray());
            }

            // create order promotions
            if ($cart->promotions->count()) {
                $order->promotions()->createMany($cart->promotions->makeHidden(['id', 'cart_id'])->toArray());
            }

            $paymentMethod = PaymentMethod::findActiveMethod($validated['payment_method_id']);

            $paymentData = PaymentManager::resolveDriverByMethod($paymentMethod->code)
                ->createPayment([
                    'payment_method_id' => $paymentMethod->id,
                    'order_id' => $order->id,
                    'amount' => $order->order_total,
                    'request_type' => $paymentMethod->code,
                    'order_code' => $order->code,
                    'order_info' => $order->order_info,
                ]);

            // Update last_payment_transaction_id to order
            $order->update(['last_payment_transaction_id' => $paymentData->id]);

            // clear cart
            CartItem::whereCartId($cart->id)->delete(); // cart items
            CartPromotion::whereCartId($cart->id)->delete(); // cart promotions
            $cart->delete();

            DB::commit();

            // return
            return $response->setCode(201)
                ->setData($paymentData->toArray());
        } catch (Exception $exception) {
            DB::rollBack();

            return $response->setCode(500)
                ->setData(null)
                ->setMessage($exception->getMessage());
        }
    }

    public function result(string $orderCode, BaseHttpResponse $response)
    {
        try {
            $order = Order::whereUserId(Auth::id())
                ->whereCode($orderCode)
                ->with('lastPaymentTransaction')
                ->firstOrFail();

            return $response->setCode(200)
                ->setData(GetOrderResultResource::make($order))->setMessage('Ok');
        } catch (Exception $exception) {
            return $response->setCode(500)
                ->setMessage($exception->getMessage());
        }
    }

    public function paymentMethods(BaseHttpResponse $response)
    {
        $data = PaymentMethod::isActive()
            ->select('id', 'name', 'code', 'type', 'payment_vendor_id')
            ->with('paymentVendor')
            ->get();

        if ($data->isEmpty()) {
            return $response->setCode(200)->setMessage("No Payment Method Found.");
        }

        return $response->setCode(200)
            ->setData(ListPaymentMethodResource::collection($data))
            ->setMessage('Ok');
    }

    public function crmCompanies(BaseHttpResponse $response)
    {
        /** @var \Modules\User\Entities\User $user */
        $user = Auth::user();

        throw_if(!$user->hasPermission(User::EMPLOYER_TYPE), Exception::class, 'Permission denied');

        try {
            /**
             * @var \Modules\Company\Entities\Company $company
             */
            $company = Company::select('id', 'tax_number')
                ->whereId($user->company_id)->firstOrFail();

            /** @phpstan-ignore-next-line */
            $apiResponse = app(CrmApi::class)->getCrmCompanies($company->id, $company->tax_number);

            throw_if(!$apiResponse['success'], Exception::class, $apiResponse['message'] ?? '');

            return $response->setCode(200)
                ->setData($apiResponse['data'])
                ->setMessage($apiResponse['data'] ? 'Ok' : 'No Tax Number Found.');
        } catch (Exception $exception) {

            return $response->setCode(500)
                ->setMessage($exception->getMessage());
        }
    }
}
