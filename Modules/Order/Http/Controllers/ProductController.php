<?php

namespace Modules\Order\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Response;
use Modules\Order\Entities\Product;
use Modules\Order\Http\Requests\GetListProductsRequest;
use Modules\Order\Transformers\GetProductDetailResource;
use Modules\Order\Transformers\ProductResource;
use Redmix0901\Core\Http\Responses\BaseHttpResponse;

class ProductController extends Controller
{
    public function index(GetListProductsRequest $request, BaseHttpResponse $response)
    {
        $page = $request->page ?? 1;
        $size = $request->page_size ?? 10;
        $from = ($size > 0) ? ($size * ($page - 1)) : 0;

        $products = Product::when($request->category_code, fn ($builder) => $builder->whereHas('category', fn($qr) => $qr->whereCode($request->category_code)))
            ->when($size > 0, fn($qr) => $qr->offset($from)->limit($size))
            ->with([
                'benefits' => fn($subQuery) => $subQuery->select('id', 'product_id', 'name', 'order')->orderBy('order'),
                'translations',
                'benefits.translations',
            ])
            ->isActive()
            ->orderBy('order')
            ->get();

        return $response->setCode(200)->setData(ProductResource::collection($products));
    }

    public function detail(int $id, BaseHttpResponse $response)
    {
        try {
            $product = Product::whereId($id)->firstOrFail();

            return $response->setCode(Response::HTTP_OK)
                ->setMessage("Ok")
                ->setData(GetProductDetailResource::make($product));
        } catch (\Throwable $th) {
            return $response->setCode(Response::HTTP_BAD_REQUEST)
                ->setError()
                ->setMessage($th->getMessage());
        }
    }
}
