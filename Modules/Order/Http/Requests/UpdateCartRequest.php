<?php

namespace Modules\Order\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\Order\Rules\ValidProduct;
use Modules\Order\Entities\CartItem;

class UpdateCartRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'product_id' => [
                'required',
                'integer',
                new ValidProduct
            ],
            'quantity' => 'nullable|integer|min:1|max:' . CartItem::MAXIMUM_QUANTITY,
            'type' => 'required_without:quantity|in:increment,decrement'
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return auth()->user()->cart()->exists();
    }

    public function messages()
    {
        return [
            'type.in' => 'The type must be increment or decrement.'
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $failedRules = $validator->failed();

            if (isset($failedRules['quantity']['Max'])) {
                $validator->errors()->add('quantity_max', 'Maximum number of products.');
            }

            if ($this->input('type') == 'increment') {
                $cartItem = CartItem::firstWhere([
                    'cart_id' => auth()->user()->cart->id,
                    'product_id' => $this->input('product_id')
                ]);

                if ($cartItem && $cartItem->quantity >= CartItem::MAXIMUM_QUANTITY) {
                    $validator->errors()->add('quantity_max', 'Maximum number of products.');
                }
            }
        });
    }
}
