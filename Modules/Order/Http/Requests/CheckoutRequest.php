<?php

namespace Modules\Order\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Modules\Order\Entities\Cart;
use Modules\Order\Rules\AlreadyExistedCompanyTaxNumberRule;
use Modules\Order\Rules\AlreadyExistedCrmCompanyIdRule;
use Modules\Order\Rules\ValidatedCartInformationRule;
use Illuminate\Validation\Validator;

class CheckoutRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        /** @var \Modules\User\Entities\User $user */
        $user = Auth::user();

        return [
            'buyer_name' => ['required'],
            'buyer_phone' => ['required', 'regex:/^\d+$/', 'min:10', 'max:15'],
            'company_tax_number' => ['required', new AlreadyExistedCompanyTaxNumberRule($user->company_id)],
            'crm_company_id' => ['required', new AlreadyExistedCrmCompanyIdRule($user->company_id, $this->company_tax_number)],
            'company_business_name' => ['required'],
            'company_phone' => ['nullable', 'regex:/^\d+$/', 'min:10', 'max:15'],
            'payment_method_id' => ['required', 'exists:payment_methods,id'],
            'item_total' => ['required', new ValidatedCartInformationRule()],
            'order_total' => ['required', new ValidatedCartInformationRule()],
            'discount_total' => ['required', new ValidatedCartInformationRule()],
            'quantity' => ['required', 'numeric', new ValidatedCartInformationRule()],
            'term_of_use' => ['required', 'boolean', 'accepted'],
            'locale' => ['nullable', 'in:en,vi'],
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Cart::whereUserId(Auth::user()->id)->exists();
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'company_tax_number' => 'Company',
            'crm_company_id' => 'Company'
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @return void
     */
    public function withValidator(Validator $validator)
    {
        $validator->after(function (Validator $validator) {
            if ($validator->errors()->isNotEmpty()) {
                /**
                 * @var \Illuminate\Support\MessageBag $failedRules
                 */
                $failedRules = $validator->failed();
                if (
                    isset($failedRules['item_total'][ValidatedCartInformationRule::class]) ||
                    isset($failedRules['order_total'][ValidatedCartInformationRule::class]) ||
                    isset($failedRules['discount_total'][ValidatedCartInformationRule::class]) ||
                    isset($failedRules['quantity'][ValidatedCartInformationRule::class])
                ) {
                    $validator->errors()->add('cart', "The cart has been already updated.");
                }
            }
        });
    }
}
