<?php

namespace Modules\Order\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\Order\Rules\AlreadyExistedCategoryCodeRule;

class GetListProductsRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'category_code' => ['nullable', new AlreadyExistedCategoryCodeRule()],
            'page' => ['nullable', 'integer', 'min:1'],
            'page_size' => ['nullable'],
            'width' => 'required',
            'height' => 'required',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}
