<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use Faker\Generator as Faker;
use Modules\Company\Entities\Company;

$factory->define(Company::class, fn($faker) => []);
$factory->state(Company::class, 'not-mapping-yet', function (Faker $faker) {
    return [
        'id' => 1,
        'uuid' => $faker->uuid,
        'display_name' => 'Applancer JSC',
        'phone' => '0123456789',
        'description' => 'Applancer JSC',
        'slug' => 'testing',
        'user_id' => NULL,
        'status' => 1,
        'tax_number' => NULL,
        'created_at' => now(),
        'updated_at' => now()
    ];
});

$factory->state(Company::class, 'already-mapping', function (Faker $faker) {
    return [
        'id' => 1,
        'uuid' => $faker->uuid,
        'display_name' => 'Applancer JSC',
        'phone' => '0123456789',
        'description' => 'Applancer JSC',
        'slug' => 'testing',
        'user_id' => NULL,
        'status' => 1,
        'tax_number' => '0314372496',
        'created_at' => now(),
        'updated_at' => now()
    ];
});
