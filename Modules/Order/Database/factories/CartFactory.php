<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use Faker\Generator as Faker;
use Modules\Order\Entities\Cart;

$factory->define(Cart::class, fn(Faker $faker) => [
    'uuid' => $faker->uuid,
    'user_id' => 3158369,
    'quantity' => 4,
    'item_total' => 5833000,
    'order_total' => 10833000,
    'discount_total' => 0,
    'tax_name' => 'Free Tax Name',
    'tax_total' => 0
]);
