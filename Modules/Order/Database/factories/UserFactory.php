<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use Faker\Generator as Faker;
use Modules\User\Entities\User;

$factory->define(User::class, fn(Faker $faker) => [
    'id' => 3158369,
    'uuid' => $faker->uuid,
    'firstname' => "HR",
    'lastname' => "Admin",
    'display_name' => 'HR Admin Topdev',
    'username' => '<EMAIL>',
    'email' => '<EMAIL>',
    'phone' => '03947586754',
    'gender' => 'female',
    'birthday' => $faker->date('Y-m-d'),
    'password' => $faker->md5('12345678'),
    'type' => 'employer',
    'remember_token' => $faker->md5('remember_token'),
    'company_id' => 1,
    'approved_at' => $faker->date('Y-m-d H:i:s'),
    'email_verified_at' => $faker->date('Y-m-d H:i:s'),
]);
