<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use Faker\Generator as Faker;
use Mo<PERSON>les\Admin\Auth\Database\Administrator;

$factory->define(Administrator::class, fn($faker) => []);
$factory->state(Administrator::class, 'admin_user', function (Faker $faker) {
    return [
        'username' => $faker->userName(),
        'email' => $faker->email(),
        'password' => $faker->password(),
        'name' => $faker->name(),
    ];
});
