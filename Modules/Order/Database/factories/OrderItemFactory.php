<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use Faker\Generator as Faker;
use Modules\Order\Entities\OrderItem;

$factory->define(OrderItem::class, function (Faker $faker) {
    return [
        'order_id' => random_int(1, 3),
        'product_id' => random_int(1, 10),
        'name' => 'item name',
        'quantity' => 3,
        'price' => 30000,
        'total_price' => 1000000,
        'anchoring_price' => 0,
        'total_anchoring_price' => 0
    ];
});
