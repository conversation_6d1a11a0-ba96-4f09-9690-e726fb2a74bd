<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use Faker\Generator as Faker;
use Modules\Order\Entities\Product;

$factory->define(Product::class, fn(Faker $faker) => [
    'name' => $faker->words(3, true),
    'product_category_id' => 1,
    'description' => $faker->text(100),
    'price' => $faker->randomNumber(6, true),
    'anchoring_price' => $faker->randomNumber(5, true),
    'is_active' => Product::IS_ACTIVE,
    'created_by' => 0,
    'order' => 0,
]);
