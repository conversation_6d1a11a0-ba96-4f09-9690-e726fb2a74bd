<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use Faker\Generator as Faker;
use Illuminate\Support\Str;
use Modules\User\Entities\User;

$factory->state(User::class, 'user_employer', function (Faker $faker) {
    return [
        'display_name' => $faker->name(),
        'email' => $faker->email(),
        'email_verified_at' => now(),
        'password' => $faker->password(),
        'remember_token' => Str::random(10),
        'type' => User::EMPLOYER_TYPE
    ];
});
