<?php

namespace Modules\Order\Observers;

use Modules\Order\Entities\CartItem;

class CartItemObserver
{

    public function creating(CartItem $cartItem)
    {
        $cartItem->calculateAndSetTheTotal();
    }

    /**
     * Handle the CartItem "created" event.
     *
     * @return void
     */
    public function created(CartItem $cartItem)
    {
        $cartItem->cart->calculateAndUpdateCart();
    }

    /**
     * Handle the CartItem "updated" event.
     *
     * @return void
     */
    public function updated(CartItem $cartItem)
    {
        $cartItem->cart->calculateAndUpdateCart();
    }

    public function updating(CartItem $cartItem)
    {
        $cartItem->calculateAndSetTheTotal();
    }

    /**
     * Handle the CartItem "deleted" event.
     *
     * @return void
     */
    public function deleted(CartItem $cartItem)
    {
        $cartItem->cart->calculateAndUpdateCart();
    }
}
