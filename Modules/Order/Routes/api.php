<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware(['auth:sanctum'])->group(function () {
    /**
     * API related to Carts
     */
    Route::apiResource('carts', 'CartController')->except('show', 'update');

    Route::prefix('carts')->group(function () {
        Route::put('/', 'CartController@update');
        /**
         * API related to Promo within Carts
         */
        Route::post('/promo', 'PromoController@add');
        Route::delete('/promo/{promoCode}', 'PromoController@delete');
    });
});

Route::prefix('orders')->group(function () {
    Route::middleware(['auth:sanctum'])->group(function () {
        /**
         * API related to checkout flow
         */
        Route::post('/', 'CheckoutController@checkout');
        Route::get('/result/{orderCode}', 'CheckoutController@result');
        Route::get('/methods', 'CheckoutController@paymentMethods');
        Route::get('/companies', 'CheckoutController@crmCompanies');

        /**
         * API related to order
         */
        Route::get('/', 'OrderController@index');
        Route::get('/{orderCode}', 'OrderController@detail');
    });
});

Route::prefix('products')->group(function () {
    Route::get('/', 'ProductController@index');
    Route::get('/{id}', 'ProductController@detail');
});
