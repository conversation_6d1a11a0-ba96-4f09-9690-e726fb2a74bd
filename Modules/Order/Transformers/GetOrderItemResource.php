<?php

namespace Modules\Order\Transformers;

use Illuminate\Http\Resources\Json\JsonResource as Resource;

class GetOrderItemResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request | null $request
     * @return array
     */
    public function toArray($request = null)
    {
        /** @var \Modules\Order\Entities\OrderItem $this */
        return [
            'name' => $this->name,
            'quantity' => $this->quantity,
            'price' => $this->formatMoney($this->price),
            'total_price' => $this->formatMoney($this->total_price),
        ];
    }
}
