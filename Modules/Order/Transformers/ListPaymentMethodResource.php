<?php

namespace Modules\Order\Transformers;

use Illuminate\Http\Resources\Json\JsonResource as Resource;

class ListPaymentMethodResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request|null $request
     * @return array
     */
    public function toArray($request = null)
    {
        /**
         * @var \Modules\Payment\Entities\PaymentMethod $this
         */
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'type' => $this->type,
            'payment_vendor' => $this->paymentVendor->name
        ];
    }
}
