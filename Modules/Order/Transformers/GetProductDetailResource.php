<?php

namespace Modules\Order\Transformers;

use Illuminate\Http\Resources\Json\JsonResource as Resource;
use Modules\Order\Helpers\TranslationHelper;
use Modules\Order\Entities\Product;

class GetProductDetailResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request|null $request
     * @return array
     */
    public function toArray($request = null)
    {
        /** @var \Modules\Order\Entities\Product $this */
        return [
            'name' => $this->name,
            'translables' => TranslationHelper::toTranslableArray($this->getModel()),
            'is_active' => $this->is_active,
            'price' => $this->price,
            'anchoring_price' => $this->anchoring_price
        ];
    }
}
