<?php

namespace Modules\Order\Transformers;

use Illuminate\Http\Resources\Json\JsonResource as Resource;

class GetPaymentTransactionsResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request|null $request
     * @return array
     */
    public function toArray($request = null)
    {
        /**
         * @var \Modules\Payment\Entities\PaymentTransactions $this
         */
        return [
            "code" => $this->order_code,
            "created_at" => $this->order->created_at->format('H:i:s Y-m-d'),
            "paid_at" => $this->paid_at ? $this->paid_at->format('H:i:s Y-m-d') : null,
            "payment_method" => $this->payment_method,
            "status" => $this->status,
            "amount" => $this->amount
        ];
    }
}
