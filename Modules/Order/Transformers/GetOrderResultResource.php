<?php

namespace Modules\Order\Transformers;

use Illuminate\Http\Resources\Json\JsonResource as Resource;

class GetOrderResultResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request|null $request
     * @return array
     */
    public function toArray($request = null)
    {
        /** @var \Modules\Order\Entities\Order $this */
        return [
            "code" => $this->code,
            "created_at" => $this->created_at->format('H:i:s Y-m-d'),
            "paid_at" => $this->paid_at ? $this->paid_at->format('H:i:s Y-m-d') : null,
            "payment_method" => $this->payment_method,
            "status" => $this->order_status,
            "amount" => $this->order_total
        ];
    }
}
