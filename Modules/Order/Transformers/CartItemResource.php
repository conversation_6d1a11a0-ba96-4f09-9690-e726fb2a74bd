<?php

namespace Modules\Order\Transformers;

use Illuminate\Http\Resources\Json\JsonResource;

class CartItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request)
    {
        /**
         * @var \Modules\Order\Entities\CartItem $this
         */
        return [
            'product_id' => $this->product_id,
            'name' => $this->name,
            'translables' => [
                'en' => [
                    'name' => $this->name,
                ],
                'vi' => [
                    'name' => $this->name_vi,
                ]
            ],
            'quantity' => $this->quantity,
            'price' => $this->price,
            'total_price' => $this->total_price,
            'anchoring_price' => $this->anchoring_price,
            'total_anchoring_price' => $this->total_anchoring_price,
            'category_code' => $this->product->category_code,
        ];
    }
}
