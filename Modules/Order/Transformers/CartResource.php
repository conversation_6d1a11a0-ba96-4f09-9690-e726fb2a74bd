<?php

namespace Modules\Order\Transformers;

use Illuminate\Http\Resources\Json\JsonResource as Resource;

class CartResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request)
    {
        /**
         * @var \Modules\Order\Entities\Cart $this
         */
        return [
            'warnings' => $this['warnings'],
            'uuid' => $this->uuid,
            'items' => CartItemResource::collection($this->items),
            'promotions' => [],
            'quantity' => $this->quantity,
            'price_total' => $this->item_total,
            'order_total' => $this->order_total,
            'discount_total' => $this->discount_total,
            'tax_name' => $this->tax_name,
            'total_tax' => $this->tax_total,
            'items_count' => $this->items->count()
        ];
    }
}
