<?php

namespace Modules\Order\Transformers;

use Illuminate\Http\Resources\Json\JsonResource as Resource;
use Modules\Order\Helpers\TranslationHelper;

class ProductResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request|null $request
     * @return array
     */
    public function toArray($request = null)
    {
        /**
         * @var \Modules\Order\Entities\Product $this
         */
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'translables' => TranslationHelper::toTranslableArray($this->getModel()),
            'category_code' => $this->category_code,
            'benefits' => $this->benefits->map(function ($benefit) {
                return [
                    'id' => $benefit->id,
                    'order' => $benefit->order,
                    'name' => $benefit->name,
                    'translables' => TranslationHelper::toTranslableArray($benefit)
                ];
            }),
            'price' => $this->price,
            'anchoring_price' => $this->anchoring_price,
            'media_url' => $this->media_url ? resize_with_salt($this->media_url, $request['width'], $request['height']) : null,
        ];
    }
}
