<?php

namespace Modules\Order\Rules;

use App\Helpers\CrmApi;
use Illuminate\Contracts\Validation\Rule;

class AlreadyExistedCompanyTaxNumberRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct(private $ams_company_id)
    {
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $response = app(CrmApi::class)->getCrmCompanies($this->ams_company_id, $value);

        return $response && $response['success'] && $response['data'];
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The company is not existed.';
    }
}
