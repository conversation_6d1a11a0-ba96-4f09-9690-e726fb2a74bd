<?php

namespace Modules\Order\Rules;

use Illuminate\Contracts\Validation\Rule;
use Modules\Order\Entities\ProductCategory;

class AlreadyExistedCategoryCodeRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if ($value) {
            return ProductCategory::whereCode($value)->exists();
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The category is not existed.';
    }
}
