<?php

namespace Modules\Order\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Modules\Order\Entities\Cart;

class ValidatedCartInformationRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $cart = Cart::select('item_total', 'order_total', 'discount_total', 'quantity')->whereUserId(Auth::id())->first();

        if (!$cart) {
            return false;
        }

        return $cart->{$attribute} == $value;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return "The cart has been already updated.";
    }
}
