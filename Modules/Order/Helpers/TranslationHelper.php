<?php

namespace Modules\Order\Helpers;

use Astrotomic\Translatable\Contracts\Translatable;

class TranslationHelper
{
    /**
     * Convert data to translable attributes
     *
     * @param \Astrotomic\Translatable\Contracts\Translatable $model related model to get translate data
     * @return array
     */
    public static function toTranslableArray($model)
    {
        if (!in_array(Translatable::class, class_implements($model))) {
            throw new \Exception('Not found translation implementation!');
        }
        
        if (empty($model->translatedAttributes)) {
            throw new \Exception('Not found translated attributes!');
        }

        $translables = [];
        $locales = config('translatable.locales');
        foreach($locales as $locale) {
            foreach($model->translatedAttributes as $translable) {
                if (!isset($translables[$locale])) {
                    $translables[$locale] = [];
                }
                $translables[$locale][$translable] = $model->translate($locale)[$translable] ?? '';
            }
        }

        return $translables;
    }
}