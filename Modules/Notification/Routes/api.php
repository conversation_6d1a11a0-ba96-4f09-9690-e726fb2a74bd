<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::prefix('/notifications')->group(function () {
    Route::middleware('auth:api')->group(function () {
        Route::get('/', 'NotificationController@all');
        Route::post('/mark-as-seen', 'NotificationController@markAsSeen');
        Route::post('/mark-as-read', 'NotificationController@markAsRead'); //
        Route::get('/reports', 'NotificationController@reports')->middleware('permission:administrator');
    });
});
