<?php

namespace Modules\Notification\Repositories\Eloquents;

use Modules\Notification\Repositories\Contracts\NotificationRepositoryInterface;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\DateHistogramAggregation;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\FilterAggregation;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\MissingAggregation;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\TermsAggregation;
use ONGR\ElasticsearchDSL\Aggregation\Metric\CardinalityAggregation;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MultiMatchQuery;
use ONGR\ElasticsearchDSL\Query\MatchAllQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\ExistsQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\RangeQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;
use ONGR\ElasticsearchDSL\Sort\FieldSort;
use Redmix0901\Core\Repositories\Eloquent\BaseRepository;

class NotificationEloquentRepository extends BaseRepository implements NotificationRepositoryInterface
{
    private $arrPeriod = [
        'daily' => 'day',
        'monthly' => 'month',
        'yearly' => 'year',
        'weekly' => 'week',
    ];

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = null, $params = [], $fields = '*')
    {
        return $this->querySearch($keyword, $params, $fields)->raw();
    }

    public function statisticsOnElasticsearch($keyword = null, $params = [], $fields = '*')
    {
        return $this->model->search('*', function ($client, $body) use ($keyword, $params) {
            $size = $params['page_size'] ?? 10;
            $from = isset($params['page']) ? $size * ($params['page'] - 1) : 0;

            $baseQuery = new BoolQuery();

            if (empty($keyword) && empty($params)) {
                $baseQuery = new MatchAllQuery();
            }

            //query by period
            if ($this->hasQuery($params, 'created_at')) {
                $body->addQuery(new RangeQuery(
                    'created_at',
                    [
                        'gte' => $params['created_at']['from'],
                        'lte' => $params['created_at']['to'],
                        'format' => 'yyyy-MM-dd HH:mm:ss',
                    ]
                ));
            }

            $body->setSize($size);
            $body->setFrom($from);
            $body->addQuery($baseQuery);

            $source = new TermsAggregation('source_type', 'type');
            $source->addParameter('size', 10000);

            $dateHist = new DateHistogramAggregation('statis');
            $dateHist->setField('created_at');
            if ($this->hasQuery($params, 'period')) {
                if (array_key_exists($params['period'], $this->arrPeriod)) {
                    $dateHist->setInterval($this->arrPeriod[$params['period']]);
                }
            } else {
                $dateHist->setInterval('month');
            }

            $cardinalityAggregation = new CardinalityAggregation('resume_count');
            $cardinalityAggregation->setField('notifiable_id');

            $seenAggregations = new FilterAggregation('seen_at');
            $seenAggregations->setFilter(new ExistsQuery('seen_at'));
            $seenAggregations->addAggregation($cardinalityAggregation);

            $readAggregations = new FilterAggregation('read_at');
            $readAggregations->setFilter(new ExistsQuery('read_at'));
            $readAggregations->addAggregation($cardinalityAggregation);

            $dateHist->addAggregation($source->addAggregation($seenAggregations));
            $dateHist->addAggregation($source->addAggregation($readAggregations));

            $body->addAggregation($dateHist);
            $body->addSort(new FieldSort('created_at', null, ['order' => FieldSort::DESC]));

            return $client->search(['index' => $this->model->searchableAs(), 'body' => $body->toArray()])->asArray();
        })->raw();
    }

    /**
     * @inheritdoc
     */
    private function querySearch($keyword, $params, $fields)
    {
        return $this->model->search('*', function ($client, $body) use ($keyword, $params) {
            // Pagination
            $size = $params['page_size'] ?? 10;
            $from = isset($params['page']) ? $size * ($params['page'] - 1) : 0;

            $baseQuery = new BoolQuery();

            /*
             *  Lọc theo notifiable_id va notifiable_type
             */
            if ($this->hasQuery($params, 'all')) {
                $baseQuery->add(new TermQuery('notifiable_id', $params['all']), BoolQuery::MUST);
                $baseQuery->add(new TermQuery('notifiable_type', \Modules\User\Entities\User::class), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'user')) {
                $baseQuery->add(new TermQuery('notifiable_id', $params['user']), BoolQuery::MUST);
                $baseQuery->add(new TermQuery('notifiable_type', \Modules\User\Entities\User::class), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'not_seen')) {
                $baseQuery->add(new ExistsQuery('seen_at'), BoolQuery::MUST_NOT);
            }

            if ($this->hasQuery($params, 'not_read')) {
                $baseQuery->add(new ExistsQuery('read_at'), BoolQuery::MUST_NOT);
            }

            if ($this->hasQuery($params, 'published_at')) {
                $baseQuery->add(
                    new RangeQuery(
                        'published_at',
                        [
                            'lte' => $params['published_at'],
                            'format' => 'yyyy-MM-dd HH:mm:ss',
                        ]
                    ),
                    BoolQuery::MUST
                );
            }

            /*
             * Tìm kiếm keyword các trường
             */
            if (!empty($keyword)) {
                $baseQuery->add(new MultiMatchQuery(['notifiable_id', 'type', '_id'], $keyword, ['operator' => 'or']), BoolQuery::MUST);
            }

            $body->addSort(new FieldSort('created_at', null, ['order' => FieldSort::DESC]));

            $body->setSize($size);
            $body->setFrom($from);
            $body->addQuery($baseQuery);

            $missingSeen = new MissingAggregation('unseen', 'seen_at');
            $missingRead = new MissingAggregation('unread', 'read_at');

            $body->addAggregation($missingSeen);
            $body->addAggregation($missingRead);

            return $client->search(['index' => $this->model->searchableAs(), 'body' => $body->toArray()])->asArray();
        })
        ->query(function ($builder) {
        });
    }

    /**
     * Check if has query.
     */
    private function hasQuery($query, $key)
    {
        return (bool) (isset($query[$key]) && !is_null($query[$key]));
    }
}
