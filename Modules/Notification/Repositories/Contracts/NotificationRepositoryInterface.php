<?php

namespace Modules\Notification\Repositories\Contracts;

use Redmix0901\Core\Repositories\Interfaces\BaseRepositoryInterface;

interface NotificationRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = '*', $params = [], $fields = '*');

    public function statisticsOnElasticsearch($keyword = '*', $params = [], $fields = '*');
}
