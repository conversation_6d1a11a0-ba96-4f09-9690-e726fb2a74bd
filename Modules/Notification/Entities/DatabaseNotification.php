<?php

namespace Modules\Notification\Entities;

use Illuminate\Notifications\DatabaseNotification as BaseDatabaseNotification;
use <PERSON><PERSON>\Scout\Searchable;

class DatabaseNotification extends BaseDatabaseNotification
{
    use Searchable;

    /**
     * @inheritdoc
     */
    protected $table = 'notifications';

    // /**
    //  * The "type" of the primary key ID.
    //  *
    //  * @var string
    //  */
    // protected $keyType = 'int';

    // /**
    //  * Indicates if the IDs are auto-incrementing.
    //  *
    //  * @var bool
    //  */
    // public $incrementing = true;

    /**
     * @inheritdoc
     */
    protected $fillable = [];

    /**
     * Thetributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'seen_at' => 'datetime',
        'read_at' => 'datetime',
        'updated_at' => 'datetime',
        'created_at' => 'datetime',
        'data' => 'array',
    ];

    /**
     * @inheritdoc
     */
    public function markAsSeen()
    {
        if (is_null($this->seen_at)) {
            $this->forceFill(['seen_at' => $this->freshTimestamp()])->save();
        }
    }

    /**
     * Mark the notification as unread.
     *
     * @return void
     */
    public function markAsUnseen()
    {
        if (!is_null($this->seen_at)) {
            $this->forceFill(['seen_at' => null])->save();
        }
    }

    /**
     * @inheritdoc
     */
    public function markAsRead()
    {
        if (is_null($this->read_at)) {
            $this->forceFill(['read_at' => $this->freshTimestamp()])->save();
        }
    }

    /**
     * Mark the notification as unread.
     *
     * @return void
     */
    public function markAsUnread()
    {
        if (!is_null($this->read_at)) {
            $this->forceFill(['read_at' => null])->save();
        }
    }

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'database_notification_ams';
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        \Log::info('Build elasticsearch database notification: ' . $this->getKey());

        return [
            'id' => $this->getKey(),
            'data' => $this->data,
            'type' => $this->type,
            'notifiable_id' => $this->notifiable_id,
            'notifiable_type' => $this->notifiable_type,
            'seen_at' => empty($this->seen_at) ? null : $this->seen_at->format('Y-m-d H:i:s'),
            'read_at' => empty($this->read_at) ? null : $this->read_at->format('Y-m-d H:i:s'),
            'updated_at' => empty($this->updated_at) ? null : $this->updated_at->format('Y-m-d H:i:s'),
            'created_at' => empty($this->created_at) ? null : $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
