<?php

namespace Modules\Notification\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ValidateParams extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            // 'ids' => 'required|regex:'.config('validate.params.ids'),
            'page' => 'nullable|integer|min:1',
        ];
    }

    public function messages()
    {
        return [
            // 'ids.required' => "Field ids is required?",
            // 'ids.regex' => "The format of ids isn't correct?",
            'page.integer' => "The format of page isn't correct?",
            'page.min' => 'Page require min is 1',
        ];
    }
}
