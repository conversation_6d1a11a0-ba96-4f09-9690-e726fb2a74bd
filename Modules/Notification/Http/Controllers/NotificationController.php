<?php

namespace Modules\Notification\Http\Controllers;

use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Modules\Notification\Events\NotificationMarkAsRead;
use Modules\Notification\Events\NotificationMarkAsSeen;
use Modules\Notification\Http\Requests\MarkAsRequest;
use Modules\Notification\Http\Requests\ValidateParams;
use Modules\Notification\Repositories\Contracts\NotificationRepositoryInterface;
use Modules\Notification\Transformers\NotificationCollection;

class NotificationController extends Controller
{
    /**
     * @var NotificationRepositoryInterface
     */
    protected $notiRepository;

    public function __construct(NotificationRepositoryInterface $noti)
    {
        $this->notiRepository = $noti;
    }

    public function all(ValidateParams $request)
    {
        $notifications = $this->notiRepository->searchOnElasticsearch(
            $request->keyword,
            array_merge($request->all(), [
                'user' => $request->user()->getKey(),
                'published_at' => now()->format('Y-m-d H:i:s'),
            ])
        );

        return NotificationCollection::fromElasticsearch($notifications, $request->page_size ?? 10)
                                ->response()
                                ->setStatusCode(200);
    }

    public function markAsSeen(MarkAsRequest $request)
    {
        $ids = $request->ids;
        if (in_array('all', explode(',', (string) $request->ids))) {
            $ids = $this->searchElasticsearchWithType($request->user()->id, 'not_seen');
        }

        if ($ids != null) {
            event(new NotificationMarkAsSeen($request->user(), $ids));
        }

        return [
            'success' => true,
        ];
    }

    public function markAsRead(MarkAsRequest $request)
    {
        $ids = $request->ids;
        if (in_array('all', explode(',', (string) $request->ids))) {
            $ids = $this->searchElasticsearchWithType($request->user()->id, 'not_read');
        }

        if ($ids != null) {
            event(new NotificationMarkAsRead($request->user(), $ids));
        }

        return [
            'success' => true,
        ];
    }

    public function searchElasticsearchWithType($userId, $type)
    {
        $notifications = $this->notiRepository->searchOnElasticsearch(null, [
                'all' => $userId,
                'page_size' => 100,
                $type => true,
            ]);

        return collect(($notifications['hits']['hits']))->map(fn($value) => $value['_source']['id'])->implode(',');
    }

    public function reports(Request $request)
    {
        $fromDate = $request->from_date ? Carbon::createFromFormat('d-m-Y', $request->from_date) : Carbon::today()->subWeeks(5);
        $toDate = $request->to_date ? Carbon::createFromFormat('d-m-Y', $request->to_date) : Carbon::today();

        $notifications = $this->notiRepository->statisticsOnElasticsearch(null, [
            'created_at' => [
                'from' => $fromDate->startOfDay()->format('Y-m-d H:i:s'),
                'to' => $toDate->endOfDay()->format('Y-m-d H:i:s'),
            ],
            'period' => 'daily',
            'page_size' => 10000,
        ]);

        $results = [];
        foreach ($notifications['aggregations']['statis']['buckets'] as $item) {
            $key1 = explode(' ', (string) $item['key_as_string'])[1];
            foreach ($item['source_type']['buckets'] as $bucket) {
                $key2 = Str::afterLast($bucket['key'], '\\');
                $results[$key1][$key2] = [
                    'notifications_count' => $bucket['doc_count'] ?? 0,
                    'notifications_seen_count' => $bucket['seen_at']['doc_count'] ?? 0,
                    'notifications_read_count' => $bucket['read_at']['doc_count'] ?? 0,
                    'user_seen_notifications_count' => $bucket['seen_at']['resume_count']['value'] ?? 0,
                    'user_read_notifications_count' => $bucket['read_at']['resume_count']['value'] ?? 0,
                ];
            }
            $results[$key1]['created_at'] = Carbon::parse($key1)->timestamp;
        }

        return response()->json([
            'error' => false,
            'message' => 'Okay!',
            'data' => $results,
        ]);
    }
}
