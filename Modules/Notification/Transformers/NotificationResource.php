<?php

namespace Modules\Notification\Transformers;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource as Resource;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class NotificationResource extends Resource
{
    use ElasticCollectionTrait;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        $fields = isset($request->fields['notification'])
                    ? explode(',', (string) $request->fields['notification']) : [];

        $data = (array) $this->data;

        if (isset($data['ids'])) {
            $data['ids'] = (string) $data['ids'];
        }

        return array_merge([
            'id' => (string) $this->uuid,
            'seen_at' => (!empty($this->seen_at) ? Carbon::createFromFormat('Y-m-d H:i:s', $this->seen_at)->format('d-m-Y') : null),
            'read_at' => (!empty($this->read_at) ? Carbon::createFromFormat('Y-m-d H:i:s', $this->read_at)->format('d-m-Y') : null),
            'created_at' => (!empty($this->created_at) ? Carbon::createFromFormat('Y-m-d H:i:s', $this->created_at)->format('d-m-Y H:i:s') : null),
        ], $data);
    }
}
