<?php

namespace Modules\Notification\Transformers;

use Illuminate\Http\Resources\Json\ResourceCollection;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class NotificationCollection extends ResourceCollection
{
    use ElasticCollectionTrait;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return NotificationResource::collection($this->collection);
    }

    public function withResponse($request, $response)
    {
        $elasticCollection = static::getElasticCollection();
        $json = array_merge([
            '_type' => 'notification',
            'aggregations' => empty($elasticCollection) ? [] : $elasticCollection->aggregations(),
        ], json_decode($response->getContent(), true));

        $response->setContent(json_encode($json));
    }
}
