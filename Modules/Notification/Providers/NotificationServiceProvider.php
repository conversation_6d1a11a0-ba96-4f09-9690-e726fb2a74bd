<?php

namespace Modules\Notification\Providers;

use Illuminate\Support\ServiceProvider;
use Modules\Notification\Entities\DatabaseNotification;
use Modules\Notification\Repositories\Contracts\NotificationRepositoryInterface;
use Modules\Notification\Repositories\Eloquents\NotificationEloquentRepository;

class NotificationServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerConfig();
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);

        $this->app->singleton(NotificationRepositoryInterface::class, fn() => new NotificationEloquentRepository(
            new DatabaseNotification
        ));
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            module_path('Notification', 'Config/config.php') => config_path('notification.php'),
        ], 'config');
        $this->mergeConfigFrom(
            module_path('Notification', 'Config/config.php'),
            'notification'
        );
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }
}
