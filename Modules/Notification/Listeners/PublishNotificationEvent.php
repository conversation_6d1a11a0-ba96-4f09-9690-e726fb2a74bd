<?php

namespace Modules\Notification\Listeners;

use App\Helpers\QueueHeplers;

class PublishNotificationEvent
{
    public function sendRabbitmq($event)
    {
        app(QueueHeplers::class)->createQueueRabbitmq($event);
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @param  Illuminate\Events\Dispatcher  $events
     * @return array
     */
    public function subscribe($events)
    {
        $events->listen(
            \Modules\Notification\Events\NotificationMarkAsSeen::class,
            'Modules\Notification\Listeners\PublishNotificationEvent@sendRabbitmq'
        );

        $events->listen(
            \Modules\Notification\Events\NotificationMarkAsRead::class,
            'Modules\Notification\Listeners\PublishNotificationEvent@sendRabbitmq'
        );
    }
}
