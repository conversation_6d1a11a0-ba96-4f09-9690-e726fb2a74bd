<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::middleware('auth:api')->get('/page', function (Request $request) {
//     return $request->user();
// });

Route::prefix('pages')->group(function () {
    Route::get('/{page}', 'ApiPageController@getPage');
    Route::post('/contact', 'ApiPageController@postContact');
    Route::post('/recruit', 'ApiPageController@postRecruit');
});
Route::get('/minigame-spin', 'ApiPageController@spin')->middleware('ensure_frontend_url');
Route::get('/minigame-gift', 'ApiPageController@getGiftMiniGame')->middleware('ensure_frontend_url');
