<?php

namespace Modules\Page\Notifications;

use App\Channels\Messages\TelegramMessage;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class NewRecruitSendToOperationNotification extends Notification
{
    use Queueable;

    /**
     * Get the notification's delivery channels.
     *
     * @return array
     */
    public function via(mixed $notifiable)
    {
        return [];
    }

    public function toTelegram($notifiable)
    {
        $receiver = config('telegram.receivers.customer_service');

        return TelegramMessage::create()
            ->to($receiver)
            ->content(
                '📢 New Recruit Register' . "\n\n" .
                    '📅 ' . now() . "\n" .
                    '🏢 Company: ' . $notifiable->company . "\n" .
                    '👤 Email: ' . $notifiable->email . "\n" .
                    '📱 Phone: ' . $notifiable->phone . "\n" .
                    '👤 Position:' . $notifiable->position
            );
    }
}
