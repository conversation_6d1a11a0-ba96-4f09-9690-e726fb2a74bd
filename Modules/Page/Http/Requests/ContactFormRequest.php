<?php

namespace Modules\Page\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ContactFormRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'fullname' => 'required',
            'company' => 'required',
            'phone' => 'required|numeric|min:10',
            'email' => 'required|email',
            'message' => 'required',
            // 'g-recaptcha-response' => 'required|captcha'
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Return custom messgaes.
     */
    public function messages()
    {
        return [

        ];
    }
}
