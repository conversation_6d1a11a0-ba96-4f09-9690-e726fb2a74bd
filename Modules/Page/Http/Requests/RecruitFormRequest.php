<?php

namespace Modules\Page\Http\Requests;

use DateTime;
use Illuminate\Foundation\Http\FormRequest;
use Modules\Page\Entities\Recruit;

class RecruitFormRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'company' => 'required',
            'name' => 'required',
            'email' => 'required|email',
            'phone' => 'required|numeric|min:10',
            // 'position' => 'required',
            // 'qty' => 'required',
            // 'year_exp' => 'required',
            // 'city_id' => 'required',
            'where_referral' => 'required | not_in:0',
            'description' => Recruit::TOPDEV_VMD_RECRUIT_SOURCE == $this->recruit_source ? '' : 'required',
            'qty' => 'required_if:recruit_source,' . Recruit::TALEN_SUCCESS_RECRUIT_SOURCE,
            'recruit_expected_date' => [
                function ($attribute, $value, $failsCallback) {
                    if ($this->recruit_source === Recruit::TALEN_SUCCESS_RECRUIT_SOURCE) {
                        if (empty($value)) {
                            $failsCallback($attribute . ' is required!');
                        } elseif (!$this->validateDate($value)) {
                            $failsCallback($attribute . ' is invalid format, it should be mm/dd/YYYY');
                        }
                    }
                },
            ],
            'company_website' => 'required_if:recruit_source,' . Recruit::TALEN_SUCCESS_RECRUIT_SOURCE,
        ];
    }

    private function validateDate($date, $format = 'Y-m-d')
    {
        $d = DateTime::createFromFormat($format, $date);

        return $d && $d->format($format) == $date;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}
