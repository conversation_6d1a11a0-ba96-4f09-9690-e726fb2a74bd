<?php

namespace Modules\Page\Http\Controllers\API;

use App\Jobs\SendContactMail;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Modules\Page\Entities\Recruit;
use Modules\Page\Http\Requests\ContactFormRequest;
use Modules\Page\Http\Requests\RecruitFormRequest;
use Modules\Page\Notifications\NewRecruitSendToOperationNotification;
use Modules\Page\Notifications\NewTalentSuccessRecruitSendToOperationNotification;
use Modules\Page\Services\GoogleSheet\VmdMiniGameSheet;
use Modules\Page\Services\GoogleSheet\VmdSurveySheet;
use Modules\Page\Services\RecaptchaService;
use Modules\Taxonomy\Entities\Taxonomy;

class ApiPageController extends Controller
{
    /**
     *  Display page with html.
     * @return Response`
     */
    public function getPage($pageName)
    {
        $skills = Taxonomy::with('term')->taxonomy('skills')->get()->map(function ($taxonomyItem) {
            $taxonomyItem->name = $taxonomyItem->term->name;

            return $taxonomyItem->only(['id', 'name']);
        });

        $path = module_path('Page', '/Resources/views/pages');
        $arrNames = [];
        $data = null;
        $files = \File::files($path);
        foreach ($files as $file) {
            $fileInfo = pathinfo((string) $file);
            $arrNames[] = str_replace('.blade', '', $fileInfo['filename']);
        }

        $response = ['error' => true, 'data' => $data, 'message' => 'Page ' . $pageName . ' not found'];

        if (in_array($pageName, $arrNames)) {
            if ($pageName == 'contact') {
                $data = config('page.contact');
            }

            if ($pageName == 'recruit') {
                $data = [
                    'skills' => $skills,
                    'recruit_referral' => config('page.where_referral'),
                    'recruit_package' => config('page.it_recruitment_packages'),
                ];
            }

            if ($pageName != 'contact' && $pageName != 'recruit') {
                $data = view('page::pages.' . $pageName, ['skills' => $skills])->render();
            }

            $response = ['error' => false, 'data' => $data, 'message' => 'Page ' . $pageName];
        }

        // return $data;
        return response()->json($response);
    }

    /**
     * Handle contact form.
     */
    public function postContact(ContactFormRequest $request)
    {
        $token = $request->input('token');
        $recaptchaService = app(RecaptchaService::class);
        $checkScore = $recaptchaService->verify($request, $token);

        if (!$checkScore) {
            $response = [
                'error' => true,
                'data' => [],
                'message' => 'Đăng ký không thành công!',
            ];
            return response()->json($response);
        }

        $data = [
            'fullname' => $request->fullname,
            'company' => $request->company,
            'email' => $request->email,
            'phone' => $request->phone,
            'message' => $request->message,
            'subject' => 'TopDev Contact',
        ];

        $response = [
            'error' => false,
            'data' => $data,
            'message' => 'Bạn đã gửi liên hệ thành công!',
        ];

        // Call Send mail
        dispatch(new SendContactMail('contact', $data));

        return response()->json($response);
    }

    /**
     * Handle recruit form.
     * */
    public function postRecruit(RecruitFormRequest $request)
    {
        $token = $request->input('token');
        $recaptchaService = app(RecaptchaService::class);
        $checkScore = $recaptchaService->verify($request, $token);

        if (!$checkScore) {
            $response = [
                'error' => true,
                'data' => [],
                'message' => 'Đăng ký không thành công!',
            ];
            return response()->json($response);
        }

        $salary_min = $request->salary_min;
        $salary_max = $request->salary_max;
        $salary = 'Negotiable';
        if ($salary_min != 0 && $salary_max != 0) {
            $salary = $salary_min . '-' . $salary_max;
        }

        $data = [
            'company' => $request->company,
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'skills' => $request->skill_str,
            'position' => $request->position,
            'recruit_package' => $request->it_recruitment_packages ? config('page.it_recruitment_packages')[$request->it_recruitment_packages] : '',
            'recruit_referral' => $request->where_referral ? config('page.where_referral')[$request->where_referral] : '',
            'year_exp' => $request->year_exp . ' year',
            'city_name' => $request->city_id,
            'salary' => $salary,
            'qty' => $request->qty,
            'description' => empty($request->description) ? '' : $request->description,
            'subject' => 'TopDev Recruit',
        ];

        // If has source then set it, otherwise is topdev.vn as default value
        if ($request->has('recruit_source')) {
            $data['recruit_source'] = $request->get('recruit_source');
        }

        $mailTemplate = 'recruit';
        $notifyCls = NewRecruitSendToOperationNotification::class;
        if ($request->get('recruit_source') === Recruit::TALEN_SUCCESS_RECRUIT_SOURCE) {
            $data = array_merge($data, [
                // New fields for talentSuccess.vn/recruit
                'company_website' => $request->company_website ?? null,
                'recruit_expected_date' => $request->recruit_expected_date ?? null,
                'subject' => 'TalentSuccess Recruit',
            ]);
            $mailTemplate = 'recruit_talent_success';
            $notifyCls = NewTalentSuccessRecruitSendToOperationNotification::class;
        }

        // New data for topde.vn/vmd/khao-sat
        if ($request->get('recruit_source') === Recruit::TOPDEV_VMD_RECRUIT_SOURCE) {
            //Get data google sheet
            $vmdSurveySheet = new VmdSurveySheet();
            $getRecruitExpectedDates = $request->recruit_expected_date;
            $data = array_merge($data, [
                'participant_Id' => generateBarcodeNumber() ?? null,
                'recruit_expected_dates' => $getRecruitExpectedDates ? json_encode([$getRecruitExpectedDates]) : null,
                'subject' => 'VMD Survey',
            ]);
            $mailTemplate = 'recruit_topde_vmd';
        }

        $newRecruit = Recruit::create($data);

        // Call Send mail
        if (isset($data['recruit_expected_date'])) {
            $data['recruit_expected_date'] = date_format(date_create($data['recruit_expected_date']), 'm/d/Y');
        }

        dispatch(new SendContactMail($mailTemplate, $data));

        if ($request->get('recruit_source') !== Recruit::TOPDEV_VMD_RECRUIT_SOURCE) {
            $newRecruit->notify(new $notifyCls());
        } else {
            try {
                $vmdSurveySheet->appendSurveyResult([[
                    $data['participant_Id'] ?? '',
                    $data['name'] ?? '',
                    $data['company'] ?? '',
                    $data['position'] ?? '',
                    $data['email'] ?? '',
                    $data['phone'] ?? '',
                    $data['skills'] ?? '',
                    $data['description'] ?? '',
                    $getRecruitExpectedDates ?? '',
                ]]);
            } catch (Exception $error) {
                Log::error($error->getTraceAsString());
            }
        }

        $response = [
            'error' => false,
            'data' => $data,
            'message' => 'Bạn đã đăng ký thành công!',
        ];

        return response()->json($response);
    }

    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return view('page::index');
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view('page::create');
    }

    /**
     * Store a newly created resource in storage.
     * @return Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show($id)
    {
        return view('page::show');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Response
     */
    public function edit($id)
    {
        return view('page::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param int $id
     * @return Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy($id)
    {
        //
    }

    public function getGiftMiniGame()
    {
        $getDataVmdSurveySheet = new VmdMiniGameSheet();
        $listGifts = $getDataVmdSurveySheet->getData()->filter(function ($value, $key) {
            unset($value['Ban đầu']);
            unset($value['Còn lại']);

            return $value;
        });

        return response()->json([
            'error' => false,
            'data' => $listGifts,
            'message' => 'Sucess',
        ]);
    }

    public function spin()
    {
        $getDataVmdSurveySheet = new VmdMiniGameSheet();
        $listGifts = $getDataVmdSurveySheet->getData()->filter(fn($value, $key) => $value['Còn lại'] > 0);
        if ($listGifts->isEmpty()) {
            return response()->json([
                'error' => true,
                'message' => 'Gift is empty!',
            ]);
        }
        $result = $listGifts->random();

        return response()->json([
            'error' => false,
            'data' => $result['ID'],
            'message' => 'Spin done!',
        ]);
    }
}
