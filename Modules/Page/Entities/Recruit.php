<?php

namespace Modules\Page\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class Recruit extends Model
{
    use Notifiable;
    protected $table = 'recruit';

    // public $timestamps = true;
    protected $fillable = [
        'company',
        'name',
        'email',
        'phone',
        'skills',
        'position',
        'recruit_package',
        'recruit_referral',
        'year_exp',
        'city_name',
        'salary',
        'qty',
        'description',
        'company_website',
        'recruit_expected_date',
        'recruit_source',
        'recruit_expected_dates',
    ];

    public const TALEN_SUCCESS_RECRUIT_SOURCE = 'talentsuccess.vn';
    public const TOPDEV_RECRUIT_SOURCE = 'talentsuccess.vn';
    public const TOPDEV_VMD_RECRUIT_SOURCE = 'topdev.vn/vmd';
}
