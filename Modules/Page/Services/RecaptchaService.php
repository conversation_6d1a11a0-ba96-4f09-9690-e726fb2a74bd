<?php

namespace Modules\Page\Services;

use Illuminate\Http\Request;
use ReCaptcha\ReCaptcha;

class RecaptchaService
{
    private $secret;
    private $enable;

    public function __construct()
    {
        $this->secret = config('services.google_recaptcha.secret_key');
        $this->enable = config('services.google_recaptcha.enable');
    }

    /**
     * @param Request $request
     * @param string $token
     * @return bool
     */
    public function verify(Request $request, string $token): bool
    {
        if (!$this->enable) {
            return true;
        }

        $recaptcha = new ReCaptcha($this->secret);

        return $recaptcha->setScoreThreshold(0.5)->verify($token, $request->ip())->isSuccess();

    }
}
