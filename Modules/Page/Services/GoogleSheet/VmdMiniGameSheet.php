<?php

namespace Modules\Page\Services\GoogleSheet;

use Revolution\Google\Sheets\Facades\Sheets;

class VmdMiniGameSheet
{
    private $spreadsheetId;
    private $sheetId;

    public function __construct()
    {
        $this->spreadsheetId = config('sheets.vmd.spreadsheet_id');
        $this->sheetId = config('sheets.vmd.minigame_sheet_id');
    }

    /**
     * Get data to google sheet.
     * @return toArray
     */
    public function getData()
    {
        $rows = Sheets::spreadsheet($this->spreadsheetId)->sheetById($this->sheetId)->range('A1:D9')->get();
        $header = $rows->pull(0);
        $values = Sheets::collection($header, $rows);

        return $values ?? null;
    }
}
