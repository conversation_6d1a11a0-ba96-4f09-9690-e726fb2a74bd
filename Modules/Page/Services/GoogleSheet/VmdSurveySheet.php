<?php

namespace Modules\Page\Services\GoogleSheet;

use Revolution\Google\Sheets\Facades\Sheets;

class VmdSurveySheet
{
    private $spreadsheetId;
    private $sheetId;

    public function __construct()
    {
        $this->spreadsheetId = config('sheets.vmd.spreadsheet_id');
        $this->sheetNane = config('sheets.vmd.sheet_nane');
    }

    // /**
    //  * Get data to google sheet
    //  * @return toArray
    //  */
    // public function getSurveyResults()
    // {
    //     $rows = Sheets::spreadsheet($this->spreadsheetId)->sheet($this->sheetNane)->get();
    //     $header = $rows->pull(0);
    //     $header[0] = str_replace("ID\n- Đối với cột ID: Hệ thống tự tạo 3 chữ số ( 100 ->999) tăng dần","id",$header[0]);
    //     $values = Sheets::collection($header, $rows);
    //     return $values->toArray() ?? null;
    // }

    /**
     * Perform importing data to google sheet.
     * @return void
     */
    public function appendSurveyResult($data)
    {
        Sheets::spreadsheet($this->spreadsheetId)
            ->sheet($this->sheetNane)
            ->append($data, 'USER_ENTERED');
    }
}
