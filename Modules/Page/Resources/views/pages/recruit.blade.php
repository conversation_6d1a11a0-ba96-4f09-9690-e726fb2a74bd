@extends('page::layouts.master')
@section('css')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
@endsection
@section('content')
    <section class="container contact">
        <div class="box box-md">
            <div class="row">
                <div class="col-lg-6 col-md-6 col-sm-6 offset-lg-3 offset-md-3 offset-sm-3 col-xs-12">
                    <h2>It’s our pleasure to support you recruiting your next awesome teammate.</h2>
                    <form class="form-custom form-submit" method="post" role="form">
                        {!! csrf_field() !!}
                        @if(Session::has('message'))
                            <div class="alert alert-success">
                                {{Session::get('message')}}
                            </div>
                        @endif
                        <div class="form-group">
                            <label>Your Fullname/Your Company</label>
                            <input type="text" class="form-control" name="company" id="" placeholder="Ex: Applancer JSC" value="{{ old('company') }}">
                        </div>
                        <div class="form-group row">
                            <div class="col-lg-6 col-md-6 col-xs-12">
                                <label>Your Email</label>
                                <input type="text" class="form-control" name="email" id="" placeholder="Ex: <EMAIL>" value="{{ old('email') }}">
                            </div>
                            <div class="col-lg-6 col-md-6 col-xs-12">
                                <label>Your Phone Number</label>
                                <input type="text" class="form-control" name="phone" id="" placeholder="08 1234567" value="{{ old('phone') }}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Which position you need support recruiting</label>
                            <input type="text" class="form-control" name="position" id="" placeholder="Ex: Senior Web Developers" value="{{ old('position') }}">
                        </div>
                        <div class="form-group">
                            <label>Please select the most important skills for this position</label>
                            <div class="textbox">
                                <select multiple="multiple" id="skill_ids" data-placeholder="All skills" data-search-input-placeholder="Search for a skill" class="select2-classic js-select2-classic" name="skill_ids[]">
                                    @foreach($skills as $skill)
                                        <option value='{!! $skill['id'] !!}'>{!! $skill['name'] !!}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>What kind of IT Recruitment Packages on TopDev do you prefer?</label>
                            <div class="textbox">
                                <select name="it_recruitment_packages" id="it_recruitment_packages" class="form-control">
                                    <option value="0"> -- Choose -- </option>
                                    <option value="1" 1=""> SMEs Package (From 1.590.000VND) </option>
                                    <option value="2" 1=""> Enterprise Package (From 2.240.000 VND) </option>
                                    <option value="3" 1=""> Ultimate Package </option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>How do you get to know TopDev as IT Recruitment Service?</label>
                            <div class="textbox">
                                <select name="where_referral" id="where_referral" class="form-control">
                                    <option value="0"> -- Choose -- </option>
                                    <option value="1" 1=""> Facebook / Fanpage TopDev </option>
                                    <option value="2" 1=""> LinkedIn </option>
                                    <option value="3" 1=""> Search </option>
                                    <option value="4" 1=""> Email </option>
                                    <option value="5" 1=""> Referral </option>
                                    <option value="6" 1=""> Newspapers </option>
                                    <option value="7" 1=""> Other </option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group row">
                            <div class="col-lg-6 col-md-6 col-xs-12">
                                <label>Experience Level</label>
                                <select name="year_exp" id="year_exp" class="form-control">
                                    <option value=""> -- Experience Level -- </option>
                                    <option value="0.5" 1=""> Fresh graduate </option>
                                    <option value="1" 1=""> 1 year </option>
                                    <option value="2" 1=""> 2 years </option>
                                    <option value="3" 1=""> 3 years </option>
                                    <option value="4" 1=""> 4 years </option>
                                    <option value="5" 1=""> 5 years </option>
                                    <option value="6" 1=""> 6 years </option>
                                    <option value="7" 1=""> 7 years </option>
                                    <option value="8" 1=""> 8 years </option>
                                    <option value="9" 1=""> 9 years </option>
                                    <option value="10" 1=""> Over 10 years </option>
                                </select>
                            </div>
                            <div class="col-lg-6 col-md-6 col-xs-12">   
                                <label>Location</label>
                                <select name="city_id" id="city_id" class="form-control">
                                    <option value=""> -- Location -- </option>
                                    <option value="27" 1=""> Ho Chi Minh </option>
                                    <option value="28" 1=""> Ha Noi </option>
                                    <option value="30" 1=""> Da Nang </option>
                                    <option value="31" 1=""> Can Tho </option>
                                    <option value="32" 1=""> Others </option>
                                    <option value="33" 1=""> Dong Nai </option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>How many headcounts you are recruiting?</label>
                            <input type="number" class="form-control" name="qty" id="" min="1" max="10" placeholder="Ex: 10" value="{{ old('qty', 1) }}">
                        </div>

                        <div class="form-group row">
                            <div class="col-lg-5 col-md-5 col-xs-12"> 
                                <label>Salary min</label> 
                                <div class="input-group">
                                    <div class="input-group-addon">$</div>
                                    <input type="text" class="form-control" name="salary_min" id="salary_min" placeholder="Salary min" value="{{ old('salary_min', 0) }}">
                                </div>
                            </div>
                            <div class="col-lg-5 col-md-5 col-xs-12">  
                                <label>Salary max</label> 
                                <div class="input-group">
                                    <div class="input-group-addon">$</div>
                                    <input type="text" class="form-control" name="salary_max" id="salary_max" placeholder="Salary max" value="{{ old('salary_max', 0) }}">
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-2 col-xs-12 custom_pd">  
                                <label style="position: relative;top:34px;">
                                <input type="checkbox" class="" name="is_negotiable" id="is_negotiable" value="{{ old('is_negotiable') }}"> Negotiable
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Please describe the job requirements & specifications</label> 
                            <textarea class="form-control" name="description" id="" cols="30" rows="10" placeholder="Please describe the job requirements & specifications">{{ old('description') }}</textarea>
                        </div>
                        <div class="form-group">
                        </div>
                        <button type="submit" name="form-action" class="btn btn2 btn-primary col-md-12 col-sm-12 col-xs-12">Submit</button>
                    </form>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('script')
@endsection
<style>
    .contact a.textunset.hotline {
        color: #e24b30;
    }
    .contact h4 {
        font-weight: 700;
        margin: 0;
        padding: 0;
    }

    .contact .h2, .contact h2 {
        font-size: 25px;
        font-weight: 300;
    }

    .contact h2 {
        margin-bottom: 10px;
    }
    .contact .btn-primary {
        color: #fff;
        background-color: #e34c31;
        border-color: #e34c31;
    }

    .contact .btn2 {
        padding: 10px 20px;
        font-size: 16px;
        border-radius: 0;
    }
    .contact .btn-primary:hover {
        color: #fff;
        background-color: #f5864e;
        border-color: #f5864e;
    } 

    .contact .input-group-addon {
        padding: 5px 10px;
        font-size: 13.5px;
        font-weight: 400;
        line-height: 25px;
        color: #777;
        text-align: center;
        background-color: #ddd;
        border: 1px solid #ddd;
        border-radius: 2px;
    } 
    .contact label {
        display: inline-block;
        max-width: 100%;
        margin-bottom: 5px;
        font-weight: 700;
    }
    .contact .form-control {
        -webkit-appearance: none;
        -webkit-box-shadow: none;
        box-shadow: none;
    }
    .contact .custom_pd{
        padding: 0;
    }
    input[type=color].form-control, input[type=date].form-control, input[type=datetime-local].form-control, input[type=datetime].form-control, input[type=email].form-control, input[type=month].form-control, input[type=number].form-control, input[type=password].form-control, input[type=search].form-control, input[type=tel].form-control, input[type=text].form-control, input[type=time].form-control, input[type=url].form-control, input[type=week].form-control, select.form-control, textarea.form-control {
        border-radius: 0;
        -webkit-border-radius: 0;
        -moz-border-radius: 0;
    }

    input[type=checkbox], input[type=radio] {
        margin: 4px 0 0;
        line-height: normal;
    }

    .component_content p, .component_content p span {
        font-size: 14px !important;
    }
</style>
