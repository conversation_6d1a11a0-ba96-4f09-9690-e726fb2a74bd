<?php

namespace Modules\Firebase\Http\Controllers\API;

use Illuminate\Routing\Controller;
use Modules\Firebase\Events\FirebaseDeviceToken;
use Modules\Firebase\Http\Requests\UpdateDeviceTokenRequest;
use Redmix0901\Core\Http\Responses\BaseHttpResponse;

class FirebaseController extends Controller
{
    public function updateDeviceToken(UpdateDeviceTokenRequest $request, BaseHttpResponse $response)
    {
        event(new FirebaseDeviceToken($request->user('api'), $request->all()));

        return $response->setMessage('Update Device Token Successfully!');
    }
}
