<?php

namespace Modules\Firebase\Events;

use App\Services\InJectRequestToEvent\BaseRequestEvent;
use Illuminate\Queue\SerializesModels;

class FirebaseDeviceToken extends BaseRequestEvent
{
    use SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(private $user, private $payload)
    {
        parent::__construct();
    }

    public function payload()
    {
        return $this->payload;
    }

    public function user()
    {
        return $this->user;
    }
}
