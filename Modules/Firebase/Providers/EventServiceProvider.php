<?php

namespace Modules\Firebase\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [];

    /**
     * Register the listeners for the subscriber.
     *
     * @param  Illuminate\Events\Dispatcher  $events
     */
    protected $subscribe = [
        \Modules\Firebase\Listeners\PublishFireBaseEvent::class,
    ];
}
