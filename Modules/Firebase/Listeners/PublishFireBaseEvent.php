<?php

namespace Modules\Firebase\Listeners;

use App\Helpers\QueueHeplers;

class PublishFireBaseEvent
{
    public function sendRabbitmq($event)
    {
        app(QueueHeplers::class)->createQueueRabbitmq($event);
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @param  Illuminate\Events\Dispatcher  $events
     * @return array
     */
    public function subscribe($events)
    {
        $events->listen(
            \Modules\Firebase\Events\FirebaseDeviceToken::class,
            'Modules\Firebase\Listeners\PublishFireBaseEvent@sendRabbitmq'
        );
    }
}
