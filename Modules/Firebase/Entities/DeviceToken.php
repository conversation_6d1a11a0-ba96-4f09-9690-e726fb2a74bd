<?php

namespace Modules\Firebase\Entities;

use Illuminate\Database\Eloquent\Model;
use <PERSON><PERSON>\Scout\Searchable;

class DeviceToken extends Model
{
    use Searchable;

    /**
     * @inheritdoc
     */
    protected $table = 'fcm_device_tokens';

    /**
     * @inheritdoc
     */
    protected $fillable = [
        'user_id', 'device_token', 'device_info', 'base_os', 'brand', 'os_build_id',
        'device_os_version', 'device_id', 'device_name', 'device_mac_address',
    ];

    /**
     * Get belongs to user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(\Modules\User\Entities\User::class);
    }

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'fcm_device_tokens_ams';
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        return [
            'id' => $this->getKey(),
            'user_id' => $this->user_id,
            'device_token' => $this->device_token,
            'device_info' => $this->device_info,
        ];
    }
}
