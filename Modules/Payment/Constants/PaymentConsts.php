<?php

namespace Modules\Payment\Constants;

class PaymentConsts
{
    // Megapay:
    // - IC: Thẻ tín dụng (Visa/master/JCB…)
    // - DC: Thẻ ATM nội địa
    // - EW: Ví điện tử nói chung (<PERSON>o)
    public const MEGAPAY_PAYMENT_METHOD_WALLET = 'EW';
    public const MEGAPAY_PAYMENT_METHOD_WALLET_MOMO = 'MOMO';
    public const MEGAPAY_PAYMENT_METHOD_ATM = 'DC';
    public const MEGAPAY_PAYMENT_METHOD_CC = 'IC';
    public const MEGAPAY_PAYMENT_LANG = 'VN';
    public const MEGAPAY_INQUIRY_SUCCESS_CODE = '00_000';
    public const MEGAPAY_INQUIRY_AWAITING_CODE = '99';
    public const MEGAPAY_INQUIRY_CUSTOMER_CANCELED_CODE = 'PG_ER5';
    public const MEGAPAY_INQUIRY_TRANSACTION_NOT_FOUND_CODE = 'OR_140';

    // Momo:
    // - payWithCC: Thẻ tín dụng (Visa/master/JCB…)
    // - payWithATM: Thẻ ATM nội địa
    // - captureWallet: Ví điện tử (Momo)
    public const MOMO_PAYMENT_METHOD_WALLET = 'captureWallet';
    public const MOMO_PAYMENT_METHOD_ATM = 'payWithATM';
    public const MOMO_PAYMENT_METHOD_CC = 'payWithCC';
    public const MOMO_PAYMENT_LANG = 'vi';

    public const RESPONSE_VIA_CALLBACK = 'callback';
    public const RESPONSE_VIA_IPN = 'ipn';

    /**
     * Get all payment methods for megapay
     */
    public static function megapayMethods()
    {
        return [
            self::MEGAPAY_PAYMENT_METHOD_WALLET,
            self::MEGAPAY_PAYMENT_METHOD_ATM,
            self::MEGAPAY_PAYMENT_METHOD_CC,
        ];
    }

    /**
     * Get all payment methods for megapay
     */
    public static function momoMethods()
    {
        return [
            self::MOMO_PAYMENT_METHOD_WALLET,
            self::MOMO_PAYMENT_METHOD_ATM,
            self::MOMO_PAYMENT_METHOD_CC,
        ];
    }
}
