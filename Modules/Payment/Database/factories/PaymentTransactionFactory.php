<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use Faker\Generator as Faker;
use Modules\Payment\Constants\PaymentConsts;
use Modules\Payment\Entities\PaymentTransactions;
use Modules\Payment\States\Transaction\Status\TransactionStatus;

$factory->define(PaymentTransactions::class, function (Faker $faker) {
    $driver = $faker->randomElement(['megapay', 'momo']);
    return [
        'order_id' => $faker->numberBetween(1, 10),
        'payment_method_id' => $faker->numberBetween(1, 6),
        'payment_method' =>  $faker->randomElement([
            PaymentConsts::MEGAPAY_PAYMENT_METHOD_ATM,
            PaymentConsts::MEGAPAY_PAYMENT_METHOD_CC,
            PaymentConsts::MEGAPAY_PAYMENT_METHOD_WALLET
        ]),
        'status' => $faker->randomElement([
            TransactionStatus::PAYMENT_STATUS_AWAITING_PAYMENT,
            TransactionStatus::PAYMENT_STATUS_PENDING,
            TransactionStatus::PAYMENT_STATUS_COMPLETED,
            TransactionStatus::PAYMENT_STATUS_CANCELLED,
            TransactionStatus::PAYMENT_STATUS_ERROR,
            TransactionStatus::PAYMENT_STATUS_REFUNDED,
        ]),
        'trans_code' => 'TC' . $faker->numberBetween(10000000, 99999999),
        'amount' => $faker->numberBetween(1000, 50000000),
        'order_code' => 'TOPDEV-' . $faker->numberBetween(100000000, 999999999),
        'order_info' => 'Thong tin don hang',
        'redirect_url' => route('payment.callback_url', ['type' => $driver]),
        'ipn_url' => route('payment.ipn_url', ['type' => $driver]),
        'pay_url' => null,
        'signature' => hash('sha256', 'TOPDEV-' . $faker->numberBetween(100000000, 999999999)),
    ];
});
