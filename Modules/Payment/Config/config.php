<?php

return [
    'name' => 'Payment',

    'fe_redirect' => [
        'page_not_found' => '/page-not-found',
        'order_not_found' => '/page-not-found',
        'order_result' => '/result/',
    ],

    'managers' => [
        'default_driver' => 'megapay'
    ],

    'megapay' => [
        'api_url' => env('PAYMENT_MEGAPAY_API_URL', 'https://sandbox.megapay.vn'),
        'connect_timeout' => 30,
        'mer_id' => env('PAYMENT_MEGAPAY_MER_ID', 'EPAY000001'),
        'encode_key' => env('PAYMENT_MEGAPAY_ENCODE_KEY', 'rf8whwaejNhJiQG2bsFubSzccfRc/iRYyGUn6SPmT6y/L7A2XABbu9y4GvCoSTOTpvJykFi6b1G0crU8et2O0Q=='),
        'payment_js_url' => env('PAYMENT_MEGAPAY_JS_URL', 'https://sandbox.megapay.vn/pg_was/js/payment/layer/paymentClient.js')
    ],

    'momo' => [
        'api_url' => env('PAYMENT_MOMO_API_URL', 'https://test-payment.momo.vn'),
        'connect_timeout' => 30,
        'partner_code' => env('PAYMENT_MOMO_PARTNER_CODE', ''),
        'access_key' => env('PAYMENT_MOMO_ACCESS_KEY', ''),
        'secret_key' => env('PAYMENT_MOMO_SECRET_KEY', ''),
        'public_key' => env('PAYMENT_MOMO_PUBLIC_KEY', ''),
    ],
];
