<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('payments')->group(function () {
    Route::get('callback/{type}', 'PaymentController@callback')->name('payment.callback_url');
    Route::post('ipn/{type}', 'PaymentController@ipn')->name('payment.ipn_url');
});
