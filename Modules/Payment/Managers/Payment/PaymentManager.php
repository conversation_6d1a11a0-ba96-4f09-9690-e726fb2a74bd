<?php

namespace Modules\Payment\Managers\Payment;

use Illuminate\Support\Manager;
use Modules\Payment\Entities\PaymentVendor;
use Modules\Payment\Managers\Payment\Drivers\Megapay;
use Modules\Payment\Managers\Payment\Drivers\Momo;
use Modules\Payment\Managers\Payment\Facades\Payment;

class PaymentManager extends Manager
{
    public const DRIVER_MEGAPAY = 'megapay';
    public const DRIVER_MOMO = 'momo';

    /**
     * @see Illuminate\Support\Manager createDriver
     */
    public function createMegapayDriver(): Megapay
    {
        return app(Megapay::class);
    }

    /**
     * @see Illuminate\Support\Manager createDriver
     */
    public function createMomoDriver(): Momo
    {
        return app(Momo::class);
    }

    /**
     * {@inheritdoc}
     */
    public function getDefaultDriver(): string
    {
        return config('payment.managers.default_driver');
    }

    public static function resolveDriverByMethod(string $code)
    {
        $paymentVendor = PaymentVendor::whereHas('paymentMethods', fn($query) => $query->whereCode($code)->isActive())->firstOrFail();
        return Payment::driver($paymentVendor->name);
    }
}
