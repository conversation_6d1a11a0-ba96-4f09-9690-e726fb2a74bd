<?php

namespace Modules\Payment\Managers\Payment\Executions;

use Carbon\Carbon;
use Modules\Payment\Entities\PaymentTransactions;
use Modules\Payment\Managers\Payment\Abstracts\PaymentAbstract;
use Modules\Payment\Managers\Payment\PaymentManager;
use Modules\Payment\Managers\Payment\Services\MegapayService;

class MegapayPaymentCreation extends PaymentAbstract
{

    /**
     * @return string megapay
     */
    protected function getCallbackType()
    {
        return PaymentManager::DRIVER_MEGAPAY;
    }

    /**
     * Create payment transaction
     *
     * @return PaymentTransactions
     */
    public function create(array $paymentData)
    {
        $megapayService = app(MegapayService::class);
        $createdAt = Carbon::now();
        /* @phpstan-ignore-next-line */
        $tranCode = $megapayService->tranCode();
        return $this->createPayment(array_merge($paymentData, [
            'trans_code' => $tranCode,
            'signature' => $megapayService->getPaymentToken($tranCode, $paymentData['amount'], $createdAt->getTimestamp()),
            // Need set it to retrieve to get timestamp of created_at when response to FE
            // @see \Modules\Payment\Transformers\MegapayOpenPaymentResource
            'created_at' => $createdAt,
            'updated_at' => $createdAt,
        ]));
    }
}
