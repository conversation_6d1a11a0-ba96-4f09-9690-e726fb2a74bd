<?php

namespace Modules\Payment\Managers\Payment\Executions;

use Carbon\Carbon;
use Modules\Payment\Entities\PaymentTransactions;
use Modules\Payment\Managers\Payment\Abstracts\PaymentAbstract;
use Modules\Payment\Managers\Payment\PaymentManager;
use Modules\Payment\Managers\Payment\Services\MomoService;

class MomoPaymentCreation extends PaymentAbstract
{
    /**
     * @return string megapay
     */
    protected function getCallbackType()
    {
        return PaymentManager::DRIVER_MOMO;
    }

    /**
     * Create payment transaction
     *
     * @return PaymentTransactions
     */
    public function create(array $paymentData)
    {
        $momoService = app(MomoService::class);
        $tranCode = $momoService->tranCode();
        $signature = $momoService->paymentMethodToken($paymentData['request_type'], $tranCode, $paymentData['order_code'], $paymentData['amount'], $paymentData['order_info']);

        $result = $momoService->createPaymentMethod($paymentData['request_type'], [
            'requestId' => $tranCode,
            'amount' => $paymentData['amount'],
            'orderId' => $paymentData['order_code'],
            'orderInfo' => $paymentData['order_info'],
            'extraData' => ''
        ], $signature);

        return $this->createPayment(array_merge($paymentData, [
            'trans_code' => $tranCode,
            'signature' => $signature,
            'pay_url' => $result['resultCode'] === 0 ? $result['payUrl'] : null,
        ]));
    }
}
