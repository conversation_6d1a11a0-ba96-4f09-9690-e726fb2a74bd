<?php

namespace Modules\Payment\Managers\Payment\Executions;

use Modules\Payment\Managers\Payment\Abstracts\PaymentResponseAbstract;

class MomoPaymentIpn extends PaymentResponseAbstract
{
    public function processPaymentResponse()
    {
    }

    public function isPending()
    {
    }

    public function isSuccess()
    {
    }

    public function isCancelled()
    {
    }

    public function isFailed()
    {
    }

    public function isTransactionExists($transactionCode)
    {
    }

    protected function getCode()
    {
    }

    protected function getMessage()
    {
    }

    protected function inquiry($transCode)
    {
    }

    protected function setInquiryResult($inquiryResult)
    {
    }

    protected function setInquiryData()
    {
    }

    protected function isInquirySuccess()
    {
    }

    protected function isInquiryDifferentIpnStatus($ipnResponseCode)
    {
    }

    protected function getInquiryStatus()
    {
    }

    protected function convertInquiryToResponse($paymentTransactionId)
    {
    }

    protected function setResponseResult($paymentTransactionId)
    {
    }

    public function convertResponseToInquiryResult($responseResult)
    {
    }
}
