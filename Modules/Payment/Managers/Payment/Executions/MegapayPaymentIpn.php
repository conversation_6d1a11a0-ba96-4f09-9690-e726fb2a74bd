<?php

namespace Modules\Payment\Managers\Payment\Executions;

use Illuminate\Support\Carbon;
use Modules\Payment\Constants\PaymentConsts;
use Modules\Payment\Entities\PaymentTransactionResponse;
use Modules\Payment\Managers\Payment\Abstracts\PaymentResponseAbstract;
use Modules\Payment\Managers\Payment\Drivers\Megapay;
use Modules\Payment\Managers\Payment\Services\MegapayService;

class MegapayPaymentIpn extends PaymentResponseAbstract
{
    /**
     * Handle logic update payment for IPN
     *
     * @return void
     */
    public function processPaymentResponse()
    {
        $this->updatePayment();
    }

    /**
     * Check payment transaction is pending or not
     *
     * @return bool
     */
    public function isPending()
    {
        return $this->inquiryData['resultCd'] == PaymentConsts::MEGAPAY_INQUIRY_AWAITING_CODE;
    }

    /**
     * Check payment transaction is success or not
     * 1. Check status
     * 2. Check same order with transaction or not
     * 3. Check same amount that user paid or not
     *
     * @return bool
     */
    public function isSuccess()
    {
        return $this->inquiryData['resultCd'] == PaymentConsts::MEGAPAY_INQUIRY_SUCCESS_CODE
            && $this->inquiryData['status'] == 0
            // Check invoice No
            && $this->inquiryData['invoiceNo'] == $this->paymentTrans->order_code
            // Check same amount paid
            && $this->inquiryData['amount'] == $this->paymentTrans->amount;
    }

    /**
     * Check failed status by response code OR success status of the isSuccess function
     *
     * @return bool
     */
    public function isFailed()
    {
        return !in_array($this->inquiryData['resultCd'], [PaymentConsts::MEGAPAY_INQUIRY_AWAITING_CODE, PaymentConsts::MEGAPAY_INQUIRY_SUCCESS_CODE])
            || !$this->isSuccess();
    }

    /**
     * Check cancelled status by specify response code
     *
     * @return bool
     */
    public function isCancelled()
    {
        return $this->isFailed() && PaymentConsts::MEGAPAY_INQUIRY_CUSTOMER_CANCELED_CODE == $this->inquiryData['resultCd'];
    }

    /**
     * Check response transaction code with db to make sure updating same transaction
     *
     * @param string $transactionCode
     * @return bool
     */
    public function isTransactionExists($transactionCode)
    {
        return $this->inquiryData['merTrxId'] === $transactionCode
            && $this->inquiryData['resultCd'] !== PaymentConsts::MEGAPAY_INQUIRY_TRANSACTION_NOT_FOUND_CODE;
    }

    /**
     * Take code from megapay inquiry data
     *
     * @return string
     */
    protected function getCode()
    {
        return $this->inquiryData['resultCd'];
    }

    /**
     * Take message from megapay inquiry data
     *
     * @return string
     */
    protected function getMessage()
    {
        return $this->inquiryData['resultMsg'];
    }

    /**
     * Call megapay to inquiry payment transactions
     *
     * @param string $transCode
     * @return mixed payment result
     */
    protected function inquiry($transCode)
    {
        return app(MegapayService::class)->inquiry($transCode);
    }

    public function setInquiryResult($inquiryResult)
    {
        $this->inquiryResult = $inquiryResult;
        return $this;
    }

    /**
     * Convert response result back to inquiry result to check later
     *
     * @param array $responseResult
     * @return MegapayPaymentIpn $this
     */
    public function convertResponseToInquiryResult($responseResult)
    {
        $this->inquiryResult = [
            'resultCd' => PaymentConsts::MEGAPAY_INQUIRY_SUCCESS_CODE,
            'data' => $responseResult,
        ];
        return $this;
    }

    public function setResponseResult($responseResult)
    {
        $this->responseResult = $responseResult;
        return $this;
    }

    public function setInquiryData()
    {
        $this->inquiryData = $this->inquiryResult['data'] ?? [];
        return $this;
    }

    protected function isInquirySuccess()
    {
        return $this->getInquiryStatus() == PaymentConsts::MEGAPAY_INQUIRY_SUCCESS_CODE;
    }

    protected function getInquiryStatus()
    {
        return $this->inquiryResult['resultCd'] ?? '';
    }

    protected function isInquiryDifferentIpnStatus($ipnResponseCode)
    {
        return ($this->inquiryData['resultCd'] ?? '') !== $ipnResponseCode;
    }

    /**
     * Convert inquiry data to response if inquiry data is different response data
     *
     * @param string $paymentTransactionId
     * @return array response data
     */
    protected function convertInquiryToResponse($paymentTransactionId)
    {
        $timeStamp = $this->inquiryData['timeStamp'];
        return array_merge(
            app(Megapay::class)->getValidated($this->inquiryData),
            [
                'transaction_type' => PaymentTransactionResponse::TYPE_INQUIRY,
                'payment_transaction_id' => $paymentTransactionId,
                'status' => $this->inquiryData['status'],
                'responsed_at' => $timeStamp ? Carbon::createFromTimestamp($timeStamp) : Carbon::now(),
                'response_via' => 'inquiry'
            ]
        );
    }
}
