<?php

namespace Modules\Payment\Managers\Payment\Drivers;

use Modules\Payment\Managers\Payment\Contracts\Payment;
use Modules\Payment\Managers\Payment\Validators\MomoCallbackValidator;

class Momo implements Payment
{
    public function createPayment(array $paymentData)
    {
        // TBD
    }

    public function processPaymentCallback(string $transCode, array $callbackData)
    {
        // TBD
    }

    public function processPaymentIpn(string $transCode, array $ipnData)
    {
        // TBD
    }

    public function processPaymentInquiry(string $transCode)
    {
        // TBD
    }

    /**
     * Get momo callback validator
     */
    public function callbackRules()
    {
        return MomoCallbackValidator::rules();
    }

    

    /**
     * Get momo callback validator
     */
    public function ipnRules()
    {
        return MomoCallbackValidator::rules();
    }

    /**
     * Mapping validated values from megapay to payment data table
     * @param array $validated validated value take from validation
     * @return array validated values after mapping
     */
    public function getValidated(array $validated)
    {
        return [];
    }
}
