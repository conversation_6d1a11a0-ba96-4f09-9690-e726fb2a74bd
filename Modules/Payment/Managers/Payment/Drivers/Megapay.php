<?php

namespace Modules\Payment\Managers\Payment\Drivers;

use Illuminate\Support\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Validator;
use Modules\Payment\Managers\Payment\Contracts\Payment;
use Modules\Payment\Managers\Payment\Executions\MegapayPaymentCallback;
use Modules\Payment\Managers\Payment\Executions\MegapayPaymentCreation;
use Modules\Payment\Managers\Payment\Executions\MegapayPaymentInquiry;
use Modules\Payment\Managers\Payment\Executions\MegapayPaymentIpn;
use Modules\Payment\Managers\Payment\Validators\MegapayCallbackValidator;
use Modules\Payment\Managers\Payment\Validators\MegapayIpnValidator;
use Modules\Payment\Managers\Payment\Validators\MegapayValidator;
use Modules\Payment\Transformers\MegapayOpenPaymentResource;

class Megapay implements Payment
{
    /**
     * Create payment for megapay
     * 1. Validate
     * 2. Create record in payment_transactions table
     * 3. Build response data that using resouce
     *
     * @param array $paymentData
     * @return MegapayOpenPaymentResource resource
     */
    public function createPayment(array $paymentData)
    {
        $validated = Validator::make($paymentData, MegapayValidator::rules())->validate();
        $paymentTrans = app(MegapayPaymentCreation::class)->create($validated);
        return MegapayOpenPaymentResource::make($paymentTrans);
    }

    /**
     * Handle callback data. Logic as the same with IPN but it contains failed response
     *
     * @param string $transCode
     * @param array $callbackData
     */
    public function processPaymentCallback(string $transCode, array $callbackData)
    {
        app(MegapayPaymentCallback::class)->processPaymentCallback($transCode, $callbackData);
    }

    /**
     * Hanlde logic to update order, transactions based on response status. Steps
     * 1. Store response to payment_transaction_responses table
     * 2. Call megapay inquiry API to make sure response is correct
     *    2.1 Check final status: success / failed: go to step 3
     *    2.2 Not final status yet:
     *        - Update order status to AWAITING_PAYMENT
     *        - Add queue to check next time?
     *        - End process
     * 3. Update order status based on final status
     * 4. Update payment_transactions
     *
     * @param string $transCode our transaction code
     * @param array $ipnData megapay data that are conveted to our format
     * @return void
     * @throws \Modules\Payment\Managers\Payment\Exceptions\PaymentIpnException
     */
    public function processPaymentIpn(string $transCode, array $ipnData)
    {
        app(MegapayPaymentIpn::class)->processPaymentIpn($transCode, $ipnData);
    }

    /**
     * Handling payment inquiry for megapay
     *
     * @param string $transCode
     * @return array
     */
    public function processPaymentInquiry(string $transCode)
    {
        return app(MegapayPaymentInquiry::class)->processPaymentInquiry($transCode);
    }

    /**
     * Get megapay callback validator
     *
     * @return array
     */
    public function callbackRules()
    {
        return MegapayCallbackValidator::rules();
    }

    /**
     * Get megapay callback validator
     */
    public function ipnRules()
    {
        return MegapayIpnValidator::rules();
    }

    /**
     * Mapping validated values from megapay to payment data table
     * @param array $validated validated value take from validation
     * @return array validated values after mapping
     */
    public function getValidated(array $validated)
    {
        $statusCode = Arr::get($validated, 'resultCd');
        $isCallback = request()->method() === 'GET';
        $transDt = Arr::get($validated, $isCallback ? 'transDt' : 'trxDt');
        $transTm = Arr::get($validated, $isCallback ? 'transTm' : 'trxTm');
        $timeStamp = Arr::get($validated, 'timeStamp');
        $paidAt = $transDt && $transTm ? date_create_from_format('YmdHis', $transDt.$transTm) : null;

        return [
            /**
             * Fields for callback and ipn
             */
            'trans_code' => Arr::get($validated, 'merTrxId'),
            'payment_method' => Arr::get($validated, 'payType'),
            'amount' => Arr::get($validated, 'amount'),
            'order_code' => Arr::get($validated, 'invoiceNo'),
            'order_info' => Arr::get($validated, 'description') . "\n" . Arr::get($validated, 'goodsNm'),
            'vendor_transaction_id' => Arr::get($validated, 'trxId'),
            'code' => $statusCode,
            'message' => Arr::get($validated, 'resultMsg'),
            'signature' => Arr::get($validated, 'merchantToken') ?? '',
            'responsed_at' => $timeStamp ? Carbon::createFromTimestamp($timeStamp) : Carbon::now(),
            'paid_at' => $paidAt ?: null,
            /**
             * Raw response to display later
             */
            'raw_response' => $validated,
            /**
             * Fields for ipn only
             */
            'status' => Arr::get($validated, 'status'),
        ];
    }
}
