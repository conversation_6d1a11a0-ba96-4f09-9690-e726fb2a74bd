<?php

namespace Modules\Payment\Managers\Payment\Validators;

use Modules\Payment\Rules\PaymentMethodIsActive;

class CreatePaymentValidator
{
    public static function rules()
    {
        return [
            'order_id' => ['required', 'int', 'exists:orders,id'],
            'amount' => ['required', 'int'],
            'request_type' => ['required', 'string'],
            'order_code' => ['required', 'string'],
            'order_info' => ['required', 'string'],
        ];
    }
}
