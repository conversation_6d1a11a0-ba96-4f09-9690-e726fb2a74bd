<?php

namespace Modules\Payment\Managers\Payment\Validators;

use Modules\Payment\Rules\MegapaySignatureValidRule;

class MegapayCallbackValidator
{
    public static function rules()
    {
        return [
            'resultCd' => ['required', 'string'],
            'resultMsg' => ['required', 'string'],
            'timeStamp' => ['required', 'string'],
            'merId' => ['required', 'in:' . config('payment.megapay.mer_id')],
            'merTrxId' => ['required', 'string', 'exists:payment_transactions,trans_code'],
            // 'trxId' => ['required', 'string'],
            'invoiceNo' => ['required', 'string'],
            'amount' => ['required', 'numeric'],
            // 'payType' => ['required', 'in:' . join(',', PaymentConsts::megapayMethods())],
            'merchantToken' => ['required_if:resultCd,00_000', new MegapaySignatureValidRule()],
            // 'transDt' => ['required', 'size:8'],
            // 'transTm' => ['required', 'size:6'],
        ];
    }
}
