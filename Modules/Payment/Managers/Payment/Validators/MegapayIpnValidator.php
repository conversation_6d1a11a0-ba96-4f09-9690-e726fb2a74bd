<?php

namespace Modules\Payment\Managers\Payment\Validators;

use Modules\Payment\Constants\PaymentConsts;
use Modules\Payment\Rules\IsPaymentTransactionReadyRule;
use Modules\Payment\Rules\MegapaySignatureValidRule;

class MegapayIpnValidator
{
    public static function rules()
    {
        return [
            'resultCd' => ['required', 'string'],
            'resultMsg' => ['required', 'string'],
            'timeStamp' => ['required', 'string'],
            'merId' => ['required', 'in:' . config('payment.megapay.mer_id')],
            'merTrxId' => ['required', 'string', new IsPaymentTransactionReadyRule()],
            'trxId' => ['required', 'string'],
            'invoiceNo' => ['required', 'string'],
            'amount' => ['required', 'numeric'],
            'payType' => ['required', 'in:' . join(',', PaymentConsts::megapayMethods())],
            'merchantToken' => ['required', 'string', new MegapaySignatureValidRule()],
            'status' => ['required', 'in:0'], // Currently, only alow payment type for megapay
            'trxDt' => ['required', 'size:8'],
            'trxTm' => ['required', 'size:6'],
        ];
    }
}
