<?php

namespace Modules\Payment\Managers\Payment\Validators;

use Modules\Payment\Managers\Payment\PaymentManager;
use Modules\Payment\Rules\PaymentMethodIsActive;

class MegapayValidator extends CreatePaymentValidator
{
    public static function rules()
    {
        return array_merge(
            parent::rules(),
            [
                'payment_method_id' => [
                    'required',
                    'int',
                    new PaymentMethodIsActive(PaymentManager::DRIVER_MEGAPAY)
                ],
            ]
        );
    }
}
