<?php

namespace Modules\Payment\Managers\Payment\Abstracts;

use Illuminate\Support\Facades\DB;
use Modules\Payment\Entities\PaymentTransactionResponse;
use Modules\Payment\Entities\PaymentTransactions;
use Modules\Payment\Managers\Payment\Exceptions\PaymentIpnException;
use Modules\Payment\Transformers\PaymentInquiryResource;
use Throwable;
use Illuminate\Support\Facades\Log;
use Modules\Order\States\Order\Status\Pending as OrderPending;
use Modules\Order\States\Order\Status\Cancelled as OrderCancelled;
use Modules\Order\States\Order\Status\Completed as OrderCompleted;
use Modules\Order\States\Order\Status\Error as OrderError;
use Modules\Payment\States\Transaction\Status\Pending;
use Modules\Payment\States\Transaction\Status\AwaitingPayment;
use Modules\Payment\States\Transaction\Status\Cancelled;
use Modules\Payment\States\Transaction\Status\Completed;
use Modules\Payment\States\Transaction\Status\Error;

abstract class PaymentResponseAbstract
{
    protected $inquiryResult = [];
    protected $responseResult = [];
    protected $inquiryData = [];
    protected $transReponseId;
    protected $paymentTrans;
    protected $paidAt;

    abstract public function processPaymentResponse();
    abstract public function isPending();
    abstract public function isSuccess();
    abstract public function isFailed();
    abstract public function isCancelled();
    abstract public function isTransactionExists($transactionCode);
    abstract protected function getCode();
    abstract protected function getMessage();
    abstract protected function inquiry($transCode);
    abstract protected function setInquiryResult($inquiryResult);
    abstract protected function setResponseResult($responseResult);
    abstract protected function setInquiryData();
    abstract protected function isInquirySuccess();
    abstract protected function isInquiryDifferentIpnStatus($ipnResponseCode);
    abstract protected function getInquiryStatus();
    abstract protected function convertInquiryToResponse($paymentTransactionId);
    abstract public function convertResponseToInquiryResult($responseResult);

    public function setTransResponseId($transReponseId)
    {
        $this->transReponseId = $transReponseId;
        return $this;
    }

    public function setPaymentTrans($paymentTrans)
    {
        $this->paymentTrans = $paymentTrans;
        return $this;
    }

    public function setPaidAt($paidAt)
    {
        $this->paidAt = $paidAt;
        return $this;
    }

    /**
     * Get transaction status based on response data
     *
     * @return string|null
     */
    protected function getTransactionStatus()
    {
        if ($this->isPending()) {
            return Pending::class;
        }

        if ($this->isSuccess()) {
            return Completed::class;
        }

        if ($this->isCancelled()) {
            return Cancelled::class;
        }

        // Final is error
        return Error::class;
    }

    /**
     * Get order status based on transaction status. For failed, need detect failed due to user cancels or not.
     *
     * @return string|null status of the order
     */
    protected function getOrderStatus()
    {
        if ($this->isPending()) {
            return OrderPending::class;
        }

        if ($this->isSuccess()) {
            return OrderCompleted::class;
        }

        if ($this->isCancelled()) {
            return OrderCancelled::class;
        }

        // Final is error
        return OrderError::class;
    }

    /**
     * Handle update data of transactions, order after
     */
    protected function updatePayment()
    {
        try {
            DB::beginTransaction();

            $this->paymentTrans->update([
                'last_payment_response_id' => $this->transReponseId,
                'paid_at' => $this->paidAt ?? null,
            ]);
            $paymentStatus = $this->getTransactionStatus();
            // Transition if not is the current status
            if (!$this->paymentTrans->status->equals($paymentStatus)) {
                $this->paymentTrans->status->transitionTo($paymentStatus);
                $this->paymentTrans->status->handle();
            }

            // Update order
            $orderStatus = $this->getOrderStatus();
            // Transition if not is the current status
            $order = $this->paymentTrans->order;
            if (!$order->status->equals($orderStatus)) {
                $order->update(['last_payment_transaction_id' => $this->paymentTrans->id]);
                $order->status->transitionTo($this->getOrderStatus());
                Log::info("PaymentResponseAbstract@updatePayment", ['status' => $orderStatus]);
                $order->status->handle();
            }

            DB::commit();
        } catch (Throwable $ex) {
            DB::rollBack();
            throw $ex;
        }
    }

    public function findTransaction($transCode, $isPending = false)
    {
        return PaymentTransactions::whereTransCode($transCode)
            ->allowUpdateStatus()
            ->when($isPending, fn ($query) => $query->whereState('status', AwaitingPayment::class))
            ->with('order')
            ->firstOrFail();
    }

    /**
     * Prepare response data based on vendor. Default is use input response data.
     *
     * @return array $responseData after prepare
     */
    protected function prepareResponseData(array $responseData)
    {
        return $responseData;
    }

    /**
     * Create transaction response
     *
     * @return int id of response data
     */
    public function createTransactionResponse(array $responseData)
    {
        return (PaymentTransactionResponse::create($this->prepareResponseData($responseData)))->id;
    }

    /**
     * Handle logic to store response data from ipn, callback
     *
     * @param int $transReponseId
     * @param PaymentTransactions $paymentTrans
     * @param array $responseData
     */
    private function saveResponseData($transReponseId, $paymentTrans, $responseData)
    {
        // Status ok from inquiry response
        if ($this->isInquirySuccess()) {
            $this->setInquiryData()
                ->setPaymentTrans($paymentTrans)
                ->setPaidAt($responseData['paid_at']);
            // If inquiry status is not the same with callback status.
            // We need convert inquiry to trans response
            if ($this->isInquiryDifferentIpnStatus($responseData['code'])) {
                $transReponseId = $this->createTransactionResponse($this->convertInquiryToResponse($paymentTrans->id));
            }
            $this->setTransResponseId($transReponseId)->processPaymentResponse();
        } else {
            throw new PaymentIpnException('Inquiry code not success ' . $this->getInquiryStatus());
        }
    }

    /**
     * Update payment transaction, order based on response response and inquiry data
     *
     * @param string $transCode transaction code
     * @param array $callbackData data response from vendor
     * @return void
     * @throws PaymentIpnException
     */
    public function processPaymentCallback(string $transCode, array $callbackData)
    {
        try {
            $paymentTrans = $this->findTransaction($transCode);
            // Set processed to avoid action callback/ipn at the same time if it is working
            $paymentTrans->setTransactionProcessed();
            // Step #1
            $callbackData['payment_transaction_id'] = $paymentTrans->id;
            $transReponseId = $this->createTransactionResponse($callbackData);
            // Convert to inquiry data to check later
            $this->convertResponseToInquiryResult($callbackData['raw_response']);
            $this->saveResponseData($transReponseId, $paymentTrans, $callbackData);
        } catch (Throwable $ex) {
            // Log for checking later, should not throw an exception due to this flow will redirect to FE
            Log::error($ex->getMessage());
            Log::error($ex->getTraceAsString());
        }
    }

    /**
     * Update payment transaction, order based on response response and inquiery data
     *
     * @param string $transCode transaction code
     * @param array $ipnData data response from vendor
     * @return void
     * @throws PaymentIpnException
     */
    public function processPaymentIpn(string $transCode, array $ipnData)
    {
        $paymentTrans = $this->findTransaction($transCode);
        // Set processed to avoid action callback/ipn at the same time if it is working
        $paymentTrans->setTransactionProcessed();
        // Step #1
        $ipnData['payment_transaction_id'] = $paymentTrans->id;
        $transReponseId = $this->createTransactionResponse($ipnData);
        $this->setResponseResult($ipnData);
        // Step #2
        $inquiryResult = $this->inquiry($transCode);
        $this->setInquiryResult($inquiryResult);
        $this->saveResponseData($transReponseId, $paymentTrans, $ipnData);
    }

    /**
     * Perform inquiry payment transaction that having pending status to get final status which are [success, error]
     *
     * @param string $transCode transaction code
     * @return array
     * @throws PaymentIpnException
     */
    public function processPaymentInquiry(string $transCode)
    {
        $paymentTrans = $this->findTransaction($transCode, true);
        // Step #2
        $inquiryResult = $this->inquiry($transCode);
        $this->setInquiryResult($inquiryResult);

        // Status ok from inquiry response
        if ($this->isInquirySuccess()) {
            $this->setInquiryData()->setPaymentTrans($paymentTrans);
            // If inquiry status is not pending and transaction is exists
            // then update latest status to the payment transaction
            if ($this->isTransactionExists($transCode) && !$this->isPending()) {
                $transReponseId = $this->createTransactionResponse($this->convertInquiryToResponse($paymentTrans->id));
                $this->setTransResponseId($transReponseId)->processPaymentResponse();

                // Retrieve latest data from db after update
                $paymentTrans->refresh();
            }
        }
        return PaymentInquiryResource::make($paymentTrans)->toArray();
    }
}
