<?php

namespace Modules\Payment\Managers\Payment\Abstracts;

use Modules\Order\Entities\Order;
use Modules\Payment\Entities\PaymentMethod;
use Modules\Payment\Entities\PaymentTransactions;
use Modules\Payment\States\Transaction\Status\TransactionStatus;

abstract class PaymentAbstract
{

    abstract protected function getCallbackType();

    /**
     * Create payment transactions
     *
     * @param array $paymentTransaction
     * @return PaymentTransactions
     */
    protected function createPayment($paymentTransaction)
    {
        $urlParams = ['type' => $this->getCallbackType()];
        return PaymentTransactions::create(array_merge(
            $paymentTransaction,
            [
                'payment_method' => PaymentMethod::whereId($paymentTransaction['payment_method_id'])->value('code'),
                'status' => TransactionStatus::PAYMENT_STATUS_AWAITING_PAYMENT,
                'redirect_url' => route('payment.callback_url', $urlParams),
                'ipn_url' => route('payment.ipn_url', $urlParams),
            ]
        ));
    }
}
