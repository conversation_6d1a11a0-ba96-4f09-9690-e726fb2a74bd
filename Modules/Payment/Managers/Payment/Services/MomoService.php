<?php

namespace Modules\Payment\Managers\Payment\Services;

use Exception;
use GuzzleHttp\Client;
use Modules\Payment\Constants\PaymentConsts;
use Modules\Payment\Managers\Payment\PaymentManager;
use Illuminate\Support\Str;

class MomoService
{
    /**
     * Get megapay transaction code by using uuid
     *
     * @return string transaction code
     */
    public function tranCode()
    {
        return strtoupper(Str::uuid()->toString());
    }

    protected function api($uri, $options, $method = 'post')
    {
        try {
            $client = new Client([
                'base_uri' => config('payment.momo.api_url'),
                'connect_timeout' => config('payment.momo.connect_timeout')
            ]);

            $response = $client->request($method, $uri, $options);
            return json_decode((string)$response->getBody(), true);
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    /**
     * Perform inquiry transaction when getting the code
     * @param string $transactionCode transaction code of the payment transaction
     * @param string $orderCode order code of the order
     * @return mixed payment result
     * @throws Exception error during call api
     */
    public function inquiry(string $transactionCode, string $orderCode)
    {
        $accessKey = config('payment.momo.api_url');
        $partnerCode = config('payment.momo.access_key');
        $secretKey = config('payment.momo.secret_key');
        $options = [
            'json' => [
                'partnerCode' => $partnerCode,
                'requestId' => $transactionCode,
                'orderId' => $orderCode,
                'lang' => PaymentConsts::MOMO_PAYMENT_LANG,
                'signature' => hash_hmac('sha256', join("&", [
                    "accessKey=$accessKey",
                    "orderId=$orderCode",
                    "partnerCode=$partnerCode",
                    "requestId=$transactionCode"
                ]), (string) $secretKey),
            ]
        ];
        return $this->api('/v2/gateway/api/query', $options);
    }

    /**
     * Get payment method token will be used for payment method request
     * @param string $requestType available values are: captureWallet, payWithCC, payWithATM
     * @param array $jsonArrayData data will be requested to momo
     * @param string $signature signed by sun set of data
     * @return mixed payment result
     * @throws Exception error during call api
     */
    public function createPaymentMethod(string $requestType, array $jsonArrayData, $signature)
    {
        return $this->api('/v2/gateway/api/create', [
            'json' => array_merge($jsonArrayData, [
                'requestType' => $requestType,
                'partnerCode' => config('payment.momo.partner_code'),
                'lang' => PaymentConsts::MOMO_PAYMENT_LANG,
                'ipnUrl' => route('payment.ipn_url', ['type' => PaymentManager::DRIVER_MOMO]),
                'redirectUrl' => route('payment.callback_url', ['type' => PaymentManager::DRIVER_MOMO]),
                'signature' => $signature,
            ])
        ]);
    }

    /**
     * Get payment method token will be used for payment method request
     * @param string $requestType available values are: captureWallet, payWithCC, payWithATM
     * @return string payment method token encrypted by hmac_sha256 algo
     */
    public function paymentMethodToken(string $requestType, string $transactionCode, string $orderCode, int $amount, string $orderInfo, string $extraData = "")
    {
        $hashString = join("&", [
            "accessKey=".config('payment.momo.access_key'),
            "amount=$amount",
            "extraData=$extraData",
            "ipnUrl=".route('payment.ipn_url', ['type' => PaymentManager::DRIVER_MOMO]),
            "orderId=$orderCode",
            "orderInfo=$orderInfo",
            "partnerCode=".config('payment.momo.partner_code'),
            "redirectUrl=".route('payment.callback_url', ['type' => PaymentManager::DRIVER_MOMO]),
            "requestId=$transactionCode",
            "requestType=$requestType"
        ]);

        return hash_hmac('sha256', $hashString, (string) config('payment.momo.secret_key'));
    }
}
