<?php

namespace Modules\Payment\Managers\Payment\Services;

use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Str;

class MegapayService
{
    /**
     * Get megapay transaction code by using uuid
     *
     * @return string transaction code
     */
    public function tranCode()
    {
        return config('payment.megapay.mer_id').strtoupper(Str::uuid()->getHex());
    }

    protected function api($uri, $options, $method = 'post')
    {
        $client = new Client([
            'base_uri' => config('payment.megapay.api_url'),
            'connect_timeout' => config('payment.megapay.connect_timeout')
        ]);

        $response = $client->request($method, $uri, $options);
        return json_decode((string)$response->getBody(), true);
    }

    /**
     * Perform inquiry transaction when getting the code
     *
     * @param string $transactionCode transaction code of the payment
     * @return mixed payment result
     * @throws Exception error during call api
     */
    public function inquiry(string $transactionCode)
    {
        $timeStamp = time();
        $options = [
            'form_params' => [
                'merId' => config('payment.megapay.mer_id'),
                'merTrxId' => $transactionCode,
                'merchantToken' => hash('sha256', $timeStamp.$transactionCode.config('payment.megapay.mer_id').config('payment.megapay.encode_key')),
                'timeStamp' => $timeStamp,
            ]
        ];
        return $this->api('/pg_was/order/trxStatus.do', $options);
    }

    /**
     * Get payment merchant token will be used for payment
     *
     * @param string $transactionCode string transaction code
     * @param int $amount amount of the payment
     * @param int $timestamp use for signed and send back to FE
     * @return string payment token encrypted by sha256 algo
     */
    public function getPaymentToken(string $transactionCode, int $amount, int $timestamp)
    {
        return hash('sha256', join('', [
            $timestamp,
            $transactionCode,
            config('payment.megapay.mer_id'),
            $amount,
            config('payment.megapay.encode_key')
        ]));
    }
}
