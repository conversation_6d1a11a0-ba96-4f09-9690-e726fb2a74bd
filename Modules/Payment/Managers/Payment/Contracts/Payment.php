<?php

namespace Modules\Payment\Managers\Payment\Contracts;

interface Payment
{
    public function createPayment(array $paymentData);
    public function processPaymentCallback(string $transCode, array $callbackData);
    public function processPaymentIpn(string $transCode, array $ipnData);
    public function processPaymentInquiry(string $transCode);
    public function callbackRules();
    public function ipnRules();
    public function getValidated(array $validated);
}
