<?php

namespace Modules\Payment\Managers\Payment\Facades;

use Illuminate\Support\Facades\Facade;
use Modules\Payment\Managers\Payment\PaymentManager;

/**
 * @method static \Modules\Payment\Managers\Payment\Contracts\Payment driver(string|null $driver)
 *
 * @see Modules\Payment\Managers\Payment\PaymentManager
 */
class Payment extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return PaymentManager::class;
    }
}
