<?php

namespace Modules\Payment\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\ValidationException;
use Modules\Payment\Managers\Payment\Facades\Payment;
use Modules\Payment\Rules\PaymentVendorExistsRule;
use Illuminate\Support\Facades\Log;
use Modules\Payment\Constants\PaymentConsts;

class PaymentCallbackValidator extends FormRequest
{

    public const RESPONSE_VIA_CALLBACK = 'callback';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array_merge(
            [
                'vendor_type' => ['required', new PaymentVendorExistsRule()]
            ],
            Payment::driver($this->vendor_type)->callbackRules()
        );
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $this->merge([
            'vendor_type' => $this->route('type'),
        ]);
    }

    /**
     * Override form request to map request as expected
     *
     * @return array
     */
    public function validated($key = null, $default = null)
    {
        return array_merge(
            Payment::driver($this->vendor_type)->getValidated(array_merge(
                $this->all(),
                [
                    // Megapay callback doesn't have this key so need set as 0
                    'status' => 0
                ]
            )),
            ['response_via' => PaymentConsts::RESPONSE_VIA_CALLBACK]
        );
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  Validator  $validator
     * @return void
     *
     * @throws ValidationException
     */
    protected function failedValidation(Validator $validator)
    {
        Log::debug(static::class.':'.__LINE__. json_encode($validator->errors()));
        Log::debug(static::class.':'.__LINE__."Data \n". json_encode($this->all()));
        throw new HttpResponseException(redirect()->away(config('app.frontend_url') . config('payment.fe_redirect.page_not_found')));
    }
}
