<?php

namespace Modules\Payment\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\Payment\Constants\PaymentConsts;
use Modules\Payment\Managers\Payment\Facades\Payment;
use Modules\Payment\Rules\PaymentVendorExistsRule;

class PaymentIpnValidator extends FormRequest
{
    public const RESPONSE_VIA_IPN = 'ipn';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array_merge(
            [
                'vendor_type' => ['required', new PaymentVendorExistsRule()]
            ],
            Payment::driver($this->vendor_type)->ipnRules()
        );
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $this->merge([
            'vendor_type' => $this->route('type'),
        ]);
    }

    /**
     * Override form request to map request as expected
     *
     * @return array
     */
    public function validated($key = null, $default = null)
    {
        return array_merge(Payment::driver($this->vendor_type)->getValidated($this->all()), ['response_via' => PaymentConsts::RESPONSE_VIA_IPN]);
    }
}
