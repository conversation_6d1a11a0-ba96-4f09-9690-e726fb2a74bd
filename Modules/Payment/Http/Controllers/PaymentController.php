<?php

namespace Modules\Payment\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Order\Entities\Order;
use Modules\Payment\Http\Requests\PaymentCallbackValidator;
use Modules\Payment\Http\Requests\PaymentIpnValidator;
use Modules\Payment\Managers\Payment\Facades\Payment;
use Modules\Payment\Managers\Payment\PaymentManager;
use Throwable;
use Illuminate\Support\Facades\Log;
use Modules\Payment\Entities\PaymentTransactions;

class PaymentController extends Controller
{
    /**
     * Handle callback from payment vendor
     * @param PaymentCallbackValidator $request validation response data based on vendor
     * @param string $type use for detecting driver in PaymentCallbackValidator class
     * @return \Illuminate\Http\RedirectResponse
     */
    public function callback(PaymentCallbackValidator $request, string $type = PaymentManager::DRIVER_MEGAPAY)
    {
        $redirectUrl = config('app.frontend_url');
        try {
            $transCode = $request->validated()['trans_code'];
            $order = Order::select('code')->whereHas('paymentTransactions', fn($query) => $query->whereTransCode($transCode))->firstOrFail();
            // If aready process, just return to FE for checking status instead
            if (PaymentTransactions::whereTransCode($transCode)->isReady()->doesntExist()) {
                return redirect()->away($redirectUrl . '/result/' . $order->code);
            }

            // If not processed, should handle callback data and then process this data
            $validated = $request->validated();
            Payment::driver($type)->processPaymentCallback($validated['trans_code'], $validated);

            return redirect()->away($redirectUrl . '/result/' . $order->code);
        } catch (Throwable $ex) {
            Log::error($ex->getMessage());
            Log::error($ex->getTraceAsString());
            $redirectUrl .= config('payment.fe_redirect.page_not_found');
            return redirect()->away($redirectUrl);
        }
    }

    /**
     * Handle ipn for both momo/megapay
     *
     * @param string $type only allow megapay, momo for now
     */
    public function ipn(PaymentIpnValidator $request, string $type = PaymentManager::DRIVER_MEGAPAY)
    {
        $validated = $request->validated();
        Payment::driver($type)->processPaymentIpn($validated['trans_code'], $validated);
    }
}
