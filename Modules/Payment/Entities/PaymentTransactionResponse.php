<?php

namespace Modules\Payment\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * Modules\Payment\Entities\PaymentTransactionResponse
 *
 * @property int $id
 * @property int $payment_transaction_id
 * @property string $payment_method
 * @property string $trans_code
 * @property int $amount
 * @property string $order_code
 * @property string $order_info
 * @property string $vendor_transaction_id
 * @property string $code
 * @property string $message
 * @property string|null $redirect_url
 * @property string $signature
 * @property \Illuminate\Support\Carbon|null $responsed_at
 * @property mixed $raw_response
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse query()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse whereMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse whereOrderCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse whereOrderInfo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse wherePaymentMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse wherePaymentTransactionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse whereRawResponse($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse whereRedirectUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse whereResponsedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse whereSignature($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse whereTransCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse whereVendorTransactionId($value)
 * @mixin \Eloquent
 * @property string $transaction_type
 * @property string|null $status
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse whereTransactionType($value)
 * @property string|null $response_via
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactionResponse whereResponseVia($value)
 */
class PaymentTransactionResponse extends Model
{
    public const TYPE_REQUEST_PAYMENT = 'request_payment';
    public const TYPE_PAYMENT = 'payment';
    public const TYPE_INQUIRY = 'inquiry';
    public const TYPE_REFUND = 'refund';

    protected $fillable = [
        'payment_transaction_id',
        'payment_method',
        'response_via',
        'trans_code',
        'amount',
        'order_code',
        'order_info',
        'vendor_transaction_id',
        'code',
        'message',
        'redirect_url',
        'signature',
        'responsed_at',
        'raw_response',
        'status',
    ];

    protected $casts = [
        'raw_response' => 'json',
        'responsed_at' => 'datetime',
    ];
}
