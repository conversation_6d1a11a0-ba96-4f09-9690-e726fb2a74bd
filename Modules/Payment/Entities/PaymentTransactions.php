<?php

namespace Modules\Payment\Entities;

use Illuminate\Database\Eloquent\Model;
use Modules\Order\Entities\Order;
use Modules\Payment\States\Transaction\Status\AwaitingPayment;
use Modules\Payment\States\Transaction\Status\Cancelled;
use Modules\Payment\States\Transaction\Status\Completed;
use Modules\Payment\States\Transaction\Status\Error;
use Modules\Payment\States\Transaction\Status\Pending;
use Modules\Payment\States\Transaction\Status\Refunded;
use Spatie\ModelStates\HasStates;
use Modules\Payment\States\Transaction\Status\TransactionStatus;

/**
 * Modules\Payment\Entities\PaymentTransactions
 *
 * @property int $id
 * @property int $order_id
 * @property int $payment_method_id
 * @property int $last_payment_response_id
 * @property TransactionStatus $status
 * @property string $payment_method
 * @property string $trans_code
 * @property int $amount
 * @property string $order_code
 * @property string $order_info
 * @property string $redirect_url
 * @property string $ipn_url
 * @property string|null $pay_url
 * @property string $signature
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions query()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions whereIpnUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions whereLastPaymentResponseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions whereNotState(string $column, $states)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions whereOrderCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions whereOrderInfo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions wherePayUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions wherePaymentMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions wherePaymentMethodId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions whereRedirectUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions whereSignature($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions whereState(string $column, $states)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions whereTransCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions whereUpdatedAt($value)
 * @mixin \Eloquent
 * @property \Illuminate\Support\Carbon|null $paid_at
 * @property string|null $processed_at
 * @property-read \Modules\Payment\Entities\PaymentTransactionResponse|null $latestTransactionResponse
 * @property-read Order $order
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions allowUpdateStatus()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions wherePaidAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions isReady()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentTransactions whereProcessedAt($value)
 * @property-read \Modules\Payment\Entities\PaymentMethod $paymentMethod
 */
class PaymentTransactions extends Model
{
    use HasStates;

    protected $fillable = [
        'order_id',
        'payment_method_id',
        'last_payment_response_id',
        'payment_method',
        'status',
        'trans_code',
        'amount',
        'order_code',
        'order_info',
        'redirect_url',
        'ipn_url',
        'pay_url',
        'signature',
        'paid_at',
        'processed_at',
    ];

    protected $casts = [
        'paid_at' => 'datetime',
        'status' => TransactionStatus::class
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    public function latestTransactionResponse()
    {
        return $this->hasOne(PaymentTransactionResponse::class, 'id', 'last_payment_response_id');
    }

    public function paymentMethod()
    {
        return $this->belongsTo(PaymentMethod::class, 'payment_method_id');
    }

    public function scopeAllowUpdateStatus($query)
    {
        $query->whereState('status', [AwaitingPayment::class, Pending::class, Completed::class, Error::class]);
    }

    public function isFinalStatus()
    {
        return in_array($this->status, [TransactionStatus::PAYMENT_STATUS_CANCELLED, TransactionStatus::PAYMENT_STATUS_COMPLETED, TransactionStatus::PAYMENT_STATUS_ERROR]);
    }

    /**
     * To avoid duplicate processs for callback/ipn should check before doing any action
     */
    public function scopeisReady($query)
    {
        $query->whereNull('processed_at');
    }

    /**
     * Update processed at before doing any action to make sure its not duplicate
     */
    public function setTransactionProcessed()
    {
        $this->update(['processed_at' => now()]);
    }
}
