<?php

namespace Modules\Payment\Entities;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Modules\Payment\Database\Factories\PaymentVendorFactory;

/**
 * Modules\Payment\Entities\PaymentVendor.
 *
 * @property int $id
 * @property string $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentVendor newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentVendor newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentVendor query()
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentVendor whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentVendor whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentVendor whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaymentVendor whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class PaymentVendor extends Model
{
    use HasFactory;
    protected $fillable = ['name'];

    public function paymentMethods()
    {
        return $this->hasMany(PaymentMethod::class, 'payment_vendor_id');
    }

    protected static function newFactory(): Factory
    {
        return PaymentVendorFactory::new();
    }
}
