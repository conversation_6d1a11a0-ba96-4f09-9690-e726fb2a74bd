<?php

namespace Modules\Payment\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Modules\Payment\Entities\PaymentMethod
 *
 * @property int $id
 * @property string $name
 * @property string $code
 * @property string $type
 * @property int $payment_vendor_id
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Modules\Payment\Entities\PaymentVendor $paymentVendor
 * @method static Builder|PaymentMethod isActive()
 * @method static Builder|PaymentMethod newModelQuery()
 * @method static Builder|PaymentMethod newQuery()
 * @method static Builder|PaymentMethod query()
 * @method static Builder|PaymentMethod whereCode($value)
 * @method static Builder|PaymentMethod whereCreatedAt($value)
 * @method static Builder|PaymentMethod whereId($value)
 * @method static Builder|PaymentMethod whereIsActive($value)
 * @method static Builder|PaymentMethod whereName($value)
 * @method static Builder|PaymentMethod wherePaymentVendorId($value)
 * @method static Builder|PaymentMethod whereType($value)
 * @method static Builder|PaymentMethod whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class PaymentMethod extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'code',
        'type',
        'payment_vendor_id',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    public function scopeIsActive(Builder $builder)
    {
        $builder->whereIsActive(true);
    }

    public function paymentVendor()
    {
        return $this->belongsTo(PaymentVendor::class, 'payment_vendor_id');
    }

    /**
     * Should get same method type(atm, cc, wallet) of the active method
     * To adapt admin switch vendor during checkout process
     *
     * @return PaymentMethod
     */
    public static function findActiveMethod(int $selectedMethodId)
    {
        return self::isActive()
            ->whereIn('type', fn($query) => $query->from('payment_methods')->select('type')->whereId($selectedMethodId))
            ->firstOrFail();
    }

    protected static function newFactory(): \Illuminate\Database\Eloquent\Factories\Factory
    {
        return \Modules\Payment\Database\Factories\PaymentMethodFactory::new();
    }
}
