<?php

namespace Modules\Activity\Campaigns;

use Illuminate\Support\Collection;

class JobRecommendLogin71 extends JobRecommend
{
    public function extraButtons(Collection $jobs)
    {
        $ids = $jobs->pluck('id')->all();
        $email = $this->getProgress()->getUser('email');

        return [
            'urlSaveJob' => token_jobs([], [], $email, $ids, 'EventSuggestJobs'),
            'urlSearch' => \frontend_url('it-jobs'),
            'urlCreateCvOnline' => taocv_url(),
        ];
    }
}
