<?php

namespace Modules\Activity\Campaigns;

class EventDownloadCvBuilder extends Campaign
{
    /**
     * Create a new campaign instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    public function toCampaignsArray()
    {
        return [
        ];
    }

    /**
     * Check campaign have condition.
     *
     * @return bool
     */
    public function shouldBePublishCampaign(): bool
    {
        return true;
    }

    /**
     * @return bool
     */
    public function shouldInterrupt()
    {
        return true;
    }
}
