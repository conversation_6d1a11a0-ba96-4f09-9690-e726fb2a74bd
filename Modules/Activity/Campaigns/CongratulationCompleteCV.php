<?php

namespace Modules\Activity\Campaigns;

class CongratulationCompleteCV extends Campaign
{
    /**
     * Create a new campaign instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    public function toCampaignsArray()
    {
        return [
            'cvprogress' => 100,
            'send_email_congrat_comple' => true,
            'cv' => $this->getProgress()->getCvbuilder()->getFullUrl(),
        ];
    }
}
