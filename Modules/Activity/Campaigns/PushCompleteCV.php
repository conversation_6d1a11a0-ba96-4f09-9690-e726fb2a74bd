<?php

namespace Modules\Activity\Campaigns;

class PushCompleteCV extends Campaign
{
    /**
     * Create a new campaign instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    public function toCampaignsArray()
    {
        return [
            'cvprogress' => $this->getProgress()->getProgressCvbuilder(),
            'send_email_push_complete' => true,
        ];
    }

    public function shouldInterrupt(): bool
    {
        $cvbuilder = $this->getProgress()->getCvbuilder()->refresh();

        $time = $cvbuilder->getUpdatedAt()->addSeconds($this->publishCampaignDelayQueue());

        \Log::info($time->toString());
        \Log::info(now()->toString());
        \Log::info($time > now());
        \Log::info($cvbuilder->completed());

        return $time > now() || $cvbuilder->completed() || $cvbuilder->notStartYet();
    }

    /**
     * Dispatch the job to make the given models searchable.
     *
     * @return mixed
     */
    public function publishCampaignDelayQueue()
    {
        return 120;
    }

    /**
     * Dispatch the job to make the given models searchable.
     *
     * @return mixed
     */
    public function publishCampaignUsingQueue()
    {
        return 'queue.processing.mautic.contact';
    }

    /**
     * Dispatch the job to make the given models searchable.
     *
     * @return mixed
     */
    public function publishCampaignUsingQueueDriver()
    {
        return 'rabbitmq';
    }
}
