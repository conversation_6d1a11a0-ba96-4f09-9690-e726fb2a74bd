<?php

namespace Modules\Activity\Campaigns;

use Illuminate\Support\Collection;

class JobRecommendCvCompleted71 extends JobRecommend
{
    public function extraButtons(Collection $jobs)
    {
        $email = $this->getProgress()->getUser('email');
        $ids = $jobs->pluck('id')->all();

        return [
            'urlSaveJob' => token_jobs([], [], $email, $ids, 'EventSuggestJobs'),
            'urlApplyJobs' => token_jobs([], [], $email, $ids, 'EventSuggestJobs'),
            'urlSearch' => \frontend_url('it-jobs'),
        ];
    }
}
