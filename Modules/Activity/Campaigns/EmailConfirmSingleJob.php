<?php

namespace Modules\Activity\Campaigns;

class EmailConfirmSingleJob extends Campaign
{
    /**
     * Create a new campaign instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    public function toCampaignsArray()
    {
        $job = $this->getProgress()->getRecentlyApply();

        return [
            'cv' => $this->getProgress()->getFilesCv(),
            'send_email_confirm_single' => true,
            'single_job_application' => $this->getSubtitleRecommend(),
        ];
    }

    public function shouldBePublishCampaign(): bool
    {
        $job = $this->getProgress()->getRecentlyApply();

        return !empty($job);
    }

    private function getSubtitleRecommend()
    {
        $jobs = $this->getProgress()->getUser()->recentlyApplyForJobs();

        return collect($jobs)->reject(fn($job) => empty($job['title'] ?? null))->map(fn($job) => $job['title'] . ' tại ' . ($job['company_name'] ?? null))->implode(', ');
    }
}
