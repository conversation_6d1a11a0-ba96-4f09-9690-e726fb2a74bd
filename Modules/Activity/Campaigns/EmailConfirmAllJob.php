<?php

namespace Modules\Activity\Campaigns;

class EmailConfirmAllJob extends Campaign
{
    /**
     * Create a new campaign instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    public function toCampaignsArray()
    {
        return [
            'cv' => $this->getProgress()->getFilesCv(),
            'send_email_confirm_apply' => true,
            'apply_all_auto_selected_j' => $this->getSubtitleRecommend(),
        ];
    }

    private function getSubtitleRecommend()
    {
        $jobs = $this->getProgress()->getUser()->recentlyApplyForJobs();

        return collect($jobs)->reject(fn($job) => empty($job['title'] ?? null))->map(fn($job) => $job['title'] . ' tại ' . ($job['company_name'] ?? null))->implode('&#13;');
    }
}
