<?php

namespace Modules\Activity\Campaigns;

class SavedJob24h extends Campaign
{
    public const PERIOD = 61;

    protected $suggestJobs;

    /**
     * Create a new campaign instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    public function toCampaignsArray()
    {
        $period = now()->subMinutes(self::PERIOD);
        $this->suggestJobs = $this->getProgress()->getFollowedJobs($period);

        return [
            'recjob' => $this->getBodyRecommend(),
        ];
    }

    public function shouldBePublishCampaign(): bool
    {
        $suggestJobs = $this->getProgress()->getFollowedJobs(
            now()->subMinutes(self::PERIOD)
        );

        return count($suggestJobs) > 0;
    }

    private function getBodyRecommend()
    {
        $suggestJobs = $this->suggestJobs;

        $urlSaveJob = token_jobs(
            collect([]),//$this->getProgress()->getSuggestSkills(),
            collect([]),//$this->getProgress()->getSuggestAreas(),
            $this->getProgress()->getUser('email'),
            collect($suggestJobs)->pluck('id')->all(),
            'EventSuggestJobs'
        );

        $urlApplyJobs = token_jobs(
            collect([]),//$this->getProgress()->getSuggestSkills(),
            collect([]),//$this->getProgress()->getSuggestAreas(),
            $this->getProgress()->getUser('email'),
            collect($suggestJobs)->pluck('id')->all(),
            'EventSuggestJobs'
        );

        $urlCreateCvOnline = taocv_url();

        return view('emails.content-to-recommend-job', compact(
            'suggestJobs',
            'urlApplyJobs',
            'urlCreateCvOnline'
        ))->render();
    }
}
