<?php

namespace Modules\Activity\Campaigns;

use Illuminate\Support\Collection;

class JobRecommend extends Campaign
{
    public function toCampaignsArray()
    {
        $jobs = collect($this->getProgress()->getSuggestJobs(true));

        return [
            'recjob' => $this->getBodyRecommend($jobs->take(5)),
            'subemail' => $this->getSubtitleRecommend($jobs->take(5)),
        ];
    }

    private function getBodyRecommend(Collection $jobs)
    {
        return view(
            'emails.content-to-recommend-job',
            array_merge([
                'suggestJobs' => $jobs->all(),
            ], $this->extraButtons($jobs))
        )->render();
    }

    private function getSubtitleRecommend(Collection $jobs)
    {
        $preSubtitle = $jobs->reject(fn($job) => empty($job['title'] ?? null))->first()['title'] ?? null;

        if ($jobs->count() == 1) {
            return $preSubtitle . ' việc làm phù hợp với bạn';
        }

        return $preSubtitle . ' và ' . ($jobs->count() - 1) . ' việc làm phù hợp với bạn';
    }

    public function extraButtons(Collection $jobs)
    {
        $ids = $jobs->pluck('id')->all();
        $email = $this->getProgress()->getUser('email');

        return [
            'urlSaveJob' => token_jobs([], [], $email, $ids, 'EventSuggestJobs'),
            'urlApplyJobs' => token_jobs([], [], $email, $ids, 'EventSuggestJobs'),
            'urlCreateCvOnline' => taocv_url(),
            //'urlSearch' => 'asdas'
        ];
    }
}
