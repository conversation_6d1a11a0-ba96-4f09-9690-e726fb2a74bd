<?php

namespace Modules\Activity\Campaigns;

use Modules\Activity\Contracts\CampaignContract;
use Modules\Activity\Contracts\CanPublishCampaign;
use Modules\Activity\Events\PublishCampaignCompleted;
use Modules\Activity\Events\PublishCampaignIncompleted;
use Modules\Activity\Events\PublishCampaignStarting;
use Modules\Activity\Jobs\MakePublishCampaign;
use Modules\Activity\Services\EmailMarketingManager;
use Modules\Activity\Services\ProgressManager;

abstract class Campaign implements CampaignContract, CanPublishCampaign
{
    protected $provider;

    protected $errors;

    protected $progress;

    /**
     * Set provider.
     *
     * @return mixed
     */
    public function setProvider($provider)
    {
        $this->provider = $provider;

        return $this;
    }

    /**
     * Set progress.
     *
     * @return mixed
     */
    public function setProgress(ProgressManager $progress)
    {
        $this->progress = $progress;

        return $this;
    }

    /**
     * Get progress.
     *
     * @return ProgressManager
     */
    public function getProgress(): ProgressManager
    {
        return $this->progress;
    }

    /**
     * Set errors.
     *
     * @return self
     */
    public function setErrors($errors)
    {
        $this->errors = $errors;

        return $this;
    }

    /**
     * Get errors.
     *
     * @return mixed
     */
    public function getErrors()
    {
        return $this->errors;
    }

    public function shouldInterrupt()
    {
        return false;
    }

    /**
     * Dispatch the job to make the given models searchable.
     *
     * @return void
     */
    public function publishCampaign($provider = null)
    {
        event(new PublishCampaignStarting($this));

        if (!$this->publishCampaignUsingQueueDriver()) {

            if ($this->shouldInterrupt()) {
                return;
            }

            $response = $this->publishCampaignUsing()->send($this);

            if ($response) {
                event(new PublishCampaignCompleted($this));
            } else {
                event(new PublishCampaignIncompleted($this));
            }

            return;
        }

        dispatch((new MakePublishCampaign($this))
                ->delay($this->publishCampaignDelayQueue())
                ->onConnection(config('queue.ams')));
    }

    /**
     * Dispatch the job to make the given models searchable.
     *
     * @return mixed
     */
    public function publishCampaignDelayQueue()
    {
        return 0;
    }

    /**
     * Dispatch the job to make the given models searchable.
     *
     * @return mixed
     */
    public function publishCampaignUsingQueue()
    {
        return 'default';
    }

    /**
     * Dispatch the job to make the given models searchable.
     *
     * @return mixed
     */
    public function publishCampaignUsingQueueDriver()
    {
        return false;
    }

    /**
     * Dispatch the job to make the given models searchable.
     *
     * @return mixed
     */
    public function publishCampaignUsing()
    {
        $provider = $this->provider;

        if (empty($provider)) {
            $provider = config('activity.email_marketing');
        }

        return app(EmailMarketingManager::class)->driver($provider);
    }

    /**
     * Check campaign have condition.
     *
     * @return bool
     */
    public function shouldBePublishCampaign(): bool
    {
        return true;
    }

    /**
     * @return array
     */
    public function toArray()
    {
        $profile = $this->getProgress()->getUser()->getProfile();

        return array_merge(
            $this->toCampaignsArray(),
            array_intersect_key(
                $profile,
                array_flip(['firstname', 'email', 'phone'])
            )
        );
    }

    /**
     * @return mixed
     */
    abstract public function toCampaignsArray();
}
