<?php

namespace Modules\Activity\Campaigns;

class ClickApply24h extends Campaign
{
    public const PERIOD = 61;

    protected $suggestJobs;

    /**
     * Create a new campaign instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    public function toCampaignsArray()
    {
        $this->suggestJobs = $this->getProgress()->getJobsClickApply(
            now()->subMinutes(self::PERIOD)
        );

        return [
            'recjob' => $this->getBodyRecommend(),
            'subject_email_click_apply' => $this->getSubtitleRecommend(),
        ];
    }

    public function shouldBePublishCampaign(): bool
    {
        $suggestJobs = $this->getProgress()->getJobsClickApply(
            now()->subMinutes(self::PERIOD)
        );

        return count($suggestJobs) > 0;
    }

    private function getBodyRecommend()
    {
        $suggestJobs = $this->suggestJobs;

        $urlSaveJob = token_jobs(
            collect([]),//$this->getProgress()->getSuggestSkills(),
            collect([]),//$this->getProgress()->getSuggestAreas(),
            $this->getProgress()->getUser('email'),
            collect($suggestJobs)->pluck('id')->all(),
            'EventSuggestJobs'
        );

        $urlApplyJobs = token_jobs(
            collect([]),//$this->getProgress()->getSuggestSkills(),
            collect([]),//$this->getProgress()->getSuggestAreas(),
            $this->getProgress()->getUser('email'),
            collect($suggestJobs)->pluck('id')->all(),
            'EventSuggestJobs'
        );

        $urlCreateCvOnline = taocv_url();

        return view('emails.content-to-recommend-job', compact(
            'suggestJobs',
            'urlSaveJob',
            'urlApplyJobs',
            'urlCreateCvOnline'
        ))->render();
    }

    private function getSubtitleRecommend()
    {
        $suggestJobs = $this->suggestJobs;

        $first = collect($suggestJobs)->reject(fn($job) => empty($job['title'] ?? null))->first();

        return ($first['title'] ?? null) . ' tại ' . ($first['company_name'] ?? null);
    }
}
