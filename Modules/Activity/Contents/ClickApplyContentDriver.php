<?php

namespace Modules\Activity\Contents;

class ClickApplyContentDriver extends Content
{
    public const DETAIL_JOB_URI = 'detail-jobs/';

    public function getSlug()
    {
        return self::DETAIL_JOB_URI
                . $this->model->jobs->first()->slug
                . '-' . $this->model->jobs->first()->getKey();
    }

    public function getContent()
    {
        return $this->model->jobs->first()->title;
    }
}
