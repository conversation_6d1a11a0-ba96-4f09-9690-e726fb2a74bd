<?php

namespace Modules\Activity\Contents;

use Str;

class SearchContentDriver extends Content
{
    public const GLUE = '-';
    public const DELIMITERS = '+';
    public const KEY_AREA_FRONTEND = 'l';
    public const KEY_TAXONOMY_FRONTEND = 't';
    public const KEY_KEYWORD_FRONTEND = 'k';

    public function getSlug()
    {
        return $this->makingKeywordQueries() . $this->makingTaxonomyQueries() . $this->makingAreaQueries();
    }

    public function getContent($space = ' ')
    {
        $merged = $this->viewables()->map->traceActivityBy();

        $content = Str::finish(
            Str::start(
                $merged->implode(' '),
                Str::finish($this->model->content, $space)
            ),
            ' ' . $this->model->area->name
        );

        return trim((string) $content);
    }

    private function makingAreaQueries()
    {
        if (empty($this->model->area_id)) {
            return null;
        }

        return Str::start($this->model->area_id, self::KEY_AREA_FRONTEND);
    }

    private function makingTaxonomyQueries()
    {
        $merged = $this->viewables()->pluck('id');

        if ($merged->isEmpty()) {
            return null;
        }

        return Str::start($merged->implode(','), self::KEY_TAXONOMY_FRONTEND);
    }

    private function makingKeywordQueries()
    {
        $flag = Str::random(10);
        $keyword = $this->getContent($flag);
        $queries = Str::replaceFirst(
            strtolower((string) $flag),
            self::DELIMITERS,
            str_replace(' ', '-', trim(strtolower((string) $keyword)))
        );

        $queries = trim((string) $queries, self::DELIMITERS);
        $queries = Str::finish(
            $queries,
            Str::start(self::KEY_KEYWORD_FRONTEND, self::GLUE)
        );
        $queries = trim(trim((string) $queries, self::DELIMITERS), self::GLUE);

        return Str::is($queries, self::KEY_KEYWORD_FRONTEND) ? null : $queries;
    }

    private function viewables()
    {
        return $this->model->jobs
            ->merge($this->model->skills)
            ->merge($this->model->companies);
    }
}
