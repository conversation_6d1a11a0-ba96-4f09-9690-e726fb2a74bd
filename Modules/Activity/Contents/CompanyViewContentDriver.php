<?php

namespace Modules\Activity\Contents;

class CompanyViewContentDriver extends Content
{
    public const DETAIL_COMPANY_URI = 'companies/';

    public function getSlug()
    {
        return self::DETAIL_COMPANY_URI
                . $this->model->companies->first()->slug
                . '-' . $this->model->companies->first()->getKey();
    }

    public function getContent()
    {
        return $this->model->companies->first()->display_name;
    }
}
