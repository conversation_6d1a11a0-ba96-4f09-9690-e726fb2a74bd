<?php

namespace Modules\Activity\Http\Middleware;

use Closure;
use Modules\Activity\Events\EventRepuestProcessed;

class PerformAutomationMiddleware
{
    public function handle($request, Closure $next)
    {
        $response = $next($request);

        if (auth('api')->check()) {
            // @phpstan-ignore-next-line
            event(new EventRepuestProcessed(auth('api')->user(), $request->all()));
        }

        return $response;
    }
}
