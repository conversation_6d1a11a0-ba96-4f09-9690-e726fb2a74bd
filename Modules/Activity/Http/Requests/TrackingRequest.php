<?php

namespace Modules\Activity\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class TrackingRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'job_ids' => 'nullable|regex:' . config('validate.params.ids'),
            'company_ids' => 'nullable|regex:' . config('validate.params.ids'),
            'collection'  => 'nullable|string',
            'tracking_variant'  => 'nullable|in:forced-login,non-forced-login',
        ];
    }

    public function messages()
    {
        return [
            'job_ids.regex' => "The format of field job_ids isn't correct?",
            'company_ids.regex' => "The format of field company_ids isn't correct?",
        ];
    }
}
