<?php

namespace Modules\Activity\Http\Controllers\API;

use App\Helpers\QueueHeplers;
use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Modules\Activity\Http\Requests\MauticWebhookRequest;
use Modules\Activity\Http\Requests\ProgressRequest;
use Modules\Activity\Http\Requests\TrackingRequest;
use Modules\Activity\Jobs\WebhookUpdateDataMautic;
use Modules\Activity\Services\ProgressManager;
use Modules\Company\Repositories\Contracts\CompanyRepositoryInterface;
use Modules\Job\Repositories\Contracts\CandidateRepositoryInterface;
use Modules\Job\Repositories\Contracts\JobRepositoryInterface;
use Modules\User\Entities\User;
use Modules\User\Repositories\Contracts\UserRepositoryInterface;
use Modules\User\Repositories\Contracts\UserResumeRepositoryInterface;
use Redmix0901\Core\Http\Responses\BaseHttpResponse;
use Redmix0901\ElasticResource\ElasticsearchPaginator;

class ProgressController extends Controller
{
    public function __construct(private readonly UserRepositoryInterface $userRepository)
    {
    }

    /**
     * @return BaseHttpResponse
     */
    public function events(ProgressRequest $request, BaseHttpResponse $response)
    {
        $data = null;

        try {
            if (!empty($request->token)) {
                $data = decrypt(urldecode((string) $request->token));

                if (empty($data['uuid']) || empty($data['job_ids']) || empty($data['event'])) {
                    return $response->setError()
                        ->setMessage('Token is invalid');
                }
            }
        } catch (DecryptException) {
            return $response->setError()
                ->setMessage('Token is invalid');
        }

        if (auth('api')->check()) {
            $user = auth('api')->user()->only('id', 'email', 'uuid');
        }

        if (isset($data['uuid'])) {
            $user = ['uuid' => $data['uuid']];
        }

        if (empty($user)) {
            return $response->setError()
                ->setMessage('Token is invalid');
        }

        $payload = array_merge($request->only('force_event', 'recently_apply', 'resume_id', 'origin', 'force_recently_apply', 'suggest_size'), array_filter([
            'job_ids' => $data['job_ids'] ?? null,
            'force_event' => $data['event'] ?? null,
        ]));

        $progress = ProgressManager::create($payload, $user);

        // if (!in_array(ProgressManager::EVENT_SUGGEST_JOBS, $progress->expectEvents())) {
        //     return [];
        // }

        $candidate = User::whereUuid($user['uuid'])->first()->candidates()->first();

        return $response->setCode(200)->setData([
            'user' => $progress->getProfile(),
            'events' => $progress->expectEvents(),
            'suggest_jobs' => $progress->getSuggestJobs(),
            'recently_apply' => $progress->getRecentlyApply(),
            'suggest_skills' => $progress->getSuggestSkills(5)->all(),
            'suggest_areas' => $progress->getSuggestAreas(2)->all(),
            'skills' => $progress->getBestSkills()->all(),
            'cvbuilder' => $progress->getProgressing(),
            'applied_ids' => $progress->getUser()->resolveAllApplyId()->all(),
            'attached_resumes' => $this->attachedResumes($user),
            'recently_used_file' => $candidate->cvbuilder_id ?? ($candidate->media_id ?? null),
        ]);
    }

    public function webhook(MauticWebhookRequest $request)
    {
        $user = $this->userRepository->searchOnElasticsearch(null, [
            'email' => $request->email,
        ]);
        $user = $this->resolveUser($user);

        if (empty($user)) {
            throw (new ModelNotFoundException)->setModel(
                $this->userRepository->getModel()::class,
                $request->email
            );
        }

        WebhookUpdateDataMautic::dispatch(
            array_merge($request->only('key', 'force_event', 'recently_apply', 'files_cv'), [
                'force_recently_apply' => true,
            ]),
            $user
        );
    }

    public function expectCampaigns(Request $request, BaseHttpResponse $response)
    {
        $user = auth('api')->user();

        return $response->setCode(200)->setData([
            'campaigns' => [
                [
                    'title' => [
                        'text' => 'Receive notifications ablout new jobs?',
                        'color' => '#fff',
                    ],
                    'body' => [
                        'text' => 'Tired of looking for work? Create job alerts to view daily latest jobs',
                        'background_color' => '#0097ff',
                        'color'=> '#fff',
                    ],
                    'action_button' => [
                        'type' => 'LOGIN',
                        'text' => 'LOGIN TO GET NOTIFICATION',
                        'background_color' => '#fff',
                        'color' => '#666666',
                        'queries' => [
                            'id' => 10,
                        ],
                    ],
                    'campaign_expect' => 'Login to get notifications',

                ],
            ],
        ]);
    }

    private function resolveUser($user, $defaults = [])
    {
        $user = $user['hits']['hits'][0]['_source'] ?? [];

        return array_merge($user, $defaults);
    }

    public function attachedResumes($user)
    {
        if (!isset($user['uuid'])) {
            return [];
        }

        if (!$user = User::whereUuid($user['uuid'])->first()) {
            return [];
        }

        try {
            $userResume = app(UserResumeRepositoryInterface::class)->searchOnElasticsearch(
                null,
                [
                    'email' => $user->email,
                    'page_size' => 10000,
                ]
            );
        } catch (\Throwable) {
            $userResume = null;
        }
        $meta = $userResume['hits']['hits'] ?? [];

        $size = 10;
        $page = 1;

        $dataAms = $user->getAttachedResume();
        $dataCvbuilder = ($this->getFromCvbuilder($user, $meta));
        $allData = array_merge(
            $dataAms,
            $dataCvbuilder
        );
        $countAlldata = count($allData);
        $ordering = (isset(request()->ordering) && request()->ordering == 'updated') ? 'updated_at' : 'created_at';
        $allData = collect($allData)->sortByDesc(fn($col) => strtotime((string) $col[$ordering]))->values()->forPage($page, $size)->all();

        $data = (new ElasticsearchPaginator($allData, null, $countAlldata, $size, $page, ['path' => ElasticsearchPaginator::resolveCurrentPath()]));

        return new ResourceCollection($data);
    }

    public function getFromCvbuilder($user, $meta)
    {
        $collectionName = 'UploadFromTopdev';
        $data = collect($meta)->map(function ($e) use ($user, $collectionName) {
            $e = $e['_source'];
            $features = $user->getFeatures();
            $features['edit'] = true;
            $features['apply'] = false;
            if (!empty($e['progress_value']) && $e['progress_value'] == '100') {
                $features['apply'] = true;
            }

            $candidates = app(CandidateRepositoryInterface::class)->allBy(
                [
                    'cvbuilder_id' => $e['resume_id'],
                ],
                [],
                ['id', 'job_id', 'created_at']
            )->all();

            $data = [];
            if (!empty($candidates)) {
                $features['edit'] = false;
                $features['delete'] = false;
                $features['duplicate'] = true;

                $data = collect($candidates)->map(function ($candidate) {
                    $candidate['applied_at'] = Carbon::createFromFormat('Y-m-d H:i:s', $candidate->created_at)->format('H:i:s d-m-Y');
                    $candidate['id'] = $candidate->id;
                    $job = app(JobRepositoryInterface::class)->searchOnElasticsearch(
                        $candidate->job_id,
                        ['page_size' => 1]
                    );
                    $job = (isset($job['hits']['hits'][0]) ?
                            collect($job['hits']['hits'][0]['_source'])->only('id', 'title', 'company', 'owned_id')->all() : null);

                    $company = app(CompanyRepositoryInterface::class)->searchOnElasticsearch(
                        null,
                        [
                            'ids' => $job['owned_id'] ?? null,
                            'page_size' => 1,
                        ]
                    );

                    $company = (isset($company['hits']['hits'][0]) ?
                        collect($company['hits']['hits'][0]['_source'])->only('slug', 'image_logo', 'display_name')->all() : null);

                    return [
                        'id' => $candidate->id,
                        'applied_at' => strtotime(Carbon::createFromFormat('Y-m-d H:i:s', $candidate->created_at)->format('H:i:s d-m-Y')),
                        'job_id' => $job['id'] ?? null,
                        'job_title' => $job['title'] ?? null,
                        'company_id' => $job['owned_id'] ?? null,
                        'company_slug' => $company['slug'] ?? null,
                        'company_image_logo' => $company['image_logo'] ?? null,
                        'company_name' => $company['display_name'] ?? null,
                    ];
                });
            }

            return [
                'features' => $features,
                'id' => $e['resume_id'] ?? null,
                'source' => 'Created on TopDev',
                'name' => $e['resume_name'] ?? null,
                'created_at' => isset($e['created_at']) ? strtotime(Carbon::createFromFormat('Y-m-d H:i:s', $e['created_at'])->format('H:i:s d-m-Y')) : null,
                'updated_at' => isset($e['updated_at']) ? strtotime(Carbon::createFromFormat('Y-m-d H:i:s', $e['updated_at'])->format('H:i:s d-m-Y')) : null,
                'type' => 'cvbuilder',
                'assets' => [],
                'applies' => $data,
            ];
        })->toArray();

        return $data;
    }

    public function tracking(TrackingRequest $request, BaseHttpResponse $response)
    {

        app(QueueHeplers::class)->trackingUserV2(['jobs::' => $request->job_ids, 'companies::'=> $request->company_ids], $request->collection, auth('api')->user(), $request);

        return $response->setMessage('success');
    }
}
