<?php

namespace Modules\Activity\Http\Controllers\API;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Activity\Repositories\Contracts\ActivityRepositoryInterface;
use Redmix0901\Core\Http\Responses\BaseHttpResponse;

class ApiActivityController extends Controller
{
    public function __construct(private readonly ActivityRepositoryInterface $activityRepository)
    {
    }

    public function reports(Request $request, BaseHttpResponse $response)
    {
        $fromDate = $request->from_date ? Carbon::createFromFormat('d-m-Y', $request->from_date) : Carbon::today()->subWeeks(5);
        $toDate = $request->to_date ? Carbon::createFromFormat('d-m-Y', $request->to_date) : Carbon::today();

        //if group_user_jobs == true and filter has Aggregation group largest 10000 then ERROR
        $activities = $this->activityRepository->searchOnElasticsearchStatistics(
            null,
            [
                'viewed_at' => [
                    'from' => $fromDate->startOfDay()->format('Y-m-d H:i:s'),
                    'to' => $toDate->endOfDay()->format('Y-m-d H:i:s'),
                ],
                'period' => $request->period ?? 'daily',
                'job_status' => 3,
                'collection' => 'JobView',
                'page_size' => 1,
                'group_user_jobs' => true,
            ]
        );

        $result = [];
        foreach ($activities['aggregations']['statis']['buckets'] as $e) {
            $totalUniqueViewJobOpen = $e['count_unique_view_users_jobs']['value'] ?? 0;

            $keyReSult = (explode(' ', (string) $e['key_as_string']))[1];
            $result[$keyReSult] = [
                'total_view_job_open' => $e['doc_count'] ?? 0,

                'total_unique_view_job_open' => $totalUniqueViewJobOpen,

                'created_at' => Carbon::parse($keyReSult)->timestamp,
            ];
        }

        return response()->json([
            'error' => false,
            'message' => 'Okay!',
            'data' => $result,
        ]);
    }
}
