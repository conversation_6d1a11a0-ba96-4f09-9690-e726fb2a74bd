<?php

namespace Modules\Activity\Traits;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Activity\Contracts\ViewableContract;

trait LogsActivity
{
    /**
     * @return HasMany
     */
    public function activities(): Has<PERSON><PERSON>
    {
        return $this->hasMany(config('activity.activity_model'), 'user_id')
                    ->orderByDesc('id');
    }

    /**
     * fetch all activities for the model, if necessary.
     *
     * In Laravel versions prior to 5.3, relations that are lazy loaded by the
     * `getRelationFromMethod()` method ( invoked by the `__get()` magic method)
     * are not passed through the `setRelation()` method, so we load the relation
     * manually.
     *
     * @return mixed
     */
    public function getActivitiesCollection()
    {
        if (!$this->relationLoaded('activities')) {
            $this->setRelation('activities', $this->activities()->get());
        }

        return $this->getRelation('activities')
                    ->groupBy('collection');
    }

    /**
     * @return void
     */
    public function activityTracking(ViewableContract $viewable = null)
    {

    }
}
