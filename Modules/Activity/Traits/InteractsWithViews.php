<?php

namespace Modules\Activity\Traits;

use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Modules\Activity\Jobs\RemoveViewsOnDelete;

trait InteractsWithViews
{
    /**
     * Get all of the views for the post.
     *
     * @return MorphToMany
     */
    public function views(): MorphToMany
    {
        return $this->morphToMany(
            config('activity.activity_model'),
            'viewable',
            'viewables',
            'viewable_id',
            'view_id'
        )->view();
    }

    public function traceActivityBy()
    {
        return null;
    }

    protected static function bootInteractsWithViews()
    {
        static::deleted(function (self $model) {
            if (isset($model->removeViewsOnDelete) && $model->removeViewsOnDelete == true) {
                dispatch(new RemoveViewsOnDelete($model));
            }
        });
    }
}
