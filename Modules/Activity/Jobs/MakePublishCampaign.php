<?php

namespace Modules\Activity\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Modules\Activity\Contracts\CampaignContract;
use Modules\Activity\Events\PublishCampaignCompleted;
use Modules\Activity\Events\PublishCampaignIncompleted;

class MakePublish<PERSON>ampaign implements ShouldQueue
{
    use Queueable;

    /**
     * The models to be made searchable.
     *
     * @var CampaignContract
     */
    public $campaign;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(CampaignContract $campaign)
    {
        $this->campaign = $campaign;
    }

    /**
     * Handle the job.
     *
     * @return void
     */
    public function handle()
    {
        if (!$this->campaign->shouldInterrupt()) {

            $campaign = $this->campaign;

            $response = $campaign->publishCampaignUsing()->send($campaign);

            if ($response) {
                event(new PublishCampaignCompleted($campaign));
            } else {
                event(new PublishCampaignIncompleted($campaign));
            }
        }
    }
}
