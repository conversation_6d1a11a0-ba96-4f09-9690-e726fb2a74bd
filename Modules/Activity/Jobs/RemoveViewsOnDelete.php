<?php

namespace Modules\Activity\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Modules\Activity\Entities\Activity;
use Modules\Activity\Entities\Viewable;

class RemoveViewsOnDelete implements ShouldQueue
{
    use Queueable;

    public $model;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Model $model)
    {
        $this->model = $model;
        $this->onConnection(config('queue.ams'));
    }

    /**isForceDeleting
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $viewables = $this->model->views();

        $viewables_type = $this->model::class;

        if ($viewables_type == \Modules\Taxonomy\Entities\Taxonomy::class) {
            $model = Viewable::where('viewable_id', $this->model->getKey())->get();
            foreach ($model as $check_viewable) {
                $unique_viewable = Viewable::where('view_id', $check_viewable->view_id)->count();

                if ($unique_viewable < 2) {
                    Activity::find($check_viewable->view_id)->delete();
                }
            }
            $viewables->sync([]);
        } elseif (!$this->model->isForceDeleting()) {
            $viewables->forceDelete();

            $viewables->sync([]);
        }
    }
}
