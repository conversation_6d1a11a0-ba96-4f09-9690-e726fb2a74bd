<?php

namespace Modules\Activity\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Request;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Modules\Activity\Services\ActivityManager;
use Modules\Activity\Services\Visitor;

class ActivityLoggerProcess implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Server and execution environment parameters ($_SERVER).
     *
     * @var array
     */
    public $server;

    /**
     * Cookies ($_COOKIE).
     *
     * @var array
     */
    public $cookies;

    /**
     * The collection under where the view will be saved.
     *
     * @var string|null
     */
    protected $collection = null;

    /**
     * The user instance.
     *
     * @var int
     */
    protected $user;

    /**
     * The viewable model where we are applying actions to.
     *
     * @var string|null
     */
    protected $viewable;

    /**
     * @var string
     */
    protected $visitorCookie;

    /**
     * @var array
     */
    protected $properties;

    protected $device_token;
    protected $trackingVariant;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($viewable, $user, string $visitorCookie, array $properties = [], array $cookies = [], array $server = [], $collection = 'view', $device_token = null, $trackingVariant = null)
    {
        $this->server = $server;
        $this->cookies = $cookies;
        $this->user = $user;
        $this->viewable = $viewable;
        $this->collection = $collection;
        $this->visitorCookie = $visitorCookie;
        $this->properties = $properties;
        $this->device_token = $device_token;
        $this->trackingVariant = $trackingVariant;

        $this->onConnection(config('queue.ams'));
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $anonymous = new Visitor($this->request(), $this->visitorCookie, $this->user);

        return app(ActivityManager::class)
            ->causer($this->user)
            ->performedOn($this->viewable)
            ->asAnonymous($anonymous)
            ->withProperties(array_merge($this->properties, [
                'area_id' => empty($this->properties['area_id'] ?? null) ? null : $this->properties['area_id'],
                'referer' => $this->request()->header('referer'),
                'user_agent' => $this->request()->header('User-Agent'),
                'referer_host' => parse_url($this->request()->header('referer'), PHP_URL_HOST),
                'collection' => $this->collection,
            ]))
            ->log();
    }

    /**
     * Emulator the request.
     *
     * @return Illuminate\Http\Request
     */
    public function request()
    {
        return new Request([], [], [], $this->cookies, [], $this->server);
    }
}
