<?php

namespace Modules\Activity\Entities;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Laravel\Scout\Searchable;
use Modules\Activity\Contracts\ActivityContract;
use Modules\Activity\Services\ContentManager;
use Modules\Company\Entities\Company;
use Modules\Job\Entities\Job;
use Modules\Taxonomy\Entities\Taxonomy;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;

class Activity extends Model implements ActivityContract
{
    use SoftDeletes;
    use Searchable;

    public const RELATION_TYPES = [
        'apply-now' => 'click-apply',
        'view' => 'view',
        'search' => 'search',
        'apply-without-cv' => 'click-apply',
        'click-ok-when-sent-cv' => 'click-ok-when-sent-cv',
    ];

    /**
     * @inheritdoc
     */
    protected $table = 'views';

    /**
     * @inheritdoc
     */
    protected $manager;

    /**
     * @inheritdoc
     */
    public $timestamps = false;

    /**
     * @inheritdoc
     */
    protected $fillable = [
        'user_agent', 'user_id', 'referer', 'referer_host', 'content', 'area_id', 'collection',
    ];

    /**
     * Viewables item unAttached.
     *
     * @return array
     */
    protected $unAttachedViewablesItems = [];

    /**
     * Get user agent.
     *
     * @return mixed
     */
    public function getUserAgentAttribute()
    {
        return $this->attributes['user_agent'];
    }

    /**
     * Get user.
     *
     * @return Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(config('auth.providers.users.model'), 'user_id')
                        ->withDefault();
    }

    /**
     * Get area.
     *
     * @return Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function area()
    {
        return $this->hasOne(config('vietnamarea.media_model'), 'code', 'area_id')
                    ->withDefault();
    }

    /**
     * Get skills.
     *
     * @return Illuminate\Database\Eloquent\Relations\MorphedByMany
     */
    public function skills()
    {
        return $this->morphedByMany(Taxonomy::class, 'viewable', 'viewables', 'view_id', 'viewable_id')
                    ->using(Viewable::class);
    }

    /**
     * Get jobs.
     *
     * @return Illuminate\Database\Eloquent\Relations\MorphedByMany
     */
    public function jobs()
    {
        return $this->morphedByMany(Job::class, 'viewable', 'viewables', 'view_id', 'viewable_id')
                    ->withTrashed()
                    ->using(Viewable::class);
    }

    /**
     * Get companies.
     *
     * @return Illuminate\Database\Eloquent\Relations\MorphedByMany
     */
    public function companies()
    {
        return $this->morphedByMany(Company::class, 'viewable', 'viewables', 'view_id', 'viewable_id')
                    ->withTrashed()
                    ->using(Viewable::class);
    }

    /**
     * Get user involve if view by anonymous.
     *
     * @return Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function trackingUser()
    {
        return $this->hasOne($this, 'visitor', 'visitor')
                    ->whereNotNull('user_id');
    }

    public function isClickApply()
    {
        return in_array($this->collection, ['apply-now', 'apply-without-cv']);
    }

    public function isSearch()
    {
        return in_array($this->collection, ['search']);
    }

    public function isView()
    {
        return in_array($this->collection, ['view']);
    }

    public function invalid()
    {
        return empty($this->getSlug());
    }

    public function getSlug()
    {
        return $this->contentManager()->getSlug();
    }

    public function getContent()
    {
        return $this->contentManager()->getContent();
    }

    public function toArray()
    {
        $this->loadMissing(['jobs', 'skills', 'companies']);

        return [
            'visitor' => $this->visitor,
            'user_id' => $this->user_id,
            'slug' => $this->getSlug(),
            'content' => $this->getContent(),
            'collection' =>$this->getCollection(),
            'area_id' => $this->area_id,
            'jobs' => $this->jobs->pluck('id')->all(),
            'skills' => $this->skills->pluck('id')->all(),
            'companies' => $this->companies->pluck('id')->all(),
            'viewed_at' => $this->viewed_at,
        ];
    }

    /**
     * Get the value used to index the model.
     *
     * @return mixed
     */
    public function getScoutKey()
    {
        return $this->getKey();
    }

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'activities_ams';
    }

    /**
     * Determine if the model should be searchable.
     *
     * @return bool
     */
    public function shouldBeSearchable()
    {
        return !$this->invalid();
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        return $this->toArray();
    }

    public function scopeVistor(Builder $builder, $visitor = null)
    {
        return empty($visitor) ? $builder : $builder->whereVisitor($visitor);
    }

    public function getAllByUser($params = [])
    {
        $model = $this;

        if (!array_key_exists('user_id', $params) && !array_key_exists('visitor', $params)) {
            return [];
        }

        $activities = $model->search('*', function ($client, $body) use ($model, $params) {
            $baseQuery = new BoolQuery();

            if (array_key_exists('user_id', $params)) {
                $baseQuery->add(new TermQuery('user_id', $params['user_id']), BoolQuery::MUST);
            }

            if (array_key_exists('visitor', $params) && !array_key_exists('user_id', $params)) {
                $baseQuery->add(new TermQuery('visitor', $params['visitor']), BoolQuery::MUST);
            }

            $body->addQuery($baseQuery);
            $body->setSize(10000);
            $body->setFrom(0);

            return $client
                ->search([
                    'index' => $model->searchableAs(),
                    'body' => $body->toArray()
                ])
                ->asArray();
        })->raw()['hits']['hits'] ?? [];

        return collect($activities)->map(fn($item) => $item['_source']);
    }

    public function getCollection()
    {
        $collection = self::RELATION_TYPES[$this->collection] ?? null;

        if ($collection == 'view') {
            if ($this->jobs->count() == 1) {
                return 'JobView';
            }

            if ($this->companies->count() == 1) {
                return 'CompanyView';
            }

            return null;
        }

        return $collection;
    }

    /**
     * @return ContentManager
     */
    private function contentManager()
    {
        if (!empty($this->manager)) {
            return $this->manager;
        }

        return $this->manager = resolve(ContentManager::class)
                    ->driver($this->getCollection())
                    ->from($this);
    }

    /**
     * @return void
     */
    public function prepareToAttachViewables($viewables, $relation)
    {
        $this->unAttachedViewablesItems[] = compact('viewables', 'relation');
    }

    /**
     * @return void
     */
    public function processUnattachedViewables(callable $callable)
    {
        foreach ($this->unAttachedViewablesItems as $item) {
            $callable($item['viewables'], $item['relation']);
        }

        $this->unAttachedViewablesItems = [];
    }

    /**
     * Scope a query to only include views withing the collection.
     *
     * @return Builder
     */
    public function scopeView(Builder $query)
    {
        return $query->whereIn('collection', ['view']);
    }

    /**
     * Scope a query to only include views withing the collection.
     *
     * @return Builder
     */
    public function scopeSearch(Builder $query)
    {
        return $query->whereIn('collection', ['search']);
    }
}
