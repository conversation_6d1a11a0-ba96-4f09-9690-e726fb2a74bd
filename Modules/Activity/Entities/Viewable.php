<?php

namespace Modules\Activity\Entities;

use Illuminate\Database\Eloquent\Relations\MorphPivot;

class Viewable extends MorphPivot
{
    /**
     * @inheritdoc
     */
    public $timestamps = false;

    /**
     * @inheritdoc
     */
    protected $table = 'viewables';

    /**
     * @inheritdoc
     */
    protected $fillable = [
        'view_id',
        'viewable_id',
        'viewable_type',
    ];
}
