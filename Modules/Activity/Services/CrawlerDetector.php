<?php

namespace Modules\Activity\Services;

use Cyrilde<PERSON>it\EloquentViewable\Contracts\CrawlerDetector as CrawlerDetectorContract;
use <PERSON><PERSON><PERSON>\CrawlerDetect\CrawlerDetect;

class CrawlerDetector implements CrawlerDetectorContract
{
    /**
     * CrawlerDetect instance.
     *
     * @var CrawlerDetect
     */
    private $detector;

    /**
     * Create a new CrawlerDetector instance.
     *
     * @param CrawlerDetect  $detector
     * @return void
     */
    public function __construct(array $headers = null, $userAgent = null)
    {
        $this->detector = new CrawlerDetect($headers, $userAgent);
    }

    /**
     * Determine if the current user is a crawler.
     *
     * @return bool
     */
    public function isCrawler(): bool
    {
        return $this->detector->isCrawler();
    }
}
