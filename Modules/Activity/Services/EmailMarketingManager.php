<?php

namespace Modules\Activity\Services;

use Illuminate\Support\Manager;
use Modules\Activity\EmailMarketing\AutoMauticEmailMarketing;
use Modules\Activity\EmailMarketing\JobMauticEmailMarketing;
use Modules\Activity\Services\Mautic\Mautic;

class EmailMarketingManager extends Manager
{
    /**
     * Create an mautic email marketing instance.
     *
     * @return JobMauticEmailMarketing
     */
    public function createJobMauticDriver()
    {
        $options = config('activity.job_mautic');

        return new JobMauticEmailMarketing(
            Mautic::create($options)->contactApi()
        );
    }

    /**
     * Create an mautic email marketing instance.
     *
     * @return AutoMauticEmailMarketing
     */
    public function createAutoMauticDriver()
    {
        $options = config('activity.auto_mautic');

        return new AutoMauticEmailMarketing(
            Mautic::create($options)->contactApi()
        );
    }

    /**
     * Get the default driver name.
     *
     * @return string
     */
    public function getDefaultDriver()
    {
        return config('activity.email_marketing');
    }
}
