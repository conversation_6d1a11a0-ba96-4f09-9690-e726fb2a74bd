<?php

namespace Modules\Activity\Services;

use Illuminate\Support\Manager;
use Modules\Activity\Contents\ClickApplyContentDriver;
use Modules\Activity\Contents\CompanyViewContentDriver;
use Modules\Activity\Contents\JobViewContentDriver;
use Modules\Activity\Contents\NullContentDriver;
use Modules\Activity\Contents\SearchContentDriver;

class ContentManager extends Manager
{
    /**
     * Create an content instance.
     *
     * @return NullContentDriver
     */
    public function createNullDriver()
    {
        return new NullContentDriver;
    }

    /**
     * Create an content instance.
     *
     * @return JobViewContentDriver
     */
    public function createJobViewDriver()
    {
        return new JobViewContentDriver;
    }

    /**
     * Create an content instance.
     *
     * @return CompanyViewContentDriver
     */
    public function createCompanyViewDriver()
    {
        return new CompanyViewContentDriver;
    }

    /**
     * Create an content instance.
     *
     * @return SearchContentDriver
     */
    public function createSearchDriver()
    {
        return new SearchContentDriver;
    }

    /**
     * Create an content instance.
     *
     * @return ClickApplyContentDriver
     */
    public function createClickApplyDriver()
    {
        return new ClickApplyContentDriver;
    }

    /**
     * Get the default driver name.
     *
     * @return string
     */
    public function getDefaultDriver()
    {
        return 'null';
    }
}
