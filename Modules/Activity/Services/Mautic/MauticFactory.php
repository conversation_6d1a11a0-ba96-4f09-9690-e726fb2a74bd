<?php

namespace Modules\Activity\Services\Mautic;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Mautic\Auth\ApiAuth;
use Mautic\MauticApi;

class MauticFactory
{
    protected $baseUrl = 'https://job.topdev.vn/';

    public function contactApi()
    {
        // Initiate the auth object specifying to use BasicAuth
        $initAuth = new ApiAuth();
        $auth = $initAuth->newAuth($this->getConfigBasicAuth(), 'BasicAuth');

        return (new MauticApi)->newApi('contacts', $auth, $this->baseUrl);
    }

    /**
     * Make a new Mautic client.
     *
     * @return \Mautic\Config
     */
    public function make(array $config)
    {
        return $this->getClient($config);
    }

    /**
     * Call Mautic Api.
     *
     * @throws \ClientException
     *
     * @param $method
     * @param $endpoints
     * @param $body
     * @param $token
     *
     * @return mixed
     */
    public function callMautic($method, $endpoints, $body, AccessToken $token)
    {
        $mauticURL = $this->getMauticUrl('api/' . $endpoints);

        $params = [];
        if (!empty($body)) {
            $params = [];
            foreach ($body as $key => $item) {
                $params['form_params'][$key] = $item;
            }
        }

        $headers = ['headers' => ['Authorization' => 'Bearer ' . $token->getToken()]];
        $client = new Client($headers);

        try {

            $response = $client->request($method, $mauticURL, $params);
            $responseBodyAsString = $response->getBody();

            return json_decode($responseBodyAsString, true);
        } catch (ClientException $e) {
            $exceptionResponse = $e->getResponse();

            return $statusCode = $exceptionResponse->getStatusCode();
        }
    }

    public function getConfigBasicAuth()
    {
        return [
            'userName'   => 'tu',
            'password'   => '123312',
        ];
    }

    public function getConfig()
    {
        return [
            'clientKey' => '',
            'clientSecret' => '',
            'callback' => '',
        ];
    }

    /**
     * Generate new token once old one expire
     * and store in consumer table.
     */
    public function refreshToken($refreshToken)
    {
        $mauticURL = $this->getMauticUrl('oauth/v2/token');
        $config = $this->getConfig();

        $client = new Client();

        try {
            $response = $client->request('POST', $mauticURL, [
                'form_params' => [
                    'client_id'     => $config['clientKey'],
                    'client_secret' => $config['clientSecret'],
                    'redirect_uri'  => $config['callback'],
                    'refresh_token' => $refreshToken,
                    'grant_type'    => 'refresh_token',
                ]]);

            $responseBodyAsString = $response->getBody();
            $responseBodyAsString = json_decode($responseBodyAsString, true);

            return $this->setAccessTokenLocal([
                'access_token'  => $responseBodyAsString['access_token'],
                'expires_in'       => time() + $responseBodyAsString['expires_in'],
                'token_type'    => $responseBodyAsString['token_type'],
                'refresh_token' => $responseBodyAsString['refresh_token'],
            ]);
        } catch (ClientException $e) {
            return $exceptionResponse = $e->getResponse();
        }
    }

    public function refreshTokenIfExpired(AccessToken $token)
    {
        if ($token->hasExpired() && $token->getRefreshToken()) {

            $token = $this->refreshToken(
                $token->getRefreshToken()
            );
        }

        return $token;
    }

    /**
     * Make a new Mautic url.
     *
     * @param string $endpoints
     * @return url
     */
    protected function getMauticUrl($endpoints = null)
    {
        if (!empty($endpoints)) {
            return $this->baseUrl . $endpoints;
        } else {
            return $this->baseUrl;
        }
    }

    /**
     * Get the Mautic client.
     *
     *
     * @return \Mautic\MauticConsumer
     */
    protected function getClient(array $setting)
    {
        if (session_status() == PHP_SESSION_NONE) {
            session_name('mauticOAuth');
            session_start();
        }

        $auth = ApiAuth::initiate($setting);

        if ($auth->validateAccessToken()) {
            if ($auth->accessTokenUpdated()) {
                $accessTokenData = $auth->getAccessTokenData();

                return $this->setAccessTokenLocal([
                    'access_token' => $accessTokenData['access_token'],
                    'expires_in' => $accessTokenData['expires'],
                    'token_type' => $accessTokenData['token_type'],
                    'refresh_token' => $accessTokenData['refresh_token'],
                ]);
            }
        }
    }

    public function setAccessTokenLocal($token)
    {
        $tokenPath = storage_path('api_mautic_credentials/token.json');

        if (is_array($token)) {
            $token = new AccessToken($token);
        }

        if ($token instanceof AccessToken) {
            file_put_contents($tokenPath, json_encode($token->getValues()));

            return $token;
        }

        throw new \InvalidArgumentException('The Mautic token invalid.');
    }

    public function getAccessTokenLocal()
    {
        $tokenPath = storage_path('api_mautic_credentials/token.json');

        if (!file_exists($tokenPath)) {
            throw new \Exception('Do not receive access token.');
        }

        return new AccessToken(json_decode(file_get_contents($tokenPath), true));
    }
}
