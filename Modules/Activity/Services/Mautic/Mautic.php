<?php

namespace Modules\Activity\Services\Mautic;

use InvalidArgumentException;
use Mautic\Auth\ApiAuth;
use Mautic\MauticApi;

class Mautic
{
    /**
     * @var string
     */
    protected $userName;

    /**
     * @var string
     */
    protected $password;

    /**
     * @var string
     */
    protected $baseUrl;

    /**
     * Create a new Mautic manager instance.
     *
     * @param $options
     *
     * @return void
     */
    public function __construct($options = [])
    {
        if (empty($options['userName'])) {
            throw new InvalidArgumentException('Required option not passed: "userName"');
        }

        $this->userName = $options['userName'];

        if (empty($options['password'])) {
            throw new InvalidArgumentException('Required option not passed: "password"');
        }

        $this->password = $options['password'];

        if (empty($options['baseUrl'])) {
            throw new InvalidArgumentException('Required option not passed: "baseUrl"');
        }

        $this->baseUrl = $options['baseUrl'];
    }

    public static function create($config = [])
    {
        return new static($config);
    }

    public function contactApi()
    {
        // Initiate the auth object specifying to use BasicAuth
        $initAuth = new ApiAuth();
        $auth = $initAuth->newAuth($this->getConfigBasicAuth(), 'BasicAuth');

        return (new MauticApi)->newApi('contacts', $auth, $this->baseUrl);
    }

    public function getConfigBasicAuth()
    {
        return [
            'userName'   => $this->userName,
            'password'   => $this->password,
        ];
    }
}
