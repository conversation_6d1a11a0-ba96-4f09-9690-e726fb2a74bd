<?php

namespace Modules\Activity\Services;

use Illuminate\Support\Manager;
use Modules\Activity\Campaigns\ClickApply24h;
use Modules\Activity\Campaigns\CongratulationCompleteCV;
use Modules\Activity\Campaigns\EmailClickApply;
use Modules\Activity\Campaigns\EmailConfirmAllJob;
use Modules\Activity\Campaigns\EmailConfirmSingleJob;
use Modules\Activity\Campaigns\EmailMeJobLikeThis;
use Modules\Activity\Campaigns\EmailSavedJob;
use Modules\Activity\Campaigns\EventDownloadCvBuilder;
use Modules\Activity\Campaigns\JobRecommend;
use Modules\Activity\Campaigns\JobRecommendApplyAll3030;
use Modules\Activity\Campaigns\JobRecommendApplySingle3030;
use Modules\Activity\Campaigns\JobRecommendCvCompleted3010;
use Modules\Activity\Campaigns\JobRecommendCvCompleted33;
use Modules\Activity\Campaigns\JobRecommendCvCompleted71;
use Modules\Activity\Campaigns\JobRecommendCvIncompleted71;
use Modules\Activity\Campaigns\JobRecommendJobLikeThis33;
use Modules\Activity\Campaigns\JobRecommendJobLikeThis71;
use Modules\Activity\Campaigns\JobRecommendLogin33;
use Modules\Activity\Campaigns\JobRecommendLogin71;
use Modules\Activity\Campaigns\NullCampaign;
use Modules\Activity\Campaigns\PushCompleteCV;
use Modules\Activity\Campaigns\SavedJob24h;
use Modules\Activity\Campaigns\WelcomeNewLogin;

class CampaignManager extends Manager
{
    /**
     * Get a driver instance.
     *
     * @param  string|null  $name
     * @return mixed
     */
    public function provider($provider = null)
    {
        return $this->driver($provider);
    }

    /**
     * Get the default driver name.
     *
     * @return string
     */
    public function getDefaultDriver()
    {
        return 'null';
    }

    /**
     * Create a PushCompleteCV driver.
     *
     * @return PushCompleteCV
     */
    public function createPushCompleteCVDriver()
    {
        return new PushCompleteCV;
    }

    /**
     * Create a WelcomeNewLogin driver.
     *
     * @return WelcomeNewLogin
     */
    public function createWelcomeNewLoginDriver()
    {
        return new WelcomeNewLogin;
    }

    /**
     * Create a JobRecommend driver.
     *
     * @return JobRecommend
     */
    public function createJobRecommendDriver()
    {
        return new JobRecommend;
    }

    /**
     * Create a SavedJob24h driver.
     *
     * @return \Modules\Activity\Campaigns\SavedJob
     */
    public function createSavedJob24hDriver()
    {
        return new SavedJob24h;
    }

    /**
     * Create a ClickApply24h driver.
     *
     * @return ClickApply24h
     */
    public function createClickApply24hDriver()
    {
        return new ClickApply24h;
    }

    /**
     * Create a CongratulationCompleteCV driver.
     *
     * @return CongratulationCompleteCV
     */
    public function createCongratulationCompleteCVDriver()
    {
        return new CongratulationCompleteCV;
    }

    /**
     * Create a EmailClickApply driver.
     *
     * @return EmailClickApply
     */
    public function createEmailClickApplyDriver()
    {
        return new EmailClickApply;
    }

    /**
     * Create a EmailConfirmAllJob driver.
     *
     * @return EmailConfirmAllJob
     */
    public function createEmailConfirmAllJobDriver()
    {
        return new EmailConfirmAllJob;
    }

    /**
     * Create a EmailConfirmSingleJob driver.
     *
     * @return EmailConfirmSingleJob
     */
    public function createEmailConfirmSingleJobDriver()
    {
        return new EmailConfirmSingleJob;
    }

    /**
     * Create a EmailSavedJob driver.
     *
     * @return EmailSavedJob
     */
    public function createEmailSavedJobDriver()
    {
        return new EmailSavedJob;
    }

    /**
     * Create a EmailMeJobLikeThis driver.
     *
     * @return EmailMeJobLikeThis
     */
    public function createEmailMeJobLikeThisDriver()
    {
        return new EmailMeJobLikeThis;
    }

    /**
     * Create a EventDownloadCvBuilder driver.
     *
     * @return EventDownloadCvBuilder
     */
    public function createEventDownloadCvBuilderDriver()
    {
        return new EventDownloadCvBuilder;
    }

    /**
     * Create a Null driver.
     *
     * @return NullCampaign
     */
    public function createNullCampaignDriver()
    {
        return new NullCampaign;
    }

    /**
     * Create a JobRecommendApplyAll3030 driver.
     *
     * @return JobRecommendApplyAll3030
     */
    public function createJobRecommendApplyAll3030Driver()
    {
        return new JobRecommendApplyAll3030;
    }

    /**
     * Create a JobRecommendApplySingle3030 driver.
     *
     * @return JobRecommendApplySingle3030
     */
    public function createJobRecommendApplySingle3030Driver()
    {
        return new JobRecommendApplySingle3030;
    }

    /**
     * Create a JobRecommendCvCompleted33 driver.
     *
     * @return JobRecommendCvCompleted33
     */
    public function createJobRecommendCvCompleted33Driver()
    {
        return new JobRecommendCvCompleted33;
    }

    /**
     * Create a JobRecommendCvCompleted71 driver.
     *
     * @return JobRecommendCvCompleted71
     */
    public function createJobRecommendCvCompleted71Driver()
    {
        return new JobRecommendCvCompleted71;
    }

    /**
     * Create a JobRecommendCvCompleted3010 driver.
     *
     * @return JobRecommendCvCompleted3010
     */
    public function createJobRecommendCvCompleted3010Driver()
    {
        return new JobRecommendCvCompleted3010;
    }

    /**
     * Create a JobRecommendCvIncompleted71 driver.
     *
     * @return JobRecommendCvIncompleted71
     */
    public function createJobRecommendCvIncompleted71Driver()
    {
        return new JobRecommendCvIncompleted71;
    }

    /**
     * Create a JobRecommendLogin33 driver.
     *
     * @return JobRecommendLogin33
     */
    public function createJobRecommendLogin33Driver()
    {
        return new JobRecommendLogin33;
    }

    /**
     * Create a JobRecommendLogin71 driver.
     *
     * @return JobRecommendLogin71
     */
    public function createJobRecommendLogin71Driver()
    {
        return new JobRecommendLogin71;
    }

    /**
     * Create a JobRecommendJobLikeThis33 driver.
     *
     * @return JobRecommendJobLikeThis33
     */
    public function createJobRecommendJobLikeThis33Driver()
    {
        return new JobRecommendJobLikeThis33;
    }

    /**
     * Create a JobRecommendJobLikeThis71 driver.
     *
     * @return JobRecommendJobLikeThis71
     */
    public function createJobRecommendJobLikeThis71Driver()
    {
        return new JobRecommendJobLikeThis71;
    }
}
