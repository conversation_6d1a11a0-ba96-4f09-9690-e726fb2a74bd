<?php

namespace Modules\Activity\Services;

use Modules\Activity\Entities\Activity;

class VisitorFeed
{
    private $activities;

    /**
     * Create a new VisitorManager instance.
     *
     * @param  array $visitor
     * @return void
     */
    public function __construct($visitor = [])
    {
        $this->activities = app(Activity::class)->getAllByUser($visitor);
    }

    public static function create($visitor = [])
    {
        return new static($visitor);
    }

    public function getHistories()
    {
        return collect($this->activities)->filter(fn($activity) => $activity['collection'] ?? null == 'search');
    }

    public function getSuggestSkills($limit = 1)
    {
        return $this->getHistories()->map(fn($activity) => $activity['skills'] ?? [])
            ->collapse()->countBy()->sort()->reverse()->take($limit)->keys();
    }

    public function getSuggestAreas($limit = 1)
    {
        return $this->getHistories()->reject(fn($activity) => empty($activity['area_id'] ?? null))
            ->pluck('area_id')->countBy()->sort()->reverse()->take($limit)->keys();
    }
}
