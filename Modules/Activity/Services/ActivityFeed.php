<?php

namespace Modules\Activity\Services;

use Modules\Taxonomy\Repositories\Contracts\TaxonomyRepositoryInterface;

class ActivityFeed
{
    public function __construct(private readonly TaxonomyRepositoryInterface $taxonomyRepository)
    {
    }

    public function topKeyword($limit = 1)
    {
        $taxonomies = $this->taxonomyRepository->searchOnElasticsearch(null, [
            'taxonomy' => 'skills',
            'ids' => '78,21,22,209,210,1,7367,1456',
            'page_size' => 10000,
        ]);

        $taxonomies = (isset($taxonomies['hits']['hits']) && !empty($taxonomies['hits']['hits']) > 0) ? collect($taxonomies['hits']['hits']) : collect([]);

        return $taxonomies->map(fn($taxomomy) => [
            'id' => $taxomomy['_source']['id'],
            'text' => $taxomomy['_source']['text_vi'],
        ])->take($limit);
    }
}
