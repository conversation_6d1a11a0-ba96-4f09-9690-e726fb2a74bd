<?php

namespace Modules\Activity\Services;

use Illuminate\Contracts\Config\Repository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Modules\Activity\Contracts\ActivityContract;
use Modules\Activity\Contracts\ViewableContract;
use Modules\Activity\Contracts\VisitorContract;

class ActivityManager
{
    protected $visitor;

    protected $activity;

    protected $defaultLogName;

    public function __construct(VisitorContract $visitor, Repository $config)
    {
        $this->visitor = $visitor;
        $this->defaultLogName = $config['activity']['default_log_name'];
    }

    public function causer($model)
    {
        $model = $this->normalizeUser($model);

        if ($model instanceof Model) {
            $this->getActivity()->user()->associate($model);
        }

        return $this;
    }

    public function asAnonymous(VisitorContract $visitor)
    {
        $this->visitor = $visitor;

        return $this;
    }

    public function performedOn($viewable)
    {
        foreach (explode(';', (string) $viewable) as $key => $value) {
            $prefix = config('activity.prefix_viewable');
            $relation = Str::before($value, '::');

            if (in_array($relation, array_keys($prefix))) {
                $viewableId = Str::after($value, '::');
                $viewableClass = new $prefix[$relation];

                $viewables = $this->normalizeViewable($viewableId, $viewableClass);

                if ($viewables->first() instanceof ViewableContract
                    && method_exists($this->getActivity(), $relation)) {
                    if (!$this->getActivity()->exists) {
                        $this->getActivity()->prepareToAttachViewables($viewables->all(), $relation);

                        $class = $this->getActivity();

                        $class::created(function ($model) {
                            $model->processUnattachedViewables(function ($viewables, $relation) use ($model) {
                                $model->{$relation}()->attach(
                                    collect($viewables)->pluck('id')->all()
                                );
                            });
                        });

                        continue;
                    }

                    $this->getActivity()->{$relation}()->attach(
                        $viewables->pluck('id')->all()
                    );
                }
            }
        }

        return $this;
    }

    public function log(string $content = null)
    {
        $activity = $this->activity;

        $activity->visitor = $this->visitor->id();
        $activity->save();
        $this->activity = null;

        return $activity;
    }

    public function withProperties($properties)
    {
        $this->getActivity()->fill($properties);

        return $this;
    }

    public static function getActivityModelInstance()
    {
        $activityModelClassName = config('activity.activity_model');

        return new $activityModelClassName();
    }

    protected function normalizeUser($model)
    {
        if ($model instanceof Model) {
            return $model;
        }

        $userClass = config('auth.providers.users.model');
        $model = (new $userClass)::find($model);

        return $model;
    }

    protected function normalizeViewable($viewableId, $viewableClass)//: Collection
    {
        return $viewableClass::find(explode(',', (string) $viewableId));
    }

    protected function getActivity(): ActivityContract
    {
        if (!$this->activity instanceof ActivityContract) {
            $this->activity = self::getActivityModelInstance();
            $this->activity->collection = $this->defaultLogName;
        }

        return $this->activity;
    }
}
