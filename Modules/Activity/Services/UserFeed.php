<?php

namespace Modules\Activity\Services;

use Illuminate\Support\Collection;
use Modules\Job\Repositories\Contracts\JobRepositoryInterface;
use Modules\User\Repositories\Contracts\UserRepositoryInterface;
use Redmix0901\ElasticResource\ElasticCollection;

class UserFeed
{
    private $user = null;

    /**
     * Create a new UserFeed instance.
     *
     * @param  array $visitor
     * @return void
     */
    public function __construct(private $payload = [], private $visitor = [])
    {
        $this->user = $this->resolveUser();
    }

    public static function create($payload = [], $visitor = [])
    {
        return new static($payload, $visitor);
    }

    public function userAnonymous(): bool
    {
        return !isset($this->user['__class_name']);
    }

    public function resolveUser()
    {
        if (!empty($this->user)) {
            return $this->user;
        }

        $user = app(UserRepositoryInterface::class)->searchOnElasticsearch(
            null,
            array_filter([
                'ids' => $this->visitor['id'] ?? null,
                'uuid' => $this->visitor['uuid'] ?? null,
                'email' => $this->visitor['email'] ?? null,
            ])
        );

        $user = $user['hits']['hits'][0]['_source'] ?? [];

        return array_merge($user, $this->visitor);
    }

    public function allApplyForJobs()
    {
        $jobs = app(JobRepositoryInterface::class)
                ->searchOnElasticsearch(null, [
                    'ids' => $this->resolveAllApplyId()->implode(','),
                ]);

        return $this->toJobArray($jobs, [
            'utm_source' => 'rec-job',
            'utm_medium' => 'email',
            'utm_campaign' => 'automation',
        ]);
    }

    public function recentlyApplyForJobs()
    {
        $jobs = app(JobRepositoryInterface::class)
                ->searchOnElasticsearch(null, [
                    'ids' => $this->resolveRecentlyApplyId()->implode(','),
                ]);

        return $this->toJobArray($jobs, [
            'utm_source' => 'rec-job',
            'utm_medium' => 'email',
            'utm_campaign' => 'automation',
        ]);
    }

    public function getAreas(): Collection
    {
        return collect($this->user['addresses']['address_region_ids'] ?? []);
    }

    public function getSkills(): Collection
    {
        return collect($this->user['skills_id'] ?? []);
    }

    public function getBestSkills(): Collection
    {
        return collect($this->user['skills'] ?? []);
    }

    public function getRankSkills(): Collection
    {
        return collect($this->user['expect_skills'] ?? []);
    }

    public function getFilesCV(): Collection
    {
        return collect($this->user['files_cv'] ?? []);
    }

    public function getFilesCVBuilder(): Collection
    {
        return collect($this->user['files_cvbuilder'] ?? []);
    }

    public function getProfile()
    {
        return [
            'uuid' => $this->user['uuid'] ?? null,
            'email' => $this->user['email'] ?? null,
            'phone' => $this->user['phone'] ?? null,
            'skills' => $this->user['skills'] ?? [],
            'addresses' => $this->user['addresses'] ?? [],
            'username' => $this->user['username'] ?? null,
            'display_name' => $this->user['display_name'] ?? null,
            'sort_name' => $this->user['sort_name'] ?? null,
            'full_name' => $this->user['full_name'] ?? null,
            'firstname' => $this->user['username'] ?? null,
            'source' => $this->user['source'] ?? null,
            'willing_to_work' => $this->user['willing_to_work'] ?? null,
            'files_cv' => $this->user['files_cv'] ?? [],
            'files_cvbuilder' => $this->user['files_cvbuilder'] ?? [],
            'followed_jobs' => $this->user['followed_jobs'] ?? [],
        ];
    }

    public function expectAreas(): Collection
    {
        return $this->recentlyApplyForAreas()->whenEmpty(fn($areas) => $areas->merge($this->getAreas()));
    }

    public function expectSkills($rank = 1): Collection
    {
        $index = 1;
        $skills = $this->recentlyApplyForSkills()->whenEmpty(fn($skills) => $skills->merge($this->getSkills())->whenEmpty(fn($skills) => $skills->merge($this->getRankSkills())));

        foreach ($this->getRankSkills()->all() as $key => $skill) {
            if (in_array($skill, $skills->all()) && $index++ == $rank) {
                return collect([$skill]);
            }
        }

        return $skills;
    }

    public function recentlyApplyForSkills(): Collection
    {
        $jobs = $this->recentlyApplyForJobs();

        return collect($jobs)->map(fn($job) => $job['skills_ids'])
            ->collapse()->unique();
    }

    public function recentlyApplyForAreas(): Collection
    {
        $jobs = $this->recentlyApplyForJobs();

        return collect($jobs)->map(fn($job) => $job['addresses']['address_region_ids'] ?? [])
            ->collapse()->unique();
    }

    public function resolveAllApplyId(): Collection
    {
        return $this->resolveRecentlyApplyId()->merge(
            $this->user['applied_jobs'] ?? []
        )->unique();
    }

    public function resolveRecentlyApplyId(): Collection
    {
        if (isset($this->payload['recently_apply'])) {
            return collect(
                array_map(fn($id) => intval($id), array_filter(
                    explode(',', (string) $this->payload['recently_apply'])
                ))
            );
        }

        if (isset($this->payload['force_recently_apply'])) {
            return collect($this->user['applied_jobs'] ?? null)->take(-1)->values();
        }

        return collect();
    }

    private function toJobArray($jobs, $utm = [])
    {
        return (new ElasticCollection($jobs))->all()->map(fn($job) => [
            'id' => $job['id'],
            'title' => $job['title'],
            'salary' => $job['salary'],
            'detail_url' => $job['detail_url'] . '?' . http_build_query($utm),
            'addresses' => $job['addresses'],
            'skills_arr' => $job['skills_arr'],
            'skills_ids' => $job['skills_ids'],
            'skills_str' => $job['skills_str'],
            'company_name' => $job['company']['display_name'] ?? null,
        ])->all();
    }

    public function __get($key)
    {
        if (array_key_exists($key, $this->user)) {
            return $this->user[$key];
        }

        return null;
    }
}
