<?php

namespace Modules\Activity\Services;

use DateTimeInterface;
use Illuminate\Support\Collection;
use <PERSON><PERSON>\Scout\Searchable;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MatchQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;
use ONGR\ElasticsearchDSL\Sort\FieldSort;

class UserCvBuilder
{
    use Searchable;

    private $email;
    private $progress;
    private $resume_id;
    private $download;
    private $updated_at;

    /**
     * Create a new UserCvBuilder instance.
     *
     * @param  array $visitor
     * @return void
     */
    public function __construct($options = [])
    {
        $this->email = $options['email'] ?? null;
        $this->progress = $options['progress_cvbuilder'] ?? $options['progress'] ?? 0;
        $this->resume_id = $options['resume_id'] ?? null;
        $this->download = $options['download'] ?? false;
        $this->updated_at = $options['updated_at'] ?? now();
    }

    /**
     * Create a new UserCvBuilder instance.
     *
     * @return self
     */
    public static function create($options)
    {
        return new static($options);
    }

    /**
     * Create Or First a UserCvBuilder instance.
     *
     * @return self
     */
    public static function firstOrCreate($options)
    {
        if (isset($options['email']) && !isset($options['resume_id'])) {
            $cvbuilder = static::search('*', function ($client, $body) use ($options) {
                $baseQuery = new BoolQuery();

                if (array_key_exists('email', $options)) {
                    $baseQuery->add(new MatchQuery('email', $options['email']), BoolQuery::MUST);
                }

                if (array_key_exists('resume_id', $options)) {
                    $baseQuery->add(new TermQuery('resume_id', $options['resume_id']), BoolQuery::MUST);
                }

                $body->addSort(new FieldSort('updated_at', null, ['order' => FieldSort::DESC]));
                $body->addQuery($baseQuery);

                return $client->search(['index' => 'progress_cvbuilder_ams', 'body' => $body->toArray()])->asArray();
            })->raw()['hits']['hits'][0]['_source'] ?? [];

            if (!empty($cvbuilder)) {
                return static::create(
                    array_merge(
                        $cvbuilder,
                        array_filter([
                            'progress_cvbuilder' => $options['progress_cvbuilder'] ?? null,
                        ])
                    )
                );
            }
        }

        return static::create($options);
    }

    /**
     * Create Or First a UserCvBuilder instance.
     *
     * @return self
     */
    public static function find($options)
    {
        if (isset($options['email']) && isset($options['resume_id'])) {
            $cvbuilder = static::search('*', function ($client, $body) use ($options) {
                $baseQuery = new BoolQuery();

                if (array_key_exists('email', $options)) {
                    $baseQuery->add(new MatchQuery('email', $options['email']), BoolQuery::MUST);
                }

                if (array_key_exists('resume_id', $options)) {
                    $baseQuery->add(new TermQuery('resume_id', $options['resume_id']), BoolQuery::MUST);
                }

                $body->addSort(new FieldSort('updated_at', null, ['order' => FieldSort::DESC]));
                $body->addQuery($baseQuery);

                return $client->search(['index' => 'progress_cvbuilder_ams', 'body' => $body->toArray()])->asArray();
            })->raw()['hits']['hits'][0]['_source'] ?? [];

            if (!empty($cvbuilder)) {
                return static::create(
                    array_merge(
                        $cvbuilder,
                        array_filter([
                            'progress_cvbuilder' => $options['progress_cvbuilder'] ?? null,
                        ])
                    )
                );
            }
        }

        return static::create($options);
    }

    /**
     * Get key model.
     *
     * @return mixed
     */
    public function getKey()
    {
        return $this->resume_id;
    }

    /**
     * Get the value used to index the model.
     *
     * @return mixed
     */
    public function getScoutKey()
    {
        return $this->getKey();
    }

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'progress_cvbuilder_ams';
    }

    /**
     * Determine if the model should be searchable.
     *
     * @return bool
     */
    public function shouldBeSearchable()
    {
        return !empty($this->getScoutKey());
    }

    public function getProgressing()
    {
        return [
            'url' => $this->getFullUrl(),
            'progress' => $this->progress,
        ];
    }

    public function getFullUrl()
    {
        return (empty($this->resume_id) || $this->notStartYet()) ? taocv_url('/user/cv') : taocv_url('my-resume/edit/' . $this->resume_id);
    }

    public function getProgress()
    {
        return $this->progress;
    }

    public function refresh()
    {
        return static::find($this->toArray());
    }

    public function notStartYet(): bool
    {
        return $this->progress == 0;
    }

    public function downloaded(): bool
    {
        return $this->download;
    }

    public function setDownload($download): self
    {
        $this->download = $download;

        return $this;
    }

    public function completed(): bool
    {
        return $this->progress == 100;
    }

    public function getUpdatedAt()
    {
        if ($this->updated_at instanceof DateTimeInterface) {
            return $this->updated_at;
        }

        if (is_string($this->updated_at)) {
            return \Carbon\Carbon::create($this->updated_at);
        }

        return now();
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        return array_merge($this->toArray(), [
            'updated_at' => $this->getUpdatedAt()->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toArray()
    {
        return [
            'email' => $this->email,
            'progress' => $this->progress,
            'download' => $this->download,
            'resume_id' => $this->resume_id,
            'updated_at' => $this->updated_at,
        ];
    }

    /**
     * Create a new Eloquent Collection instance.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function newCollection(array $models = [])
    {
        return new Collection($models);
    }

    /**
     * Determine if the current class should use soft deletes with searching.
     *
     * @return bool
     */
    public static function usesSoftDelete()
    {
        return false;
    }

    /**
     * @return bool
     */
    public static function available()
    {
        $client = resolve(\Elasticsearch\Client::class);

        return $client->indices()->exists(['index' => 'progress_cvbuilder_ams']);
    }

    public static function createWriteIndex()
    {
        $source = (new \Matchish\ScoutElasticSearch\Searchable\DefaultImportSource(static::class));
        $index = \Matchish\ScoutElasticSearch\ElasticSearch\Index::fromSource($source);
        dispatch_sync(new \Matchish\ScoutElasticSearch\Jobs\Stages\CreateWriteIndex($source, $index));
    }
}
