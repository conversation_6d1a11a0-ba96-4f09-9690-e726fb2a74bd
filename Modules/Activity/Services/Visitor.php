<?php

namespace Modules\Activity\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Str;
use Modules\Activity\Contracts\VisitorContract;

class Visitor implements VisitorContract
{
    /**
     * <PERSON><PERSON> stores the DNT header under the "HTTP_DNT" key instead of "DNT".
     *
     * @var string
     */
    public const DNT = 'HTTP_DNT';

    /**
     * The visitor cookie key.
     *
     * @var string
     */
    protected $user;

    /**
     * The request instance.
     *
     * @var Request
     */
    protected $request;

    /**
     * The crawler detector instance.
     *
     * @var string
     */
    protected $visitorCookie;

    /**
     * The visitor cookie key.
     *
     * @var string
     */
    protected $visitorCookieKey = 'topdev_visitor';

    /**
     * Create a new visitor instance.
     *
     * @return void
     */
    public function __construct(Request $request, string $visitorCookie = null, string $user = null)
    {
        $this->user = $user;
        $this->request = $request;
        $this->visitorCookie = $visitorCookie;
    }

    /**
     * Get the unique ID that represent's the visitor.
     *
     * @return string
     */
    public function id(): string
    {
        if (!empty($this->visitorCookie)) {
            return $this->visitorCookie;
        }

        if (!Cookie::has($this->visitorCookieKey)) {
            $uniqueString = $this->generateUniqueCookieValue();

            Cookie::queue($this->visitorCookieKey, $uniqueString, $this->cookieExpirationInMinutes());

            return $uniqueString;
        }

        return Cookie::get($this->visitorCookieKey);
    }

    /**
     * Get the visitor IP address.
     *
     * @return string|null
     */
    public function ip(): string
    {
        return $this->request()->ip();
    }

    /**
     * Determine if the visitor has a "Do Not Track" header.
     *
     * @return bool
     */
    public function hasDoNotTrackHeader(): bool
    {
        return 1 === (int) $this->request()->header(self::DNT);
    }

    /**
     * Determine if the visitor is a crawler.
     *
     * @return bool
     */
    public function isCrawler(): bool
    {
        return $this->crawlerDetector()->isCrawler();
    }

    /**
     * Returns the request instance.
     *
     * @return Request
     */
    protected function request(): Request
    {
        return $this->request;
    }

    /**
     * Returns the crawler detector instance.
     *
     * @return CrawlerDetector;
     */
    protected function crawlerDetector(): CrawlerDetector
    {
        return new CrawlerDetector(
            $this->request()->header(),
            $this->request()->header('User-Agent')
        );
    }

    /**
     * Generate a unique visitor id.
     *
     * @return string
     */
    protected function generateUniqueCookieValue(): string
    {
        return Str::random(80);
    }

    /**
     * Get the expiration in minutes.
     *
     * @return int
     */
    protected function cookieExpirationInMinutes()
    {
        return 2628000; // aka 5 years
    }
}
