<?php

namespace Modules\Activity\Services;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Modules\Activity\Contracts\CampaignContract;
use Modules\Activity\Contracts\ProgressContract;
use Modules\Job\Entities\Job;
use Modules\Job\Repositories\Contracts\JobRepositoryInterface;
use Redmix0901\ElasticResource\ElasticCollection;

class ProgressManager implements ProgressContract
{
    public const AREA_DEFAULT = 79;
    public const SUGGEST_STOP_POINT = 6;
    public const EVENT_SUGGEST_JOBS = 'EventSuggestJobs';
    public const EVENT_UPDATE_AREAS = 'EventUpdateAreas';
    public const EVENT_UPDATE_SKILLS = 'EventUpdateSkills';
    public const EVENT_BOX_NOTIFY = 'EventBoxNotify';
    public const EVENT_CREATE_CVONLINE = 'EventCreateCvOnline';
    public const EVENT_BOX_SEND_EMAIL = 'EventBoxSendEmail';
    public const EVENT_COMPLETED_CV = 'EventCompletedCv';
    public const EVENT_DOWNLOAD_CVBUILDER = 'EventDownloadCvBuilder';

    public const EMAIL_SAVED_JOB = 'EmailSavedJob';
    public const EMAIL_CLICK_APPLY = 'EmailClickApply';
    public const EMAIL_ME_JOB_LIKE_THIS = 'EmailMeJobLikeThis';
    public const EMAIL_CONFIRM_ALL_JOB = 'EmailConfirmAllJob';
    public const EMAIL_CONFIRM_SINGLE_JOB = 'EmailConfirmSingleJob';
    public const WELCOME_NEW_LOGIN = 'WelcomeNewLogin';

    /**
     * @return UserFeed
     */
    private $user;

    /**
     * @return VisitorFeed
     */
    private $visitor = null;

    /**
     * @return UserCvBuilder
     */
    private $cvbuilder = null;

    private $recently_apply = null;

    private $force_event = null;

    private $files_cv = null;

    private $suggest_jobs = [];

    private $user_instance = null;

    private $origin = null;

    private $job_ids = null;

    private $suggest_size = 6;

    /**
     * @return ActivityFeed
     */
    private $activityFeed = null;

    public function __construct(array $payload, array $user = [])
    {
        $this->activityFeed = resolve(ActivityFeed::class);

        $this->user = UserFeed::create($payload, $user);

        $this->visitor = VisitorFeed::create(array_filter([
            'user_id' => $user['id'] ?? null,
            'visitor' => $user['visitor'] ?? null,
        ]));

        $this->cvbuilder = UserCvBuilder::firstOrCreate(
            array_intersect_key(
                array_merge($payload, $user),
                array_flip(['progress_cvbuilder', 'email', 'resume_id', 'download'])
            )
        );

        if ($payload['origin'] ?? null == 'cvbuilder') {
            $this->cvbuilder = UserCvBuilder::find(
                array_intersect_key(
                    array_merge($payload, $user),
                    array_flip(['progress_cvbuilder', 'email', 'resume_id', 'download'])
                )
            );
        }

        $this->job_ids = $payload['job_ids'] ?? null;
        $this->force_event = $payload['force_event'] ?? null;
        $this->recently_apply = $payload['recently_apply'] ?? null;
        $this->origin = $payload['origin'] ?? null;
        $this->files_cv = $payload['files_cv'] ?? null;
        $this->suggest_size = $payload['suggest_size'] ?? 6;
    }

    /**
     * @return self
     */
    public static function create($payload, $user = [])
    {
        return new static($payload, $user);
    }

    /**
     * Get suggest job for user.
     *
     * @return array
     */
    public function getSuggestJobs($withutm = false): array
    {
        if (!empty($this->suggest_jobs)) {
            return $withutm ? $this->suggest_jobs_with_utm : $this->suggest_jobs;
        }

        $utm = $withutm ? [
            'utm_source' => 'rec-job',
            'utm_medium' => 'email',
            'utm_campaign' => 'automation',
        ] : [];

        $similarJobs = $this->getSimilarJobsFromElasticsearch();

        return $this->toJobArray($similarJobs, $utm);

        // $similarJobs = $this->getSimilarJobs();

        // // Log for more information
        // $this->logSimilarJob($similarJobs);

        // // Get similar jobs with current job apply
        // return $this->toJobArrayFromEloquentModel($similarJobs, $utm);
    }

    /**
     * @return void
     */
    private function logSimilarJob(\Illuminate\Database\Eloquent\Collection $similarJobs)
    {
        Log::info("Similar jobs for job id {$this->recently_apply}:", [
            'similar_job_ids' => $similarJobs->pluck('id')->toArray(),
            'recently_apply' => $this->recently_apply,
        ]);
    }

    /**
     * @param $similarJobs
     * @return array
     */
    private function toJobArrayFromEloquentModel($similarJobs, array $utm = []): array
    {
        $utmQuery = !empty($utm) ? '?' . http_build_query($utm) : '';

        return $similarJobs
            ->map(fn (Job $job) => [
                'id' => $job->id,
                'title' => $job->title,
                'salary' => $job->job_information->salary->toArray(),
                'detail_url' => $job->detail_url . $utmQuery,
                'addresses' => [
                    'address_region_ids' => $job->address_region_ids,
                    'address_region_list' => $job->address_region_list,
                    'address_region_array' => $job->address_region_array,
                    'full_addresses' => $job->full_addresses,
                    'sort_addresses' => $job->short_addresses,
                    'collection_addresses' => $job->collection_addresses,
                ],
                'skills_arr' => $job->skills_name->all(),
                'skills_str' => $job->skills_name->implode(', '),
                'job_types_str' => $job->job_types_name->implode(', '),
                'company_logo' => optional($job->company->getFirstMedia('image_logo'))->getUrl(),
                'company_name' => optional($job->company)->display_name,
                'published' => $job->published_at ? [
                    'date' => Carbon::createFromFormat('Y-m-d H:i:s', $job->published_at)->format('d-m-Y'),
                    'since' => Carbon::createFromFormat('Y-m-d H:i:s', $job->published_at)->locale('vi_VN')->diffForHumans(),
                ] : [],
            ])
            ->filter()
            ->all();
    }

    /**
     * Get jobs user following.
     *
     * @return array
     */
    public function getFollowedJobs($time = null): array
    {
        $user = $this->getUserInstance();
        $ids = $user->followedJobs($time)->pluck('id')->implode(',');

        $jobs = app(JobRepositoryInterface::class)
                ->searchOnElasticsearch(
                    null,
                    array_filter([
                        'status' => Job::STATUS_OPEN,
                        'ids' => empty($ids) ? ' ' : $ids,
                    ])
                );

        return $this->toJobArray($jobs, [
            'utm_source' => 'email',
            'utm_medium' => 'automation',
            'utm_campaign'=>'saved_job',
        ]);
    }

    /**
     * Get jobs user click.
     *
     * @return array
     */
    public function getJobsClickApply($time = null): array
    {
        $user = $this->getUserInstance();
        $ids = $user->clickApply($time)->pluck('id')->implode(',');

        $jobs = app(JobRepositoryInterface::class)
                ->searchOnElasticsearch(
                    null,
                    array_filter([
                        'status' => Job::STATUS_OPEN,
                        'ids' => empty($ids) ? ' ' : $ids,
                    ])
                );

        return $this->toJobArray($jobs, [
            'utm_source' => 'email',
            'utm_medium' => 'automation',
            'utm_campaign' => 'clickapplynotsendcv',
        ]);
    }

    /**
     * Get expect location for user.
     * Priority expect location.
     *
     * @return Collection
     */
    public function userExpectAreas(): Collection
    {
        return $this->user->expectAreas()->whenEmpty(fn($collection) => $collection->merge(
            $this->visitor->getSuggestAreas()
        ));
    }

    /**
     * Get expect skills for user.
     * Priority expect skills.
     *
     * @return Collection
     */
    public function userExpectSkills($rank = 1): Collection
    {
        return $this->user->expectSkills($rank)->whenEmpty(fn($collection) => $collection->merge(
            $this->visitor->getSuggestSkills()
        ));
    }

    /**
     * Expect event for user is current.
     *
     * @return array
     */
    public function expectEvents($events = []): array
    {
        if ($this->user->userAnonymous()) {
            return $events;
        }

        if ($this->expectEventBoxNotify()) {
            array_push($events, self::EVENT_BOX_NOTIFY);
        }

        if ($this->expectEventBoxSendEmail()) {
            array_push($events, self::EVENT_BOX_SEND_EMAIL);
        }

        if ($this->expectEventUpdateAreas()) {
            array_push($events, self::EVENT_UPDATE_AREAS);

            return $events;
        }

        if ($this->expectEventUpdateSkills()) {
            array_push($events, self::EVENT_UPDATE_SKILLS);

            return $events;
        }

        if ($this->expectEventCreateCvOnline()) {
            array_push($events, self::EVENT_CREATE_CVONLINE);

            return $events;
        }

        if ($this->expectEventSuggestJobs()) {
            array_push($events, self::EVENT_SUGGEST_JOBS);

            return $events;
        }

        return $events;
    }

    /**
     * Check current user has receive event EventUpdateAreas.
     *
     * @return bool
     */
    public function expectEventUpdateAreas(): bool
    {
        return $this->user->getAreas()->isEmpty() && $this->forceEvent(self::EVENT_UPDATE_AREAS);
    }

    /**
     * Check current user has receive event EventUpdateSkills.
     *
     * @return bool
     */
    public function expectEventUpdateSkills(): bool
    {
        return $this->user->getSkills()->isEmpty() && $this->forceEvent(self::EVENT_UPDATE_SKILLS);
    }

    /**
     * Check current user has receive event EventCreateCvOnline.
     *
     * @return bool
     */
    public function expectEventCreateCvOnline(): bool
    {
        return $this->user->getFilesCV()->isEmpty()
            && $this->user->getFilesCVBuilder()->isEmpty()
            && $this->forceEvent(self::EVENT_CREATE_CVONLINE);
    }

    /**
     * Check current user has receive event EventSuggestJobs.
     *
     * @return bool
     */
    public function expectEventSuggestJobs(): bool
    {
        $jobs = $this->getSuggestJobs();
        $applied = $this->user->resolveAllApplyId()->all();

        $ids = collect($jobs)->map(fn($job) => $job['id'])->all();

        if (count(array_diff($ids, $applied)) == 0) {
            return false;
        }

        $recentlyApply = !empty($jobs) && !empty($this->recently_apply);
        $forceEvent = $this->force_event == self::EVENT_SUGGEST_JOBS;
        $downloadedCv = $this->cvbuilder->downloaded() && $this->origin == 'cvbuilder';
        $notForceOtherEvent = $this->forceEvent(self::EVENT_SUGGEST_JOBS);

        return ($recentlyApply || $forceEvent || $downloadedCv) && $notForceOtherEvent;
    }

    /**
     * Check current user has receive event BoxSendEmail.
     *
     * @return bool
     */
    public function expectEventBoxSendEmail(): bool
    {
        return false;
    }

    /**
     * Check current user has receive event update lacation.
     *
     * @return bool
     */
    public function expectEventBoxNotify(): bool
    {
        return $this->cvbuilder->completed();
    }

    /**
     * Check current user has receive event update lacation.
     *
     * @return mixed
     */
    public function getUser($key = null)
    {
        if (empty($key)) {
            return $this->user;
        }

        return $this->user->{$key};
    }

    public function getVisitor()
    {
        return $this->visitor;
    }

    /**
     * Get public profile user.
     *
     * @return array
     */
    public function getProfile(): array
    {
        return $this->user->getProfile();
    }

    /**
     * Get skills name of user.
     *
     * @return Collection
     */
    public function getBestSkills(): Collection
    {
        return $this->user->getBestSkills();
    }

    /**
     * Get suggest location from search of user.
     *
     * @return Collection
     */
    public function getSuggestAreas($limit = 1): Collection
    {
        return $this->visitor->getSuggestAreas($limit);
    }

    /**
     * Get suggest skills from search of user.
     *
     * @return Collection
     */
    public function getSuggestSkills($limit = 1)
    {
        $result = $this->visitor->getSuggestSkills($limit);
        if (count($result->all()) < 1) {
            $result = $this->activityFeed->topKeyword($limit)->pluck('id');
        }

        // return $this->visitor->getSuggestSkills($limit);
        return $result;
    }

    public function getRecentlyApply()
    {
        $jobs = $this->user->recentlyApplyForJobs();

        return last($jobs);
    }

    public function getProgressing()
    {
        return $this->cvbuilder->getProgressing();
    }

    public function getProgressCvbuilder()
    {
        return $this->cvbuilder->getProgress();
    }

    /**
     * Get info from cvbuidler.
     *
     * @return UserCvBuilder
     */
    public function getCvbuilder(): UserCvBuilder
    {
        return $this->cvbuilder;
    }

    /**
     * Get user's files cv.
     *
     * @return array
     */
    public function getFilesCv()
    {
        return (string) $this->files_cv;
    }

    /**
     * Get user instance.
     *
     * @return Model
     */
    public function getUserInstance()
    {
        if (!empty($this->user_instance)) {
            return $this->user_instance;
        }

        $userId = $this->user->id;
        $model = config('auth.providers.users.model');
        $this->user_instance = app($model)->find($userId ?? 0);

        return $this->user_instance;
    }

    /**
     * Get the campaign.
     *
     * @return mixed
     */
    public function campaign(): CampaignContract
    {
        if ($this->force_event == 'JobRecommend') {
            if ($this->user->getFilesCVBuilder()->isNotEmpty()) {
                $event = 'JobRecommendCvCompleted33';
            } elseif ($this->user->resolveAllApplyId()->isNotEmpty()) {
                $event = 'JobRecommendApplyAll3030';
            } else {
                $event = 'JobRecommendLogin33';
            }
        }

        return app(CampaignManager::class)
                ->provider($event ?? $this->force_event)
                ->setProgress($this);
    }

    /**
     * Check request has force event.
     *
     * @return bool
     */
    private function forceEvent($event): bool
    {
        return empty($this->force_event) || $this->force_event == $event;
    }

    /**
     * Transform suggest jobs.
     *
     * @return array
     */
    private function toJobArray($jobs, $utm = [])
    {
        return (new ElasticCollection($jobs))->all()->map(fn($job) => [
            'id' => $job['id'],
            'title' => $job['title'],
            'salary' => $job['salary'],
            'detail_url' => $job['detail_url'] . '?' . http_build_query($utm),
            'addresses' => $job['addresses'],
            'skills_arr' => $job['skills_arr'],
            'skills_str' => $job['skills_str'],
            'company_logo' => $job['company']['image_logo'] ?? null,
            'company_name' => $job['company']['display_name'] ?? null,
            'published' => isset($job['published_at']) ? [
                'date' => Carbon::createFromFormat('Y-m-d H:i:s', $job['published_at'])->format('d-m-Y'),
                'since' => Carbon::createFromFormat('Y-m-d H:i:s', $job['published_at'])->locale('vi_VN')->diffForHumans(),
            ] : [],
            'refreshed' => isset($job['refreshed_at']) ? [
                'date' => Carbon::createFromFormat('Y-m-d H:i:s', $job['refreshed_at'])->format('d-m-Y'),
                'since' => Carbon::createFromFormat('Y-m-d H:i:s', $job['refreshed_at'])->locale('vi_VN')->diffForHumans(),
            ] : [],
        ])->all();
    }

    /**
     * Get the similar jobs of recent apply.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getSimilarJobs(): \Illuminate\Database\Eloquent\Collection
    {
        return Job::find($this->recently_apply)
            ->similar_jobs()
            ->with(['job_information', 'taxonomies.term.translations', 'company', 'addresses', 'company.media'])
            ->where('status', Job::STATUS_OPEN)
            ->whereNull('deleted_at')
            ->limit($this->suggest_size)
            ->get();
    }

    /**
     * Get the similar jobs of recent apply from ES.
     *
     * @return array
     */
    public function getSimilarJobsFromElasticsearch()
    {
        $idsSimilar = Job::find($this->recently_apply)->similar_jobs()
            ->select('jobs.id')
            ->whereNull('deleted_at')
            ->where('status', Job::STATUS_OPEN)
            ->whereDoesntHave('candidates', function ($query) {
                $query->where('resume_id', auth('api')->user()->id ?? NULL);
            })
            ->get()
            ->sortByDesc('pivot.confidence')
            ->pluck('id')
            ->toArray();

        if (empty($idsSimilar)) {
            return [];
        }

        $jobs = app(JobRepositoryInterface::class)
            ->searchOnElasticsearch(
                null,
                array_filter([
                    'status' => Job::STATUS_OPEN,
                    'ids' => implode(',', $idsSimilar),
                    'page_size' => 10000,
                    'except_ids' => $this->user->resolveAllApplyId()->implode(','),
                ])
            );

        $jobs['hits']['hits'] = collect(array_replace(array_flip(array_unique($idsSimilar)), collect($jobs['hits']['hits'])->keyBy('_id')->all()))->map(function ($value) {
            if (is_array($value)) {
                return $value;
            }
        })->reject(fn($item) => is_null($item))->take($this->suggest_size)->values()->all();

        return $jobs;
    }
}
