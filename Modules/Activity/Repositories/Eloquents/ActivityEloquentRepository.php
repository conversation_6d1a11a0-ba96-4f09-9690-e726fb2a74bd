<?php

namespace Modules\Activity\Repositories\Eloquents;

use Modules\Activity\Repositories\Contracts\ActivityRepositoryInterface;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\DateHistogramAggregation;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\TermsAggregation;
use ONGR\ElasticsearchDSL\Aggregation\Metric\CardinalityAggregation;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MatchQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MultiMatchQuery;
use ONGR\ElasticsearchDSL\Query\MatchAllQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\IdsQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\RangeQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermsQuery;
use ONGR\ElasticsearchDSL\Sort\FieldSort;
use Redmix0901\Core\Repositories\Eloquent\BaseRepository;

class ActivityEloquentRepository extends BaseRepository implements ActivityRepositoryInterface
{
    private $arrPeriod = [
        'daily' => 'day',
        'monthly' => 'month',
        'yearly' => 'year',
        'weekly' => 'week',
    ];

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = null, $params = [], $fields = '*')
    {
        return $this->querySearch($keyword, $params, $fields)->raw();
    }

    public function searchOnEloquent($keyword = null, $params = [], $fields = '*')
    {
        return $this->querySearch($keyword, $params, $fields)->get();
    }

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearchStatistics($keyword = null, $params = [], $fields = '*')
    {
        return $this->querySearchStatistics($keyword, $params, $fields)->raw();
    }

    /**
     * @inheritdoc
     */
    private function querySearch($keyword, $params, $fields)
    {
        return $this->model->search('*', function ($client, $body) use ($keyword, $params) {
            // Pagination
            $size = $params['page_size'] ?? 10;
            $from = isset($params['page']) ? $size * ($params['page'] - 1) : 0;

            $baseQuery = new BoolQuery();

            /*
             * Tìm kiếm keyword các trường title và tên của employer
             */
            if (!empty($keyword)) {
                $baseQuery->add(
                    new MultiMatchQuery(
                        ['content', '_id'],
                        $keyword,
                        ['operator' => 'or']
                    ),
                    BoolQuery::MUST
                );
            }

            if ($this->hasQuery($params, 'collection')) {
                // $baseQuery->add(new TermsQuery('collection', explode(",", $params['collection'])), BoolQuery::MUST);
                $baseQuery->add(new MatchQuery('collection', $params['collection']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'users')) {
                $baseQuery->add(new TermsQuery('user_id', explode(',', (string) $params['users'])), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'ids')) {
                $baseQuery->add(new IdsQuery(explode(',', (string) $params['ids'])), BoolQuery::MUST);
            }

            if (empty($keyword) && empty($params)) {
                $baseQuery = new MatchAllQuery();
            }

            $body->setSize($size);
            $body->setFrom($from);
            $body->addQuery($baseQuery);
            $body->addAggregation(new TermsAggregation('status', 'status'));
            $body->addSort(new FieldSort('viewed_at', null, ['order' => FieldSort::DESC]));

            return $client->search(['index' => $this->model->searchableAs(), 'body' => $body->toArray()])->asArray();
        })
            ->query(function ($builder) {
                //$builder->with('tags', 'salary', 'company', 'author', 'addresses');
            });
    }

    private function hasQuery($query, $key)
    {
        return (bool) (isset($query[$key]) && !is_null($query[$key]));
    }

    private function querySearchStatistics($keyword, $params, $fields)
    {
        return $this->model->search('*', function ($client, $body) use ($keyword, $params) {
            // Pagination
            $size = $params['page_size'] ?? 10;
            $from = isset($params['page']) ? $size * ($params['page'] - 1) : 0;

            $baseQuery = new BoolQuery();

            //query by period
            if ($this->hasQuery($params, 'viewed_at')) {
                $dateRangeAggregation = new RangeQuery(
                    'viewed_at',
                    [
                        'gte' => $params['viewed_at']['from'],
                        'lte' => $params['viewed_at']['to'],
                        'format' => 'yyyy-MM-dd HH:mm:ss',
                    ]
                );

                $body->addQuery($dateRangeAggregation);
            }

            if ($this->hasQuery($params, 'collection')) {
                $baseQuery->add(new MatchQuery('collection', $params['collection']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'job_status')) {
                $baseQuery->add(new MatchQuery('job_status', $params['job_status']), BoolQuery::MUST);
            }

            $body->setTrackTotalHits(true);

            $body->setSize($size);
            $body->setFrom($from);
            $body->addQuery($baseQuery);

            $dateHist = new DateHistogramAggregation('statis');
            $dateHist->setField('viewed_at');
            if ($this->hasQuery($params, 'period')) {
                if (array_key_exists($params['period'], $this->arrPeriod)) {
                    $dateHist->setInterval($this->arrPeriod[$params['period']]);
                }
            } else {
                $dateHist->setInterval('day');
            }

            if (isset($params['group_user_jobs'])) {
                $cardinalityJobAndUserAggregation = new CardinalityAggregation('count_unique_view_users_jobs');
                $cardinalityJobAndUserAggregation->setScript(
                    "
                    def job_ids = doc['jobs'];
                    def user_id = doc['user_id'];

                    def arr = [];
                    if(job_ids.size() > 0 && user_id.size() > 0){
                        for ( item in  job_ids){
                            arr.add(item + '_' + user_id[0]);
                        }

                        return arr;
                    }


                "
                );
                $cardinalityJobAndUserAggregation->setPrecisionThreshold(40000);
                $dateHist->addAggregation($cardinalityJobAndUserAggregation);
            }

            $body->addAggregation($dateHist);

            $body->addSort(new FieldSort('viewed_at', null, ['order' => FieldSort::ASC]));

            return $client->search(['index' => $this->model->searchableAs(), 'body' => $body->toArray()])->asArray();
        })
            ->query(function ($builder) {
            });
    }
}
