<?php

namespace Modules\Activity\Repositories\Caches;

use Modules\Activity\Repositories\Contracts\ActivityRepositoryInterface;
use Redmix0901\Core\Repositories\Caches\BaseCacheDecorator;
use Redmix0901\Core\Repositories\Interfaces\BaseRepositoryInterface;

class ActivityCacheDecorator extends BaseCacheDecorator implements ActivityRepositoryInterface
{
    public function __construct(BaseRepositoryInterface $base)
    {
        //$this->time = 100;

        // Invoke parent
        parent::__construct($base);
    }

    public function searchOnElasticsearchStatistics($keyword = '*', $params = [], $fields = '*')
    {
    }

    /**
     * @inheritdoc
     */
    public function searchOnEloquent($keyword = null, $params = [], $fields = '*')
    {
        return $this->getWithCache(__FUNCTION__, func_get_args());
    }

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = '*', $params = [], $fields = '*')
    {
        return $this->getWithCache(__FUNCTION__, func_get_args());
    }
}
