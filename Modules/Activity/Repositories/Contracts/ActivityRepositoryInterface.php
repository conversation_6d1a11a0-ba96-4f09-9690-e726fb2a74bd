<?php

namespace Modules\Activity\Repositories\Contracts;

use Redmix0901\Core\Repositories\Interfaces\BaseRepositoryInterface;

interface ActivityRepositoryInterface extends BaseRepositoryInterface
{
    /**
     * @inheritdoc
     */
    public function searchOnEloquent($keyword = null, $params = [], $fields = '*');

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = '*', $params = [], $fields = '*');

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearchStatistics($keyword = '*', $params = [], $fields = '*');
}
