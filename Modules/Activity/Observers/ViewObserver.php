<?php

namespace Modules\Activity\Observers;

use Modules\Activity\Events\ActivityCreated;
use Modules\Activity\Events\SaveViewProcessed;

class ViewObserver
{
    /**
     * Handle the saved event for the model.
     *
     * @param  \Modules\Activity\Entities\Activity $activity
     * @return void
     */
    public function saved($activity)
    {
        if ($activity->isClickApply() && $activity->wasRecentlyCreated) {
            event(new ActivityCreated($activity));
        } elseif ($activity->wasRecentlyCreated) {
            event(new SaveViewProcessed($activity));
        }
    }
}
