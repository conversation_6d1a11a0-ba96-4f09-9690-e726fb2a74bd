<?php

namespace Modules\Activity\EmailMarketing;

use Modules\Activity\Contracts\CampaignContract;

class JobMauticEmailMarketing extends EmailMarketing
{
    /**
     * The Mautic client.
     *
     * @var \Mautic\Api\Contacts
     */
    protected $mautic;

    /**
     * Create a new engine instance.
     *
     * @return void
     */
    public function __construct($mautic)
    {
        $this->mautic = $mautic;
    }

    /**
     * @inheritdoc
     */
    public function send(CampaignContract $campaign): bool
    {
        try {
            $data = $campaign->toArray();
            $source = $this->normalizeSource($data['source'] ?? null);
            $address = last($data['addresses'] ?? []) ?: [];

            $response = $this->mautic->create([
                'title' => $data['email'] . ' - ' . $data['firstname'] ?? null,
                'firstname' => $data['firstname'] ?? null,
                'lastname' => $data['lastname'] ?? null,
                'email' => $data['email'] ?? null,
                'mobile' => $data['phone'] ?? null,
                'tags' => array_merge($data['skills'] ?? [], [$source]),
                'city' => $address['province']['value'] ?? null,
                'zipcode' => $address['postal_code'] ?? null,
                'country' => 'Vietnam',
                'owner' => 4,
                'address1' => $address['full_address'],
            ]);

            if (array_key_exists('errors', $response)) {
                $campaign->setErrors($response['errors']);
            }

            return array_key_exists('contact', $response);
        } catch (\Exception) {
            return false;
        }
    }

    /**
     * Transform source to mautic.
     *
     * @return string
     */
    private function normalizeSource($source)
    {
        return match ($source) {
            'TOPDEVVN' => 'SRC TOPDEV',
            'SLIMSHARE' => 'SRC SLIMSHARE',
            default => 'SRC TOPDEV',
        };
    }
}
