<?php

namespace Modules\Activity\EmailMarketing;

use Modules\Activity\Contracts\CampaignContract;

class AutoMauticEmailMarketing extends EmailMarketing
{
    /**
     * The Mautic client.
     *
     * @var \Mautic\Api\Contacts
     */
    protected $mautic;

    /**
     * Create a new engine instance.
     *
     * @return void
     */
    public function __construct($mautic)
    {
        $this->mautic = $mautic;
    }

    /**
     * @inheritdoc
     */
    public function send(CampaignContract $campaign): bool
    {
        try {
            $response = $this->mautic->create($campaign->toArray());

            if (array_key_exists('errors', $response)) {
                $campaign->setErrors($response['errors']);
            }

            return array_key_exists('contact', $response);
        } catch (\Exception) {
            return false;
        }
    }
}
