<?php

namespace Modules\Activity\Providers;

use Illuminate\Database\Eloquent\Factory;
use Illuminate\Support\ServiceProvider;
use Modules\Activity\Contracts\VisitorContract;
use Modules\Activity\Entities\Activity;
use Modules\Activity\Http\Middleware\PerformAutomationMiddleware;
use Modules\Activity\Observers\ViewObserver;
use Modules\Activity\Repositories\Contracts\ActivityRepositoryInterface;
use Modules\Activity\Repositories\Eloquents\ActivityEloquentRepository;
use Modules\Activity\Services\Visitor;

class ActivityServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        Activity::observe(ViewObserver::class);
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->registerFactories();
        $this->loadMigrationsFrom(module_path('Activity', 'Database/Migrations'));
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);
        $this->app->bind(VisitorContract::class, Visitor::class);
        $this->app['router']->aliasMiddleware('perform-automation', PerformAutomationMiddleware::class);

        $this->commands([
            \Modules\Activity\Console\TrackActivityAnonymous::class,
        ]);

        $this->app->singleton(ActivityRepositoryInterface::class, fn() => new ActivityEloquentRepository(
            new Activity
        ));
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            module_path('Activity', 'Config/config.php') => config_path('activity.php'),
        ], 'config');
        $this->mergeConfigFrom(
            module_path('Activity', 'Config/config.php'),
            'activity'
        );
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews()
    {
        $viewPath = resource_path('views/modules/activity');

        $sourcePath = module_path('Activity', 'Resources/views');

        $this->publishes([
            $sourcePath => $viewPath,
        ], 'views');

        $this->loadViewsFrom(array_merge(array_map(fn($path) => $path . '/modules/activity', \Config::get('view.paths')), [$sourcePath]), 'activity');
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/activity');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'activity');
        } else {
            $this->loadTranslationsFrom(module_path('Activity', 'Resources/lang'), 'activity');
        }
    }

    /**
     * Register an additional directory of factories.
     *
     * @return void
     */
    public function registerFactories()
    {
        if (!app()->environment('production') && $this->app->runningInConsole()) {
            app(Factory::class)->load(module_path('Activity', 'Database/factories'));
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }
}
