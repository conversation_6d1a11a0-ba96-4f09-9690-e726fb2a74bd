<?php

namespace Modules\Activity\Contracts;

use Illuminate\Database\Eloquent\Relations\MorphToMany;

interface ViewableContract
{
    /**
     * Get the value of the model's primary key.
     *
     * @return mixed
     */
    public function getKey();

    /**
     * Get the class name for polymorphic relations.
     *
     * @return string
     */
    public function getMorphClass();

    /**
     * Get the views the model has.
     *
     * @return MorphToMany
     */
    public function views(): MorphToMany;

    /**
     * @return void
     */
    public function recentlyViewedBy($visitor);
}
