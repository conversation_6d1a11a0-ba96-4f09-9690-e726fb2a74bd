<?php

namespace Modules\Activity\Events;

use Modules\Activity\Contracts\CampaignContract;

class PublishCampaignIncompleted
{
    /**
     * @var Modules\Activity\Contracts\CampaignContract
     */
    private $campaign;

    /**
     * Create a new Event update model instance.
     */
    public function __construct(CampaignContract $campaign)
    {
        $this->campaign = $campaign;
        \Log::info($campaign->getErrors());
        \Log::info('PublishCampaignIncompleted');
    }

    /**
     * Get campaign instance.
     *
     * @return Modules\Activity\Contracts\CampaignContract
     */
    public function campaign(): CampaignContract
    {
        return $this->campaign;
    }
}
