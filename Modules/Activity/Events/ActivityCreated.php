<?php

namespace Modules\Activity\Events;

use App\Services\InJectRequestToEvent\BaseRequestEvent;
use Illuminate\Queue\SerializesModels;
use Modules\Activity\Entities\Activity;

final class ActivityCreated extends BaseRequestEvent
{
    use SerializesModels;

    /**
     * @var Modules\Activity\Entities\Activity
     */
    public $activity;

    /**
     * Create a new Event update model instance.
     *
     * @param Modules\Activity\Entities\Activity $view
     */
    public function __construct(Activity $activity)
    {
        $this->activity = $activity;
        parent::__construct();
    }

    /**
     * Get activity model instance.
     *
     * @return Modules\Activity\Entities\Activity
     */
    public function activity()
    {
        return $this->activity;
    }
}
