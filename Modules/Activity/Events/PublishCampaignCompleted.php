<?php

namespace Modules\Activity\Events;

use Modules\Activity\Contracts\CampaignContract;

class PublishCampaignCompleted
{
    /**
     * @var Modules\Activity\Contracts\CampaignContract
     */
    private $campaign;

    /**
     * Create a new Event update model instance.
     */
    public function __construct(CampaignContract $campaign)
    {
        $this->campaign = $campaign;
        \Log::info(array_diff_key($campaign->toArray(), array_flip(['recjob'])));
        \Log::info('PublishCampaignCompleted');
    }

    /**
     * Get campaign instance.
     *
     * @return Modules\Activity\Contracts\CampaignContract
     */
    public function campaign(): CampaignContract
    {
        return $this->campaign;
    }
}
