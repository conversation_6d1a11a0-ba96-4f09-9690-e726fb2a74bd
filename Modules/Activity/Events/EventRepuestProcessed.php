<?php

namespace Modules\Activity\Events;

use Illuminate\Queue\SerializesModels;
use Modules\User\Entities\User;

final class EventRepuestProcessed
{
    use SerializesModels;

    /**
     * @var Modules\User\Entities\User
     */
    private $user;

    /**
     * Create a new Event update model instance.
     *
     * @param mixed[] $payload
     */
    public function __construct(User $user, private $payload = [])
    {
        $this->user = $user;
    }

    /**
     * Get user instance.
     *
     * @return Modules\User\Entities\User
     */
    public function user()
    {
        return $this->user;
    }

    /**
     * Get payload.
     *
     * @return array
     */
    public function payload()
    {
        return $this->payload;
    }
}
