<?php

namespace Modules\Activity\Events;

use Illuminate\Queue\SerializesModels;
use Modules\Activity\Entities\Activity;

final class SaveViewProcessed
{
    use SerializesModels;

    /**
     * @var Modules\Activity\Entities\Activity
     */
    private $activity;

    /**
     * Create a new Event update model instance.
     *
     * @param Modules\Activity\Entities\Activity $view
     */
    public function __construct(Activity $activity)
    {
        $this->activity = $activity;
    }

    /**
     * Get activity model instance.
     *
     * @return Modules\Activity\Entities\Activity
     */
    public function activity()
    {
        return $this->activity;
    }
}
