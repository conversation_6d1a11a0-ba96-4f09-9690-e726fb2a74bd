<?php

namespace Modules\Activity\Console;

use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Modules\Activity\Services\ActivityManager;

class TrackActivityAnonymous extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $signature = 'activities:tracking-anonymous 
                        {user? : The ID of the user}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $activityModelInstance = ActivityManager::getActivityModelInstance();

        $user = \Modules\User\Entities\User::find(
            $this->argument('user')
        );

        $this->info('Getting all activity anonymous, it took a while');

        $views = $activityModelInstance::whereNull('user_id')
                    ->with('trackingUser')
                    ->whereHas('trackingUser', function (Builder $query) use ($user) {
                        is_a($user, 'Modules\User\Entities\User')
                            ? $query->whereUserId($user->getKey()) : $query;
                    })
                    ->limit(3)
                    ->orderByRaw('id DESC')
                    ->get();

        foreach ($views as $key => $value) {
            $activityHasUser = $activityModelInstance::where('visitor', $value->visitor)->whereNotNull('user_id')->first();

            if ($activityHasUser) {
                $value->update([
                    'user_id' => $activityHasUser->user_id,
                ]);

                $this->info('Map activity anonymous from schema index: ' . $key . ' id:' . $value->id);
            }
        }
    }
}
