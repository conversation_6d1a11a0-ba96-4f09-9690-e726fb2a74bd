<?php

return [
    'name' => 'Activity',

    /*
     * This model will be used to log activity.
     * and extend Illuminate\Database\Eloquent\Model.
     */
    'activity_model' => \Modules\Activity\Entities\Activity::class,

    /*
     * If no log name is passed to the activity() helper
     * we use this default log name.
     */
    'default_log_name' => 'default',

    /*
     * These generators will be used to create an image of media files.
     */
    'prefix_viewable' => [
        'jobs' => Modules\Job\Entities\Job::class,
        'companies' => Modules\Company\Entities\Company::class,
        'skills' => Modules\Taxonomy\Entities\Taxonomy::class,
    ],

    'email_marketing' => 'AutoMautic',

    'auto_mautic' => [
        'userName' => 'tu',
        'password' => '123312',
        'baseUrl' => 'https://job.topdev.vn/',
    ],

    'job_mautic' => [
        'userName' => env('MAUTIC_USERNAME'),
        'password' => env('MAUTIC_PASSWORD'),
        'baseUrl' => env('MAUTIC_BASEURL'),
    ],
];
