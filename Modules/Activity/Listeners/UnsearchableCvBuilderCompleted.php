<?php

namespace Modules\Activity\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Modules\Activity\Events\EventRepuestProcessed;
use Modules\Activity\Services\ProgressManager;

class UnsearchableCvBuilderCompleted implements ShouldQueue
{
    /**
     * The time (seconds) before the job should be processed.
     *
     * @var int
     */
    public $delay = 2;

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle(EventRepuestProcessed $event)
    {
        $user = $event->user();
        $payload = $event->payload();

        $progress = ProgressManager::create(
            $payload,
            $user->only('id', 'email', 'uuid')
        );

        if ($progress->getCvbuilder()->downloaded()
            && in_array(ProgressManager::EVENT_SUGGEST_JOBS, $progress->expectEvents())) {

            \Log::info('unsearchable');
            \Log::info($progress->getCvbuilder()->toArray());

            $progress->getCvbuilder()->setDownload(false);
            $progress->getCvbuilder()->searchable();
        }
    }

    /**
     * Determine whether the listener should be queued.
     *
     * @return bool
     */
    public function shouldQueue(EventRepuestProcessed $event)
    {
        return true;
    }
}
