<?php

namespace Modules\Activity\Listeners;

use Modules\Activity\Contracts\ViewableContract;
use Modules\Activity\Events\SaveViewProcessed;

class VistorOnViewable
{
    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle(SaveViewProcessed $event)
    {
        $model = $event->activity();
        $visitor = $model->user;
        $viewable = $model->viewable;

        if ($viewable instanceof ViewableContract) {
            $viewable->recentlyViewedBy($visitor);
        }

        if ($visitor->exists) {
            $visitor->activityTracking($viewable);
        }
    }
}
