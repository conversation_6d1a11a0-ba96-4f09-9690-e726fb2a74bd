<?php

namespace Modules\Activity\Listeners;

use App\Helpers\QueueHeplers;

class PublishActivityEvent
{
    public function sendRabbitmq($event)
    {
        app(QueueHeplers::class)->createQueueRabbitmq($event);
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @param  Illuminate\Events\Dispatcher  $events
     * @return array
     */
    public function subscribe($events)
    {
        $events->listen(
            \Modules\Activity\Events\EventRepuestProcessed::class,
            'Modules\Activity\Listeners\PublishActivityEvent@sendRabbitmq'
        );

        $events->listen(
            \Modules\Activity\Events\ActivityCreated::class,
            'Modules\Activity\Listeners\PublishActivityEvent@sendRabbitmq'
        );

        $events->listen(
            \Modules\Activity\Events\PublishCampaignCompleted::class,
            'Modules\Activity\Listeners\PublishActivityEvent@sendRabbitmq'
        );

        $events->listen(
            \Modules\Activity\Events\PublishCampaignIncompleted::class,
            'Modules\Activity\Listeners\PublishActivityEvent@sendRabbitmq'
        );

        $events->listen(
            \Modules\Activity\Events\PublishCampaignStarting::class,
            'Modules\Activity\Listeners\PublishActivityEvent@sendRabbitmq'
        );

        $events->listen(
            \Modules\Activity\Events\SaveViewProcessed::class,
            'Modules\Activity\Listeners\PublishActivityEvent@sendRabbitmq'
        );
    }
}
