<?php

namespace Modules\Activity\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Modules\Activity\Campaigns\CongratulationCompleteCV;
use Modules\Activity\Campaigns\EventDownloadCvBuilder;
use Modules\Activity\Campaigns\PushCompleteCV;
use Modules\Activity\Events\PublishCampaignStarting;

class SyncProgressCvBuilderToElasticsearch implements ShouldQueue
{
    /**
     * The time (seconds) before the job should be processed.
     *
     * @var int
     */
    public $delay = 2;

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle(PublishCampaignStarting $event)
    {
        $progress = $event->campaign()->getProgress();

        if ($progress->getCvbuilder()->shouldBeSearchable()) {
            $progress->getCvbuilder()->searchable();
        }
    }

    /**
     * Determine whether the listener should be queued.
     *
     * @return bool
     */
    public function shouldQueue(PublishCampaignStarting $event)
    {
        return $event->campaign() instanceof PushCompleteCV
                || $event->campaign() instanceof CongratulationCompleteCV
                || $event->campaign() instanceof EventDownloadCvBuilder;
    }
}
