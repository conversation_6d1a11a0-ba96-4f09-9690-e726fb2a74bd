<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::middleware('auth:api')->get('/activity', function (Request $request) {
//     return $request->user();
// });

/*
|--------------------------------------------------------------------------
| API progresses
|--------------------------------------------------------------------------
*/
Route::prefix('progresses/')->group(function () {
    Route::middleware('auth:api')->group(function () {

    });

    Route::get('/', 'ProgressController@events')->middleware('perform-automation');
    Route::post('/', 'ProgressController@webhook');
    Route::get('/campaigns', 'ProgressController@expectCampaigns');
});

Route::prefix('activities/')->group(function () {
    Route::post('/tracking', 'ProgressController@tracking')->middleware('ensure_frontend_requests_are_stateful');

    Route::middleware('auth:api')->group(function () {
        Route::get('/reports', 'ApiActivityController@reports')->middleware('permission:administrator');
    });
});
