<?php

namespace Modules\Announcement\Repositories\Eloquents;

use Modules\Announcement\Repositories\Contracts\AnnouncementInterface;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\TermsAggregation;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MultiMatchQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\IdsQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermsQuery;
use ONGR\ElasticsearchDSL\Sort\FieldSort;
use Redmix0901\Core\Repositories\Eloquent\BaseRepository;

class AnnouncementRepository extends BaseRepository implements AnnouncementInterface
{
    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = null, $params = [], $fields = '*')
    {
        \Log::info('Search announcement form Elasticsearch');

        return $this->querySearch($keyword, $params, $fields)->raw();
    }

    public function searchOnEloquent($keyword = null, $params = [], $fields = '*')
    {
        if (isset($params['page_size'])) {
            return $this->querySearch($keyword, $params, $fields)->paginate($params['page_size']);
        }

        return $this->querySearch($keyword, $params, $fields)->get();
    }

    /**
     * @inheritdoc
     */
    private function querySearch($keyword, $params, $fields)
    {
        return $this->model->search('*', function ($client, $body) use ($keyword, $params) {
            // Pagination
            $size = $params['page_size'] ?? 10;
            $from = isset($params['page']) ? $size * ($params['page'] - 1) : 0;

            $baseQuery = new BoolQuery();

            /*
             * Tìm kiếm keyword các trường post_content, post_title, post_name
             */
            if (!empty($keyword)) {
                $baseQuery->add(new MultiMatchQuery(['id'], $keyword, ['operator' => 'or']), BoolQuery::MUST);
            }

            /*
             * Lọc các post có id sau
             */
            if (array_key_exists('ids', $params)) {
                $baseQuery->add(new IdsQuery(empty($params['ids']) ? [] : explode(',', (string) $params['ids'])), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'model_type')) {
                $baseQuery->add(new TermQuery('model_type', $params['model_type']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'model_id')) {
                $baseQuery->add(new TermQuery('model_id', $params['model_id']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'model_ids')) {
                $baseQuery->add(new TermsQuery('model_id', explode(',', (string) $params['model_ids'])), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'type')) {
                $baseQuery->add(new TermQuery('type', $params['type']), BoolQuery::MUST);
            }

            $body->setSize($size);
            $body->setFrom($from);
            $body->addQuery($baseQuery);
            $body->addAggregation(new TermsAggregation('status', 'status'));

            $body->addSort(new FieldSort('created_at', null, ['order' => FieldSort::DESC]));

            return $client->search(['index' => $this->model->searchableAs(), 'body' => $body->toArray()])->asArray();
        })
        ->query(function ($builder) {
        });
    }

    /**
     * Check if has query.
     */
    private function hasQuery($query, $key)
    {
        return (bool) (isset($query[$key]) && !is_null($query[$key]));
    }
}
