<?php

namespace Modules\Announcement\Entities;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use <PERSON><PERSON>\Scout\Searchable;

class Announcement extends Model
{
    use Searchable;
    use SoftDeletes;

    /**
     * @inheritdoc
     */
    protected $table = 'announcements';

    /**
     * @inheritdoc
     */
    protected $primaryKey = 'id';

    /**
     * @inheritdoc
     */
    protected $fillable = [
        'id',
        'model_id',
        'model_type',
        'title',
        'body',
        'type',
        'expires_at',
    ];

    protected $casts = ['expires_at' => 'datetime', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    /**
     * Create a new Eloquent model instance.
     *
     * @param array $attributes
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    /**
     * @inheritdoc
     */
    protected static function boot()
    {
        parent::boot();
    }

    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Determine if the model should be searchable.
     *
     * @return bool
     */
    public function shouldBeSearchable()
    {
        return $this->deleted_at == null;
    }

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'announcements_ams_v1';
    }

    // public function scopeActive(Builder $builder)
    // {
    //     return $builder->whereNull('expires_at')
    //                 ->orWhereNotNull('expires_at')
    //                 ->where('expires_at', '>', now());
    // }

    public function scopeNonActive(Builder $builder)
    {
        return $builder->whereNotNull('expires_at')
                    ->where('expires_at', '<', now());
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        return [
            'id' => $this->id,
            'model_id' => $this->model_id,
            'model_type' => $this->model_type,
            'title' => $this->name,
            'body' => $this->description,
            'type' => $this->type,
            'expires_at' => empty($this->expires_at) ? null : $this->expires_at->format('Y-m-d H:i:s'),
            'created_at' => empty($this->created_at) ? null : $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => empty($this->updated_at) ? null : $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
