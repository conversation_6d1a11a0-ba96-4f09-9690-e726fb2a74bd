<?php

namespace Modules\Announcement\Providers;

use Illuminate\Database\Eloquent\Factory;
use Illuminate\Support\ServiceProvider;
use Modules\Announcement\Entities\Announcement;
use Modules\Announcement\Repositories\Contracts\AnnouncementInterface;
use Modules\Announcement\Repositories\Eloquents\AnnouncementRepository;

class AnnouncementServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->registerFactories();
        $this->loadMigrationsFrom(module_path('Announcement', 'Database/Migrations'));
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);

        $this->app->singleton(AnnouncementInterface::class, fn() => new AnnouncementRepository(new Announcement));
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            module_path('Announcement', 'Config/config.php') => config_path('announcement.php'),
        ], 'config');
        $this->mergeConfigFrom(
            module_path('Announcement', 'Config/config.php'),
            'announcement'
        );
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews()
    {
        $viewPath = resource_path('views/modules/announcement');

        $sourcePath = module_path('Announcement', 'Resources/views');

        $this->publishes([
            $sourcePath => $viewPath,
        ], 'views');

        $this->loadViewsFrom(array_merge(array_map(fn($path) => $path . '/modules/announcement', \Config::get('view.paths')), [$sourcePath]), 'announcement');
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/announcement');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'announcement');
        } else {
            $this->loadTranslationsFrom(module_path('Announcement', 'Resources/lang'), 'announcement');
        }
    }

    /**
     * Register an additional directory of factories.
     *
     * @return void
     */
    public function registerFactories()
    {
        if (!app()->environment('production') && $this->app->runningInConsole()) {
            app(Factory::class)->load(module_path('Announcement', 'Database/factories'));
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }
}
