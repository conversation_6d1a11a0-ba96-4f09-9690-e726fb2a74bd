<?php

namespace Modules\Announcement\Transformers;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource as Resource;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class StatusJobAnnouncementResource extends Resource
{
    use ElasticCollectionTrait;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request
     * @return array
     */
    public function toArray($request)
    {
        $fields = isset($request->fields['status_job_announcement'])
                    ? explode(',', (string) $request->fields['status_job_announcement']) : [];

        return [
            'id' => $this->id,
            'title' => $this->when(in_array('title', $fields), !empty($this->expires_at) ? ((strtotime(now()) < strtotime((string) $this->expires_at)) ? (string) $this->title : null) : null),
            'body' => $this->when(in_array('body', $fields), !empty($this->expires_at) ? ((strtotime(now()) < strtotime((string) $this->expires_at)) ? (string) $this->body : null) : null),
            // 'description' => (string) $this->description,
            // 'type' => (string) $this->type,
            // 'created_at' => Carbon::createFromFormat('Y-m-d H:i:s', $this->created_at)->format('d-m-Y'),
            // 'updated_at' => Carbon::createFromFormat('Y-m-d H:i:s', $this->updated_at)->format('d-m-Y'),
        ];
    }
}
