<?php

namespace Modules\Announcement\Transformers;

use Illuminate\Http\Resources\Json\ResourceCollection;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

class StatusJobAnnouncementCollection extends ResourceCollection
{
    use ElasticCollectionTrait;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $result = $this->collection->groupBy('type');
        $data = [];
        foreach ($result as $key => $e) {
            $data[$key] = StatusJobAnnouncementResource::collection($e);
        }

        return $data;
    }

    public function withResponse($request, $response)
    {
        $elasticCollection = static::getElasticCollection();

        $json = array_merge([
            '_type' => 'Announcement',
            'aggregations' => empty($elasticCollection) ? [] : $elasticCollection->aggregations(),
        ], json_decode($response->getContent(), true));

        $response->setContent(json_encode($json));
    }
}
